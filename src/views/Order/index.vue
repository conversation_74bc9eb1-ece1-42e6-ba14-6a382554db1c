<template>
  <div class="box">
    <div class="item-box">
 
      <div class="item" v-for="item in 4" :key="item">
        <div class="title">商品一</div>
        <div class="info">
          <img class="img" src="https://static.medsci.cn/public-image/ms-image/2a8fb480-3566-11ec-8e2f-1389d01aad85_head.jpg" alt="">
          <div class="cls">
            <div class="t">唐伟精品课：脑血管病影像与临床</div>
            <div class="v">
              <div class="original">原价：499元</div>
              <div>折后：119.4元</div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div class="bottom">
      <div class="bottom-left">
        <span class="num">共4件，</span>
        <span>合计：477.6元</span>
      </div>
      <div class="bottom-right" @click="submit">提交订单</div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { Toast } from "vant"

export default ({
  setup() {
    const submit = () => {
      Toast("已开通")
    }
    return {
      submit
    }
  },
})
</script>

<style scoped lang="stylus">
  .box {
    background-image url('https://static.medsci.cn/public-image/ms-image/6fd6f450-3565-11ec-8e2f-1389d01aad85_btn.png')
    height 667px
    background-size 100% 100%
    margin-bottom: 60px
    .item-box {
      padding-top 150px
      .item {
        color #723107
        font-size 13px
        margin 0 27px
        border-bottom 1px dashed #723107
        box-sizing border-box
        padding 12px 0 15px
        &:last-child {
          border-bottom none
        }
        .title {
          font-size 14.5px
          font-weight bold
          margin-bottom: 11px
        }
        .info {
          display: flex
          .img {
            width 52.5px
            height 69px
            min-width 52.5px
            margin-right: 10px
          }
          .cls {
            display: flex
            flex-direction column
            justify-content space-between
            width 100%
            .v {
              display: flex
              justify-content: space-between
              .original {
                color #6A6A6A
                text-decoration line-through
              }
            }
          }
        }
      }
    }

    .bottom {
      position fixed
      left 0
      bottom 0
      display flex
      height 50px
      line-height 50px
      font-size 15px
      &-left {
        width calc(100vw - 155px)
        background-color #FFF5E3
        text-align center
        font-size 15.5px
        color #723107
        font-weight 500
        .num {
          color #8E8E8E
          font-size 13.5px
        }
      }
      &-right {
        width 155px
        background-color #FF5F00
        color #ffffff
        text-align center
        font-size 18.5px
      }
    }
  }
</style>