<template>
  <div class="box">
    <div class="item-box">

      <div class="item" v-for="item in list" :key="item">
        <!-- <div class="title">商品{{index+1}}</div> -->
        <div class="info">
          <img class="img" :src="item.cover" alt="">
          <div class="cls">
            <div class="t">{{item.title}}</div>
            <div class="v">
              <div class="original">原价：{{item.price}}元</div>
              <div>折后：{{item.activityPrice}}元</div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div class="bottom">
      <div class="bottom-left">
        <span class="num">共{{list.length}}件，</span>
        <span>合计：{{activityPrice}}元</span>
      </div>
      <div class="bottom-right" @click="submit">结算</div>
    </div>
  </div>

  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    @select="onSelect"
    cancel-text="取消"
    close-on-click-action
  />
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper" @click.stop>
      <div>
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="">
      </div>
    </div>
  </van-overlay>

</template>

<script>
import { onMounted, reactive, ref, toRefs, computed } from 'vue'
import { Toast } from 'vant'
import request from '@/utils/request'
import Cookies from 'js-cookie'
import { useRoute, useRouter } from 'vue-router'
import { baseUrl } from '@/utils/config'

export default ({
  setup () {
    const route = useRoute()
    const router = useRouter()

    const activityPrice = ref(0) // 总价

    const isBay = ref(false)
    const way = ref('')
    const app_order_id = ref('')
    const MSpaymentStatus = ref('')
    const wxOpenId = ref('')
    const show = ref(false)
    const showImg = ref(false)
    const qrCodeUrlImg = ref('')
    const accessAppId = ref('college')
    const actions = [
      { name: '支付宝支付' },
      { name: '微信支付' }
    ]
    const onSelect = (item) => {
      show.value = false
      MobilePay(item.name == '支付宝支付' ? 'ALI' : 'WX')
    }

    const activeList = computed(() => state.list.filter(i => i.active).map(j => j.id))
    const userInfo = reactive({
      info: null
    })
    const state = reactive({
      list: [],
      diacountList:[]
    })

    onMounted(() => {
      getInit()
      getAuth()
    })

    const getInit = async () => {
      const list = sessionStorage.getItem('ms_class_list')
      // console.log(list,'list');
      activityPrice.value = sessionStorage.getItem('ms_class_price')
      // console.log(activityPrice.value,'list');
      const list2 = JSON.parse(sessionStorage.getItem('ms_class_discount'))
      // console.log(list2,'list2');
      state.diacountList = list2
      state.list = JSON.parse(list)
      state.list.forEach((item,index1)=>{
        state.diacountList.forEach((el,index2)=>{
          if(index1 == index2){
            item.activityPrice = el.activityPrice
          }
        })
      })
      // state.list = state.list.map(i => {
      //   return {
      //     ...i,
      //     newActivityPrice: (activityPrice.value / state.list.length).toFixed(1)
      //   }
      // })
    }

    const getQueryVariable = (variable) => {
      const query = window.location.search.substring(1)
      const vars = query.split('&')
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=')
        if (pair[0] == variable) {
          return pair[1]
        }
      }
      return false
    }

    // 跳转登录
    const Login = () => {
      window.addLoginDom()
    }

    const getAuth = () => {
      const userAgent = navigator.userAgent
      const openId = route.query.openId || getQueryVariable('openId')
      if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
        way.value = 'WX'
        // 微信扫码
        const AppId = 'wx9096048917ec59ab'
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href)
          const RedirectUrl = encodeURIComponent(
              `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          )
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`
        } else {
          wxOpenId.value = openId
        }
      } else if (userAgent != null && userAgent.indexOf('AlipayClient') > -1) {
        way.value = 'ALI'
      } else {
        way.value = 'ALL'
      }
    }

    const submit = async () => {
      const userInfos = Cookies.get('userInfo')
      if (!userInfos) {
          Login()
          return false;
      } else {
        userInfo.info = JSON.parse(userInfos)
      }
      Toast.loading({
        duration: 0,
        message: '提交中...',
        loadingType: 'spinner'
      })
      const res = await request.post('/activity/course/createCourseOrder', {
        ids: activeList.value,
        mobile: userInfo.info ? userInfo.info.mobile : '',
        username: userInfo.info ? userInfo.info.userName : '',
        userId: userInfo.info ? userInfo.info.plaintextUserId : '',
        category: 'new-year-day',
        department: sessionStorage.getItem('ms_class_type'),
      })
      if(res.status == 200) {
        app_order_id.value = res.result.orderNo;
        Toast.clear()
        selectPayWay()
      }else {
        Toast(res.message)
      }
    }

    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == 'ALL') {
          show.value = true
        } else {
          MobilePay(way.value)
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + '/payment/pay/merge_qrcode', {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value
        })
        const { qrCodeUrl } = res.data
        if (qrCodeUrl) {
          showImg.value = true
          qrCodeUrlImg.value = qrCodeUrl
        }

        const inter = setInterval(() => {
          getStatus()
          if (MSpaymentStatus.value == 'PAID') {
            clearInterval(inter)
          }
        }, 3000)
      }
      isBay.value = false
    }

    const getStatus = async () => {
      const res = await request.get(baseUrl + '/payment/pay/query', {
        params: {
          appOrderId: app_order_id.value
        }
      })

      const { paymentStatus } = res.data
      MSpaymentStatus.value = paymentStatus
      if (paymentStatus == 'PAID') {
        Toast('支付成功')
        if(sessionStorage.getItem('ms_class_type') == "breathing"){
          window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyBreath";
        }else if(sessionStorage.getItem('ms_class_type') == "scientific_research"){
          window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyScience";
        }else if(sessionStorage.getItem('ms_class_type') == "cardiovascular"){
          window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxg";
        }else if(sessionStorage.getItem('ms_class_type') == "nerve"){
          window.location.href = "https://static.medsci.cn/active/index.html#/sjNew";
        }
      }
    }

    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + '/payment/pay/build', {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: 'MEDSCI_WEB',
        payType: way.value === 'ALL' ? 'MWEB' : way.value === 'WX' ? 'JSAPI' : 'NATIVE'
      })

      if (res2.code !== 'SUCCESS') {
        Toast(res2.msg)
        return
      }

      const res3 = await request.post(baseUrl + '/payment/pay/order', {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value
      })

      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement('div')
        div.innerHTML = aliH5.html
        document.body.appendChild(div)
        document.forms[0].submit()
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl
      }
      if (wechatH5) {
        window.location.href = wechatH5.h5Url
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          {
            appId: wechatJsapi.appId,
            timeStamp: wechatJsapi.timeStamp,
            nonceStr: wechatJsapi.nonceStr,
            package: wechatJsapi.packageStr,
            signType: wechatJsapi.signType,
            paySign: wechatJsapi.paySign
          },
          function (res) {
            if (res.err_msg == 'get_brand_wcpay_request:ok') {
              Toast.success('支付成功！')
              if(sessionStorage.getItem('ms_class_type') == "breathing"){
                window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyBreath";
              }else if(sessionStorage.getItem('ms_class_type') == "scientific_research"){
                window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyScience";
              }else if(sessionStorage.getItem('ms_class_type') == "cardiovascular"){
                window.location.href = "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxg";
              }else if(sessionStorage.getItem('ms_class_type') == "nerve"){
                window.location.href = "https://static.medsci.cn/active/index.html#/sjNew";
              }
            }
          }
        )
      }
    }

    return {
      ...toRefs(state),
      activityPrice,
      actions,
      show,
      showImg,
      qrCodeUrlImg,

      submit,
      onSelect
    }
  }
})
</script>

<style scoped lang="stylus">
  .box {
    background-image url('https://static.medsci.cn/public-image/ms-image/461e4310-a463-11ee-a5c7-fd0174f02b25_xiangqing.png')
    height 100vh
    background-size 100% 100%
    padding-top: 150px;
    box-sizing: border-box;
    max-width 750Px
    margin 0 auto
    .item-box {
      box-sizing: border-box;
      max-height: calc(100vh - 220px)
      overflow-y: auto;
      .item {
        color #723107
        font-size 13px
        margin 0 27px
        border-bottom 1px dashed rgba(119,50,11,0.05)
        box-sizing border-box
        padding 15px 0 15px
        &:last-child {
          border-bottom none
        }
        .title {
          font-size 14.5px
          font-weight bold
          margin-bottom: 11px
        }
        .info {
          display: flex
          .img {
            object-fit: cover
            width 52.5px
            height 69px
            min-width 52.5px
            margin-right: 10px
            border-radius: 5px
          }
          .cls {
            display: flex
            flex-direction column
            justify-content space-between
            width 100%
            .v {
              display: flex
              justify-content: space-between
              .original {
                color #6A6A6A
                text-decoration line-through
              }
            }
          }
        }
      }
    }

    .bottom {
      max-width: 750Px;
      position fixed
      left 0
      right 0
      bottom 0
      margin  0 auto
      display flex
      height 50px
      line-height 50px
      font-size 15px
      &-left {
        width calc(100% - 155px)
        background-color #FFF5E3
        text-align center
        font-size 16.5px
        color #B51C1C
        font-weight 700
        .num {
          color #8E8E8E
          font-size 13.5px
        }
      }
      &-right {
        width 155px
        background-color #B51C1C
        color #ffffff
        text-align center
        font-size 18.5px
      }
    }
  }

  .wrapper {
    height 100%
    display flex
    justify-content center
    align-items center
    flex-wrap wrap
    .text {
      font-size 18px
      color #ffffff
      width 100%
      text-align center
    }
  }
</style>
