<template>
  <div :class="activeDetail.participateButton ? 'box2': 'box'">
    <img class="image" :src="activeDetail.poster" alt="">
    <!-- 固定在页面下方 -->
    <div class="bottom" v-if="!activeDetail.participateButton" @click="join(activeDetail.status)">
      <img :src="activeDetail.participateButtonPicture" alt="">
    </div>
    <!-- 悬浮在页面下方 -->
    <div class="bottom2" v-else @click="join(activeDetail.status)">
      <img :src="activeDetail.participateButtonPicture" alt="">
    </div>
  </div>
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper">
      <div class="img-wrap">
        <img :src="qrCodeUrlImg" style="width: 100%;height: 100%" alt="">
      </div>
    </div>
  </van-overlay>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import { msBaseUrl, activityBaseUrl } from "@/utils/sanofiConfig"

export default ({
  name: 'sanofi',
  components: {
  },
  setup: () => {
    const route = useRoute()
    const activeId = ref("")
    const activeDetail = reactive({})

    const showImg = ref(false);
    const qrCodeUrlImg = ref('')
    const userInfo = reactive({
      info: {},
    })
    const isParticipate = ref(false)

    onMounted(async () => {
      activeId.value = route.query.id
      // 埋点
      getMaiDian()
      // 获取活动详情
      getActivityDetail()
      // 主站埋点
      if(window.navigator.userAgent.includes('medsci_app')){
        getMedsciMaiDian('medsci_app')
      }else {
        getMedsciMaiDian('medsci_site')
      }
    });
    // 主站埋点
    const getMedsciMaiDian = (sign) => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      const script = document.createElement('script')
      script.id = 'maidian'
      script.src = 'https://img.medsci.cn/web/js/demo/msstatis.min.js'
      const s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(script, s)

      script.onload = function() {
        const appId = 1
        const channel = sign
        const id = userInfos.userId
        const token = userInfos.token.accessToken
        window.MsStatis.init(appId, channel, id, token)
        window.MsStatis.disableEvent = ['pushState', 'popstate']

        // isMaidianLoad = true
        // if(memorize.length) {
        //   memorize.forEach(params => {
        //     window.MsStatis.setStatisRule(params)
        //   })
        // }
      }
    }
    const getActivityDetail = async () => {
      let res = await request.get(activityBaseUrl+`/medsciActivity/getActivityTemplateById/${activeId.value}`)
      if(res.status == 200) {
        Object.assign(activeDetail, res.data)
        document.title = activeDetail.name
        // 用户没登陆去登录
        getUserInfo()
        wxShare({
          title:  activeDetail.shareTitle,
          summary: activeDetail.shareSummary,
          thumb: activeDetail.shareIcon
        })
      }
    }
    // 埋点
    const getMaiDian = async () => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token ? userInfos.token.accessToken : ""
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/insertActivityLog', params)
      if(res.status == 200) {
        console.log('埋点了')
      }
    }
    // 判断用户是否登录
    const getUserInfo = async () => {
      const userInfos = Cookies.get('userInfo');
      if (!userInfos) {
        // &sso_sessionid=c8aa5301313_4389ad3c1cb348d99540ec85d77a193d
        const sso_sessionid = route.query.sso_sessionid || getQueryVariable("sso_sessionid")
        if (sso_sessionid) {
          const res = await request.post('/medsciUser/getLoginUserInfoBySid', { sessionid: sso_sessionid });
          userInfo.info = res.data;
          isLimitComplete()
        } else {
          if(window.location.href.includes('code=') && window.location.href.includes('state=')) {

          } else {
            Login()
          }
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom()
        }
        isLimitComplete()
      }
    }
    // 用户登陆后 是否限制完善信息
    const isLimitComplete = async () => {
      // 是否限制完善信息
      if(activeDetail.usersPerfectInformation) {
        const res = await request.get(msBaseUrl+'/perfectInfo/userInfoStatus?encryptionUserId='+ userInfo.info.userId)
        if(res.data.isCompleteInfo){
          // 用户参加活动状态
          // getUsersParticipateStatus()
        } else {
          addPerfectInfoDom()
        }
      }else {
        // 用户参加活动状态
        // getUsersParticipateStatus()
      }
      
    }
    // 获取用户参加活动状态
    const getUsersParticipateStatus = async () => {
      let userInfos = JSON.parse(Cookies.get('userInfo'))
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token.accessToken
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipateStatus', params)
      if(res.status == 200){
      // 用户参与过  有二维码 弹出二维码 没有二维码 点击按钮已经参与活动
        isParticipate.value = true
        if(res.data && res.data.enterpriseWechatQrCode){
          qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
          showImg.value = true
        } else {
          Toast('您已参与过该活动！')
        }
      }else {
        isParticipate.value = false
        Toast(res.message)
      }
    }
    const join = async (val) => {
       if(val == 0) {
        Toast('该活动未审核通过，请您先审核！')
       } else {
        enterActivity()
        //  if(isParticipate.value){
        //   Toast('您已参与过该活动！')
        //  } else {
        //     let userInfos = JSON.parse(Cookies.get('userInfo'))
        //     let params = {
        //       "activityTemplateId": activeId.value,
        //       "token": userInfos.token.accessToken
        //     }
        //     const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipate', params)
        //     if(res.status == 200 || res.status == 1013){
        //       if(res.data&&res.data.enterpriseWechatQrCode) {
        //         qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
        //         showImg.value = true
        //       } else {
        //         res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
        //       }
        //     } else{
        //       Toast(res.message)
        //     }
        //  }
       }
    }
    // 参加活动
    const enterActivity = async () => {
        let userInfos = JSON.parse(Cookies.get('userInfo'))
        let params = {
          "activityTemplateId": activeId.value,
          "token": userInfos.token.accessToken
        }
        const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipate', params)
        if(res.status == 200 || res.status == 1013){
          if(res.data && res.data.enterpriseWechat) {
            if(res.data.enterpriseWechatQrCode) {
              qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
              showImg.value = true
            } else {
              res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
            }
          } else {
            res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
          }
        } else{
          Toast(res.message)
        }
    }
    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }
    // 跳转登录
    const Login = () => {
      addLoginDom()
    }
    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    }
    return {
      showImg,
      qrCodeUrlImg,
      activeDetail,
      userInfo,
      wxShare,
      Login,
      join
    };
  }
})
</script>
<style scoped lang="stylus">
.van-overlay{
  display: flex
  align-items: center
  justify-content: center
  .wrapper{
    width: 100%
    height:300px
    display: grid
    justify-content: center
    align-content: center
    padding: 0px 20px
    .img-wrap{
      width: 100%;
      height 230px
    }
    
  }
}
  .box {
    position relative
    max-width 10rem
    margin 0 auto
    margin-bottom: env(safe-area-inset-bottom);
    padding-bottom: 1.6rem;
    font-size: 0;
    .image {
      width 100% 
    }
    .bottom {
      height 1.6rem
      position: fixed;
      right: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      text-align: center
      img{
        width: 100%
        max-width: 10rem;
        height: 100%
      }
    }
  }
  .box2{
      position relative
      max-width 10rem
      margin 0 auto
      margin-bottom: env(safe-area-inset-bottom);
      padding-bottom: 3rem;
      font-size: 0;
      .image {
        width 100% 
      }
    .bottom2{
      height 3rem
      max-width: 10rem;
      position: fixed;
      right: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      box-sizing: border-box
      padding: 30px 30px;
      text-align: center
      img{
        width: 100%
        height: 100%
        max-width: 10rem;
        border-radius: 20px
      }
    }
  }
</style>