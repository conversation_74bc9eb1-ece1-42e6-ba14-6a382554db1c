<template>
  <div class="swiper-container swiper1">
    <div class="swiper-wrapper">
      <div class="swiper-slide" :class="{ 'pc-swiper-slide': getters.model == 'pc' }" v-for="item in list" :key="item.id">
        <img :src="item.img_url" alt="" @click="link(item)">
      </div>
    </div>
    <!-- 如果需要分页器 -->
    <div class="swiper-pagination"></div>
  </div>
</template>

<script>
import { reactive, onMounted, toRefs } from 'vue'
import Swiper, {
  Autoplay,
  EffectCoverflow,
  EffectCube,
  Pagination,
  Navigation,
} from "swiper";
import { useStore } from "vuex"
Swiper.use([Autoplay, EffectCoverflow, EffectCube, Pagination, Navigation]);

// swiper-bundle.min.css 决定了小圆点和左右翻页标签，如果不需要可以不引用
import "swiper/swiper-bundle.min.css";

// swiper.less/sass/css 决定了基础的样式
import "swiper/swiper.min.css";

export default ({
  name: "msSwiper",
  components: {},
  setup(props) {
    const { getters } = useStore()
    const state = reactive({
      list: [
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_定金风.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_高永利.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_黄丽红.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_吕明.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_陶博士.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_姚娜.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_张博士.png",
          red_url: ""
        },
        { 
          img_url: "https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_周东东.png",
          red_url: ""
        }
      ]
    })
    onMounted(async () => {
      initSwiper()
    });

    const initSwiper = () => {
      new Swiper(".swiper1", {
        // pagination: {
        //   el: ".swiper-pagination",
        // },
        loop : true,
        slidesPerView: 1.2,
        slidesOffsetBefore : 30,
        autoplay: {
          delay: 3000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
        },
      });
    }

    const link = (item) => {
      const url = item.red_url;
      if (!url) {
        return
      }
      window.location.href = item.red_url
    }

    return {
      ...toRefs(state),
      getters,

      link,
      initSwiper
    }
  },
});
</script>

<style lang="stylus" scoped>
.swiper1 {
  // min-height 200px
  // max-width: 1000px
}
.swiper-slide {
  text-align: center
  // height 200px
  &.pc-swiper-slide {
    // height 578px
  }
  img {
    // width: 100%;
    width 100%
    height 100%
    object-fit: cover
  }
}

.swiper-container{
  --swiper-theme-color: #ff6600;
  --swiper-pagination-color: #ffffff;/* 两种都可以 */
}

.swiper-container-horizontal>.swiper-pagination-bullets, .swiper-pagination-custom, .swiper-pagination-fraction {
  bottom 10px;
  right: 25px;
  width auto;
  left auto
  position absolute
}
</style>