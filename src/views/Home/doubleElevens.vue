<template>
  <div class="box">
    <img
      class="image"
      src="https://static.medsci.cn/public-image/ms-image/3faa0a70-7478-11ee-beaa-d366b7f78c29_bcg.png"
      alt=""
    />
    <div class="content">
      <div class="mobile_box">
        <input class="inputMobile" placeholder="请填写手机号" />
      </div>
      <div class="content_box">
        <img
          class="summitBtn"
          @click="applyBtn"
          style="width: 5.2rem"
          src="https://static.medsci.cn/public-image/ms-image/a404f7b0-73c3-11ee-9deb-cbc105e8d30c_btn.png"
          alt=""
        />
      </div>
    </div>
    <div class="last">
      <div class="bottom">
        <img
          @click="scrollToTop"
          src="https://static.medsci.cn/public-image/ms-image/eb1e9110-73c3-11ee-9deb-cbc105e8d30c_footerBtn.png"
          alt=""
        />
      </div>
    </div>
  </div>

  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    @select="onSelect"
    cancel-text="取消"
    close-on-click-action
  />
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper">
      <div @click.stop="">
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="" />
      </div>
    </div>
  </van-overlay>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from "vue";
import request from "@/utils/request";
import Cookies from "js-cookie";
import { useRouter, useRoute } from "vue-router";
import { Toast } from "vant";
// import falseData from '@/components/falseData.vue';
import msSwiper from "./msSwiper.vue";
import { baseUrlKap, baseUrlActiveKap } from "@/utils/configkap";
import { baseUrl } from "@/utils/config";
import { baseUrlSix } from "@/utils/config618";

export default {
  name: "doubleEleven",
  components: {
    // falseData,
    msSwiper,
  },
  setup: () => {
    const router = useRouter();
    const route = useRoute();

    const loading = ref(false);
    const active = ref(false);

    const isBay = ref(false);
    const way = ref("");
    const app_order_id = ref("");
    const MSpaymentStatus = ref("");
    const wxOpenId = ref("");
    const animationFrame = ref("");
    const show = ref(false);
    const showImg = ref(false);
    const qrCodeUrlImg = ref("");
    const accessAppId = ref("college");
    const isEnd = ref(false);
    const time = ref(
      new Date("2022/11/30 23:59:59").getTime() - new Date().getTime()
    ); // 还剩多少毫秒
    const actions = [{ name: "支付宝支付" }, { name: "微信支付" }];
    const onSelect = (item) => {
      show.value = false;
      MobilePay(item.name == "支付宝支付" ? "ALI" : "WX");
    };

    const userInfo = reactive({
      info: {},
    });
    const state = reactive({
      msg: {},
    });
    const joinStatus = ref(false);

    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    };

    onMounted(async () => {
      // window.addEventListener("scroll", () => {
      //   scrollPosition.value = window.pageYOffset;
      // });
      document.title = "双11，研之有礼";
      wxShare({
        title: "双11，研之有礼",
        summary: "专业导师为您服务，折扣+充值好礼，统统给到您~",
        thumb:
          "https://static.medsci.cn/public-image/ms-image/b351b7d0-73eb-11ee-9deb-cbc105e8d30c_share.png",
      });
      // const userInfos = Cookies.get("userInfo");
      // if (!userInfos) {
      //   // Login()
      // } else {
      //   userInfo.info = JSON.parse(userInfos);
      //   attendStatus();
      // }
      // 埋点
      // request.get(baseUrlSix + "/medsci-activity/visit", {
      //   params: {
      //     user_id: userInfo.info.plaintextUserId,
      //     ciphertext_user_id: userInfo.info.userId,
      //     event_type: "view",
      //     type: "double_eleven",
      //   },
      // });
      // const res = await request.get(baseUrlKap + "/activityNew/d9ca10a")
      // state.msg = res.data;

      // let userAgent = navigator.userAgent;
      // if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
      //   if(route.query.openId || getQueryVariable("openId")) {
      //     buy()
      //   }
      // }
    });
    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl,
        },
      });
      wx.config(res.data);
      wx.error(function (error) {
        console.log(error);
      });
      wx.ready(function () {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    };
    const getAuth = () => {
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf("MicroMessenger") > -1) {
        way.value = "WX";
        //微信扫码
        const AppId = "wx9e48b4288d6af69e";
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href);

          const RedirectUrl = encodeURIComponent(
            `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          );
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
        } else {
          wxOpenId.value = openId;
        }
      } else if (userAgent != null && userAgent.indexOf("AlipayClient") > -1) {
        way.value = "ALI";
      } else {
        way.value = "ALL";
      }
    };
    const applyBtn = async () => {
      const inputMobile = document.querySelector(".inputMobile");
      const phoneReg = /^1[3-9]\d{9}$/;
      const val = inputMobile.value;
      // 验证手机号
      if (val != "") {
        if (phoneReg.test(val)) {
          const res = await request.post(
            baseUrlSix + "/medsci-activity/two-three/double_eleven_two",
            {
              mobile: val,
            }
          );
          if (res.code == 200) {
            Toast("您已报名成功，3个工作日内会有顾问与您联系！");
          } else if (res.code == 205) {
            Toast("您已报名，无需重复提交！");
          } else {
            Toast(res.msg);
          }
        } else {
          Toast("请输入合法的手机号");
        }
      } else {
        Toast("请输入手机号");
      }
    };
    // 获取是否参加活动
    const attendStatus = async () => {
      const res = await request.post(
        baseUrlSix + "/medsci-activity/attend-status",
        {
          mobile: userInfo.info.mobile,
          type: "double_eleven",
        }
      );
      if (res.data && res.data.status == 1) {
        joinStatus.value = true;
      } else {
        joinStatus.value = false;
      }
    };
    // 参加活动
    const joinActivity = async () => {
      console.log(joinStatus.value, "rrr");
      if (isEnd.value) {
        Toast("活动已结束~");
        return false;
      }
      if (!Cookies.get("userInfo")) {
        Login();
        return false;
      }
      if (!joinStatus.value) {
        const res = await request.post(
          baseUrlSix + "/medsci-activity/double_eleven",
          {
            user_id: userInfo.info.plaintextUserId,
            ciphertext_user_id: userInfo.info.userId,
            mobile: userInfo.info.mobile,
            user_name: userInfo.info.userName,
            real_name: userInfo.info.realName,
            email: userInfo.info.email,
          }
        );
        if (res.code == 200) {
          Toast.success("报名成功");
          joinStatus.value = true;
        }
        if (res.code == 205) {
          Toast("您已报名，等待回访");
          joinStatus.value = true;
        }
        if (res.code != 200 && res.code != 205) {
          Toast(res.msg || "服务繁忙，请稍后再试");
        }
      }
    };
    const buy = async () => {
      if (isEnd.value) {
        Toast("活动已结束~");
        return false;
      }
      // getAuth()
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf("MicroMessenger") > -1) {
        way.value = "WX";
        //微信扫码
        const AppId = "wx9e48b4288d6af69e";
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href);

          const RedirectUrl = encodeURIComponent(
            `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          );
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
          return;
        } else {
          wxOpenId.value = openId;
        }
      } else if (userAgent != null && userAgent.indexOf("AlipayClient") > -1) {
        way.value = "ALI";
      } else {
        way.value = "ALL";
      }
      const userInfos = Cookies.get("userInfo");
      Toast.loading({
        duration: 0,
        message: "提交中...",
        loadingType: "spinner",
      });
      if (!userInfos) {
        const sso_sessionid =
          route.query.sso_sessionid || getQueryVariable("sso_sessionid");
        if (sso_sessionid) {
          const res = await request.post("/medsciUser/getLoginUserInfoBySid", {
            sessionid: sso_sessionid,
          });
          userInfo.info = res.data;
          Pay();
        } else {
          Login();
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom();
        }
        Pay();
      }
    };
    // 跳转登录
    const Login = () => {
      addLoginDom();
    };
    const testPlay = (item) => {
      if (item.link) {
        window.location.href = item.link;
      }
    };
    const Pay = async () => {
      const { userId, userName, realName, mobile, email, plaintextUserId } =
        userInfo.info;
      const res = await request.post(
        baseUrlActiveKap + "/openOrder/addActivityOrder",
        {
          activityId: state.msg.id,
          itemNum: 1,
          itemPicPath: "",
          activityName: state.msg.name,
          itemPrice: state.msg.money,
          projectId: 1,
          orderType: state.msg.type,
          mobile: mobile,
          payment: state.msg.money,
          userId: userId,
          nikeName: userName,
          orderExplain: "临床会员活动",
        }
      );
      if (res.status == 200) {
        app_order_id.value = res.data;
        Toast.clear();
        selectPayWay();
      } else {
        Toast(res.message);
      }
    };
    const link = () => {
      var u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      } else {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      }
    };
    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == "ALL") {
          show.value = true;
        } else {
          MobilePay(way.value);
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + "/payment/pay/merge_qrcode", {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value,
        });
        const { qrCodeUrl } = res.data;
        if (qrCodeUrl) {
          showImg.value = true;
          qrCodeUrlImg.value = qrCodeUrl;
        }
        const inter = setInterval(() => {
          getStatus();
          if (MSpaymentStatus.value == "PAID") {
            clearInterval(inter);
          }
        }, 3000);
      }
      isBay.value = false;
    };
    const getStatus = async () => {
      const res = await request.get(baseUrl + "/payment/pay/query", {
        params: {
          appOrderId: app_order_id.value,
        },
      });
      const { paymentStatus } = res.data;
      MSpaymentStatus.value = paymentStatus;
      if (paymentStatus == "PAID") {
        Toast("支付成功");
      }
    };
    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + "/payment/pay/build", {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: "MEDSCI_WEB",
        payType:
          way.value == "ALL" ? "MWEB" : way.value == "WX" ? "JSAPI" : "NATIVE",
      });
      if (res2.code != "SUCCESS") {
        Toast(res2.msg);
        return;
      }
      const res3 = await request.post(baseUrl + "/payment/pay/order", {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value,
      });
      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data;
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement("div");
        div.innerHTML = aliH5.html;
        document.body.appendChild(div);
        document.forms[0].submit();
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl;
      }
      if (wechatH5) {
        // window.location.href = wechatH5.h5Url
        Toast.fail("用微信端打开进行支付");
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          "getBrandWCPayRequest",
          {
            appId: wechatJsapi.appId,
            timeStamp: wechatJsapi.timeStamp,
            nonceStr: wechatJsapi.nonceStr,
            package: wechatJsapi.packageStr,
            signType: wechatJsapi.signType,
            paySign: wechatJsapi.paySign,
          },
          function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
              Toast.success("支付成功！");
            }
          }
        );
      }
    };
    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time);
        return [val[0], val[1]];
      } else {
        return [0, time];
      }
    };
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    };
    const scrollToTop = () => {
      let ua = navigator.userAgent
      if (/mobile/i.test(ua)) {
        // 移动端
        window.scrollTo({
        top: 200,
        behavior: "smooth",
      });
      } else {
        // PC 端
        window.scrollTo({
        top: 400,
        behavior: "smooth",
      });
      }
      
      shakeImage();
      setTimeout(() => {
        cancelAnimationFrame(animationFrame.value);
      }, 1500);
    };
    const shakeImage = () => {
      let btn = document.querySelector(".summitBtn");
      // 将元素定位为relative
      btn.style.position = "relative";
      btn.style.left = `${Math.random() * 5 - 2.5}px`;
      btn.style.top = `${Math.random() * 5 - 2.5}px`;
      animationFrame.value = requestAnimationFrame(shakeImage);
    };

    return {
      animationFrame,
      shakeImage,
      applyBtn,
      scrollToTop,
      ...toRefs(state),
      loading,
      userInfo,
      active,
      actions,
      show,
      showImg,
      qrCodeUrlImg,
      isEnd,
      Login,
      buy,
      Pay,
      testPlay,
      getQueryVariable,
      wxShare,
      link,
      onSelect,
      formatTime,
      onFinish,
      time,
      joinActivity,
      joinStatus,
    };
  },
};
</script>

<style scoped lang="stylus">
.box {
  font-size 16px
  position relative
  max-width 10rem
  margin 0 auto
  margin-bottom: env(safe-area-inset-bottom);
  // padding-bottom: 90px;
  display flex
  .image {
    width 100%
  }

  .wrap{
    position: absolute;
    top: 1180px;
    left: 5%;
    width: 90%;
    height: 60px;
    background: white;
    opacity: 0
    z-index: 999
  }

  .couse {
    position: absolute;
    top 53px;
    right: 5px
    width 200px
  }


  .img-back{
    height 151px
    width 100%
    background-size: 100% 100%
    position absolute
    top 992px
    left 50%
    transform: translate(-50%)
    z-index 100
    box-sizing border-box

  }
  .last {
    position absolute
    right 0
    left 0
    bottom 0
    margin auto
    width 10rem
    display flex
    flex-direction: column;
    .bottom {
      height 50px
      display: flex
      img{
          width:100%
        }
      }
    }
  }

.van-popup {
  text-align center
  .title-box {
    text-align center
  }
  .btn-box {
    text-align center
  }
  .title {
    width 85px
    margin-top 20px
  }
  .content {
    font-size 13px
    box-sizing border-box
    padding 15px 20px 25px
    color #723107
    .item {
      display: flex
      margin 5px 0
      div {
        &:first-child {
          flex 0 0 80px
        }
      }
      a {
        color #723107
      }
    }
  }

  .btn {
    width 256px
    margin 0 auto
  }
}

.wrapper {
  height 100%
  display flex
  justify-content center
  align-items center
  flex-wrap wrap
  .text {
    font-size 18px
    color #ffffff
    width 100%
    text-align center
  }
}
 .content{
       position: absolute
       top: 10.95rem
       width: 100%
       height: auto;
 }
 .content_box{
   margin-top: 0.35rem;position: relative;
   margin-left:calc(50% - 2.57rem)
 }
 .mobile_box{
 margin-left:calc(50% - 3.65rem);
 margin-top:0.5rem
 }
 .inputMobile{
           font-weight: 500;
           font-size: 0.4rem;
           padding: 0.15rem 0.3rem;
           background-color: white;
           border-radius: 8px;
           width: 6.6rem;
 }
</style>
