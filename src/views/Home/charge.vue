<template>
  <div class="box">
    <div class="img">
      <img src="https://static.medsci.cn/public-image/ms-image/47385a10-f1db-11ec-a1b8-6123b3ff61ea_charge.jpg" alt="">
    </div>
    <div class="bottom" @click="joinActivity" v-if="!joinStatus">
      <img src="https://static.medsci.cn/public-image/ms-image/a730eba0-ed15-11ec-a1b8-6123b3ff61ea_join.png" alt="">
    </div>
    <div class="bottom" v-else>
      <img src="https://static.medsci.cn/public-image/ms-image/5d7ee490-ed4b-11ec-a1b8-6123b3ff61ea_joined.png" alt="">
    </div>
    <div class="guize" @click="showDialog(2)"></div>
    <div class="guwen1" @click="showDialog(1)">
      <img src="https://static.medsci.cn/public-image/ms-image/4714ec90-ed46-11ec-a1b8-6123b3ff61ea_kefu.png" alt="">
    </div>
    <div class="guwen2" @click="showDialog(1)"></div>
  </div>
  <van-overlay :show="show" @click="show = false" center>
    <!-- 顾问 -->
    <template v-if="showType == 1">
      <div class="dialog-wrapper" @click.stop="">
        <img class="img1" src="https://static.medsci.cn/public-image/ms-image/456f2470-ed49-11ec-a1b8-6123b3ff61ea_img1.png" alt="">
        <img class="img2" src="https://static.medsci.cn/public-image/ms-image/456f2470-ed49-11ec-a1b8-6123b3ff61ea_img2.png" alt="">
        <img class="img3" src="https://static.medsci.cn/public-image/ms-image/456f2470-ed49-11ec-a1b8-6123b3ff61ea_img3.png" alt="">
        <img @click.stop="show=false" class="dialog-close" src="https://static.medsci.cn/public-image/ms-image/456f2470-ed49-11ec-a1b8-6123b3ff61ea_close.png" alt="">
      </div>
    </template>
    <!-- 规则 -->
    <template v-if="showType == 2">
      <div class="guize-wrapper" @click.stop="">
        <img class="img1" src="https://static.medsci.cn/public-image/ms-image/0eef8520-ed44-11ec-a1b8-6123b3ff61ea_guize_content.png" alt="">
        <img @click.stop="show=false" class="dialog-close" src="https://static.medsci.cn/public-image/ms-image/456f2470-ed49-11ec-a1b8-6123b3ff61ea_close.png" alt="">
      </div>
    </template>
  </van-overlay>

</template>

<script>
import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
// import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import { baseUrlSix } from "@/utils/config618"

export default ({
  name: 'charge',
  components: {
  },
  setup: () => {
    // const router = useRouter()
    // const route = useRoute()
    const joinStatus = ref(false)
    const show = ref(false);
    const showType = ref('');
    const userInfo = reactive({
      info: {},
    })
    onMounted(async () => {
      document.title = '梅斯预充值送好礼'
      const userInfos = Cookies.get('userInfo');
      // userInfo.info = {"avatar":"http://thirdwx.qlogo.cn/mmopen/vi_32/gjR5H01q089ibicoupdmf2tYdbfx3hPL6fxbMS7NM9dEvaicANS4rACp1aDmDDsuQs8fqqXJrNruPvNLfuuAEVPWQ/132","email":"<EMAIL>","getRoleLoginResponses":[{"identityFlag":0,"roleId":8,"roleName":"前台角色"}],"mobile":"16621177880","plaintextUserId":2530807,"projectCode":"MSYX","projectId":1,"projectName":"梅斯医学","projectType":0,"projectUserStatus":1,"realName":"www","redirectUrl":"https://www.medsci.cn?sso_sessionid=5cb72530807_7b13fced5f4740eba43021fb1b3f9f57","token":{"accessToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************","accessTokenExpireTime":630720000,"refreshToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************"},"userId":"5cb72530807","userName":"伍达辉","userStatus":1,"wechatOpenid":"oAcsesyHWw-mbA2VEMWE9OTWBrkU"};
      if(!userInfos) {
        // Login()
      } else {
        userInfo.info = JSON.parse(userInfos);
        attendStatus()
      }
      // 埋点
      request.get(baseUrlSix+"/medsci-activity/visit", {
        params: {
            user_id: userInfo.info.plaintextUserId,
            ciphertext_user_id: userInfo.info.userId,
            event_type: 'view',
            type: 'pre_recharge',
        }
      })
      // 微信分享
      nextTick(() => {
        wxShare({
          title: "梅斯预充值送好礼",
          summary: "1次充值，长期有效，还送百万豪礼和梅斯服务",
          thumb: "https://static.medsci.cn/public-image/ms-image/a9713de0-ed45-11ec-a1b8-6123b3ff61ea_share_icon.png"
        })
      })
    });
    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        console.log('wx config success')
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: 'https://static.medsci.cn/product/medsci/active/zen0214/index.html#/charge',
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: 'https://static.medsci.cn/product/medsci/active/zen0214/index.html#/charge',
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }
    // 获取是否参加活动
    const attendStatus = async() => {
      const res = await request.post(baseUrlSix+"/medsci-activity/attend-status", {
        mobile: userInfo.info.mobile,
        type: 'pre_recharge'
      })
      if(res.data && res.data.status == 1) {
          joinStatus.value = true
      } else {
          joinStatus.value = false
      }
	  }
    // 参加活动
    const joinActivity = async() => {
        if(!Cookies.get('userInfo')) {
          Login()
          return false
        }
        if(joinStatus.value) {
            Toast('您已报名，等待回访')
        } else {
            const res = await request.post(baseUrlSix+"/medsci-activity/pre-recharge", {
                user_id: userInfo.info.plaintextUserId,
                ciphertext_user_id: userInfo.info.userId,
                mobile: userInfo.info.mobile,
                user_name: userInfo.info.userName,
                real_name: userInfo.info.realName,
                email: userInfo.info.email
            })
            if(res.code == 200) {
                Toast.success('报名成功')
                joinStatus.value = true;
            }
            if(res.code == 205) {
                Toast('您已报名，等待回访')
                joinStatus.value = true;
            }
            if(res.code != 200 && res.code != 205) {
                Toast(res.msg || '服务繁忙，请稍后再试')
            }
        }
	  }
    // 跳转登录
    const Login = () => {
      addLoginDom()
    }
    const showDialog = (type) => {
      show.value = true;
      showType.value = type;
    }
    return {
      userInfo,
      show,
      showType,
      wxShare,
      Login,
      showDialog,
      joinStatus,
      joinActivity,
    };
  }
})
</script>

<style scoped lang="stylus">
  img{
    width: 100%;
    height: 100%;
  }
  .box {
    width: 10rem;
    font-size: 0;
    padding-bottom: 1.61rem;
    margin: 0 auto;
    position: relative;
    .img{
        width: 10rem;
    }
    .bottom{
        position: fixed;
        left: 0;
        bottom: 0;
        width: 10rem;
        height: 1.61rem;
        display: flex;
        z-index: 1;
        right: 0;
        margin: 0 auto;
    }
    .left{
        width: 5.65rem;
        color: #333;
        background-color: #FFFFFF;
        flex: none;
        font-size: 0.43rem;
        text-align: center;
    }
    .right{
        width: 4.35rem;
        color: #fff;
        background-color: #FF6600;
        font-size: 0.49rem;
        text-align: center;
    }
    .right-2{
      background: #bdbdbd;
      font-size: 0.43rem;
    }
    .guwen2{
        width: 9rem;
        height: 1.5rem;
        position: absolute;
        left: 0.5rem;
        top: 58.85rem;
        opacity: 0;
        right: 0.5rem;
        margin: 0 auto;
    }
    .guwen1{
        width: 1.79rem;
        height: 1.92rem;
        position: fixed;
        right: 0;
        top: 9.45rem;
    }
    .guize{
        width: 1.8rem;
        height: 0.9rem;
        position: absolute;
        right: 1rem;
        top: 4.3rem;
    }
  }

  // 弹窗
  .guize-wrapper{
    width: 9.79rem;
    height: 14.63rem;
    position: fixed;
    top: 0.48rem;
    right: 0;
    overflow: hidden;
    .img1{
      width: 9.79rem;
      height: 14.63rem;
    }
    .dialog-close{
      width 0.65rem;
      height: 0.65rem;
      position: absolute;
      right: 0.4rem;
      top: 1.97rem;
    }
  }
  .dialog-wrapper{
    width: 9.79rem;
    height: 11.96rem;
    position: fixed;
    top: 1.43rem;
    right: 0;
    overflow: hidden;
    .img1{
      width: 9.79rem;
      height: 11.96rem;
    }
    .img2{
      width 3.04rem;
      height: 2.96rem;
      position: absolute;
      left: 1.39rem;
      top: 5.37rem;
    }
    .img3{
      width 3.04rem;
      height: 2.96rem;
      position: absolute;
      right: 1.51rem;
      top: 5.37rem;
    }
    .dialog-close{
      width 0.65rem;
      height: 0.65rem;
      position: absolute;
      right: 0.4rem;
      top: 1.03rem;
    }
  }
  @media screen and (min-width: 750px) {
    .box .guwen1{
        width: 1.79rem;
        height: 1.92rem;
        position: fixed;
        right: calc(50% - 375Px);
        top: 500Px;
    }
    .guize-wrapper {
        top: -1rem;
        left: 0;
        margin: 0 auto;
        transform: scale(0.7);
    }
    .dialog-wrapper {
        top: 0.1rem;
        left: 0;
        margin: 0 auto;
        transform: scale(0.7);
    }
  }
</style>