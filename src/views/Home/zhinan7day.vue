<template>
  <div class="box">
    <false-data ></false-data>
    <img class="image" src="https://static.medsci.cn/public-image/ms-image/3d0e0e20-e020-11ed-b4b6-4d1e60dcd7df_指南会员1元购长图-庆51.png" alt="">
    <div class="last">
      <div class="bottom" @click="buy">
          <img class="image" src="https://static.medsci.cn/public-image/ms-image/438314c0-e021-11ed-b4b6-4d1e60dcd7df_按键1.png" alt="">
      </div>
    </div>
  </div>

  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    @select="onSelect"
    cancel-text="取消"
    close-on-click-action
  />
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper" @click.stop>
      <div>
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="">
      </div>
    </div>
  </van-overlay>

</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import falseData from '@/components/falseData.vue';
import msSwiper from "./msSwiper.vue"
import { baseUrl } from "@/utils/config"
import { baseUrlSix } from "@/utils/config618"

export default ({
  // 免费领取
//   name: 'zhinan7day',
//   components: {
//   },
//   setup: () => {
//     // const router = useRouter()
//     // const route = useRoute()
//     const joinStatus = ref(false)
//     const show = ref(false);
//     // const timing = ref(true);
//     const userInfo = reactive({
//       info: {},
//     })

//     onMounted(async () => {
//       document.title = '老用户回归礼'
//       // const end = (new Date('2022/6/21')).getTime()
//       // const now = (new Date()).getTime()
//       // if(now-end > 0) {
//       //   timing.value = false;
//       // }
//       const userInfos = Cookies.get('userInfo');
//       userInfo.info = {"avatar":"http://thirdwx.qlogo.cn/mmopen/vi_32/gjR5H01q089ibicoupdmf2tYdbfx3hPL6fxbMS7NM9dEvaicANS4rACp1aDmDDsuQs8fqqXJrNruPvNLfuuAEVPWQ/132","email":"<EMAIL>","getRoleLoginResponses":[{"identityFlag":0,"roleId":8,"roleName":"前台角色"}],"mobile":"16621177880","plaintextUserId":2530807,"projectCode":"MSYX","projectId":1,"projectName":"梅斯医学","projectType":0,"projectUserStatus":1,"realName":"www","redirectUrl":"https://www.medsci.cn?sso_sessionid=5cb72530807_7b13fced5f4740eba43021fb1b3f9f57","token":{"accessToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************","accessTokenExpireTime":630720000,"refreshToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************"},"userId":"5cb72530807","userName":"伍达辉","userStatus":1,"wechatOpenid":"oAcsesyHWw-mbA2VEMWE9OTWBrkU"};
//       if(!userInfos) {
//         // Login()
//         attendStatus()//ts
//       } else {
//         userInfo.info = JSON.parse(userInfos);
//         attendStatus()
//       }
//       // 埋点
//       request.get(baseUrlSix+"/medsci-activity/visit", {
//         params: {
//             user_id: userInfo.info.plaintextUserId,
//             ciphertext_user_id: userInfo.info.userId,
//             event_type: 'view',
//             type: 'guider_give',
//         }
//       })

//       wxShare({
//         title: "老用户回归礼",
//         summary: "7天指南VIP，免费领！",
//         thumb: "https://static.medsci.cn/public-image/ms-image/f57157a0-c14a-11ed-8dcb-15ac2b21d063_4d671d01-96d4-41b3-b51f-e1dfb60e3fa0.png"
//       })
//     });
//     const wxShare = async (sub) => {
//       let apiUrl = window.location.href.split("#")[0];
//       const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
//         params: {
//           url: apiUrl
//         }
//       })
//       wx.config(res.data)
//       wx.error(function(error) {
//         console.log(error)
//       })
//       wx.ready(function() {
//         wx.onMenuShareAppMessage({
//           // 微信内朋友分享
//           title: sub.title, // 分享标题
//           desc: sub.summary, // 分享描述
//           // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
//           link: window.location.href,
//           imgUrl: sub.thumb, // 分享图标
//           success: function() {
//             // 设置成功
//             //  console.log(window.location.href,'555')
//           },
//         });
//         wx.onMenuShareTimeline({
//           // 微信朋友圈
//           title: sub.title, // 分享标题
//           desc: sub.summary, // 分享描述
//           // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
//           link: window.location.href,
//           imgUrl: sub.thumb, // 分享图标
//           success: function() {
//             // 设置成功
//             //  console.log(window.location.href,'555')
//           },
//         });
//       });
//     }
//     // 获取是否参加活动
//     const attendStatus = async() => {
//       const res = await request.post(baseUrlSix+"/medsci-activity/attend-status", {
//         mobile: userInfo.info.mobile,
//         type: 'guider_give'
//       })
//       if(res.data && res.data.status == 1) {
//           joinStatus.value = true
//       } else {
//           joinStatus.value = false
//       }
// 	  }
//     // 参加活动
//     const joinActivity = async() => {
//         // if(!Cookies.get('userInfo')) {
//         //   Login()
//         //   return false
//         // }
//         if(joinStatus.value) {
//             Toast('您已领取成功，无法重复领取')
//         } else {
//             const res = await request.post(baseUrlSix+"/medsci-activity/give/member-card", {
//                 // user_id: userInfo.info.plaintextUserId,
//                 // ciphertext_user_id: userInfo.info.userId,
//                 mobile: userInfo.info.mobile,
//                 type: 'guider_give',
//                 // user_name: userInfo.info.userName,
//                 // real_name: userInfo.info.realName,
//                 // email: userInfo.info.email,
//             })
//             if(res.code == 200) {
//                 Toast.success('您已领取成功')
//                 joinStatus.value = true;
//             }
//             if(res.code == 205) {
//                 Toast('您已领取成功，无法重复领取')
//                 joinStatus.value = true;
//             }
//             if(res.code != 200 && res.code != 205) {
//                 Toast(res.msg || '服务繁忙，请稍后再试')
//             }
//         }
// 	  }
//     // 跳转登录
//     const Login = () => {
//       addLoginDom()
//     }
//     const showDialog = () => {
//       show.value = true
//     }
//     return {
//       userInfo,
//       show,
//       // timing,
//       wxShare,
//       Login,
//       showDialog,
//       joinStatus,
//       joinActivity,
//     };
//   }
// })
// 支付
name: 'zhinan7day',
  components: {
    falseData,
    msSwiper
  },
  setup: () => {
    const router = useRouter()
    const route = useRoute()

    const loading = ref(false);
    const active = ref(false)
    const guize = ref(false)
    const time = ref(new Date("2032/02/20 23:59:59").getTime() - new Date().getTime())   // 还剩多少毫秒

    const isBay = ref(false);
    const way = ref('')
    const app_order_id = ref('')
    const MSpaymentStatus = ref("");
    const wxOpenId = ref("");
    const show = ref(false);
    const showImg = ref(false);
    const qrCodeUrlImg = ref("");
    const accessAppId = ref('college')
    const isEnd = ref(false)
    const actions = [
      { name: '支付宝支付' },
      { name: '微信支付' },
    ];
    const onSelect = (item) => {
      show.value = false;
      MobilePay(item.name == '支付宝支付' ? 'ALI' : 'WX')
    };

    const userInfo = reactive({
      info: {},
    })
    const state = reactive({
      msg: {}
    })

    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    }
    
    onMounted(async () => {
      document.title = '庆五一福利提前享'
      wxShare({
        title: "庆五一福利提前享",
        summary: "７天VIP指南会员1元购",
        thumb: "https://static.medsci.cn/public-image/ms-image/10597790-e177-11ed-b4b6-4d1e60dcd7df_300X300.png"
      })
      const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      state.msg = res.data;
      // console.log(state.msg, '11')
      if(state.msg && state.msg.activityEndTime) {
        state.msg.activityEndTime = state.msg.activityEndTime.replace(/-/g, '/');
        time.value = new Date(state.msg.activityEndTime).getTime() - new Date().getTime()
      }
      if (time.value < 0) {
        isEnd.value = true
      }
      let userAgent = navigator.userAgent;
      if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
        if(route.query.openId || getQueryVariable("openId")) {
          buy()
        }
      }
    });

    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      // console.log(apiUrl)
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/zhinan7day",
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/zhinan7day",
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }


    const getAuth = () => {
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
        way.value = "WX"
        //微信扫码
        const AppId = 'wx9096048917ec59ab'
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href)
          const RedirectUrl = encodeURIComponent(
              `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          )
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`
        } else {
          wxOpenId.value = openId
        }
      } else if (userAgent != null && userAgent.indexOf('AlipayClient') > -1) {
        way.value = "ALI"
      } else {
        way.value = "ALL"
      }
    }

    const buy = async () => {
      // if (isEnd.value) {
      //   return
      // }
      const userInfos = Cookies.get('userInfo');
      if(userInfos) {
        getAuth()
        Toast.loading({
          duration: 0,
          message: '提交中...',
          loadingType: 'spinner',
        });
      }
      if (!userInfos) {
        const sso_sessionid = route.query.sso_sessionid || getQueryVariable("sso_sessionid")
        if (sso_sessionid) {
          const res = await request.post('/medsciUser/getLoginUserInfoBySid', { sessionid: sso_sessionid });
          userInfo.info = res.data;
          Pay()
        } else {
          Login()
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom()
        }
        Pay()
      }
    }

    // 跳转登录
    const Login = () => {
      addLoginDom()
    }

    const testPlay = (item) => {
      if (item.link) {
        window.location.href = item.link
      }
    }

    const Pay = async () => {
      const { userId, userName, realName, mobile, email, plaintextUserId } = userInfo.info;

      const res = await request.post("/activity/createOrder", {
        "itemId": state.msg.id,
        "itemNum": 1,
        "itemPicPath": state.msg.cardImage,
        "itemTitle": state.msg.cardName,
        "itemPrice": state.msg.activityPrice,
        // "itemPrice": state.msg.firstPrice,
        "projectId": state.msg.projectId,
        "orderType": 1,
        "mobile": mobile,
        "payment": 0,
        "userId": userId,
        "nikeName": userName,
        "buyerMessage": '指南会员活动',
      });
      app_order_id.value = res.data;
      Toast.clear()
      selectPayWay()
    }

    const link = () => {
      var u = navigator.userAgent;
      const isAndroid =  u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href = "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"
      } else {
        window.location.href = "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"
      }
    }
    
    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time)
        return [val[0], val[1]]
      } else {
        return [0, time]
      }
    }

    const showGuize = () => {
      guize.value = true
    }
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    }


    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == "ALL") {
          show.value = true
        } else {
          MobilePay(way.value)
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + "/payment/pay/merge_qrcode", {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value
        })
        const { qrCodeUrl } = res.data;
        if (qrCodeUrl) {
          showImg.value = true
          qrCodeUrlImg.value = qrCodeUrl
        }

        const inter = setInterval(() => {
          getStatus()
          if (MSpaymentStatus.value == 'PAID') {
            clearInterval(inter)
          }
        }, 3000);
      }
      isBay.value = false;
    }

    const getStatus = async () => {
      const res = await request.get(baseUrl + "/payment/pay/query", {
        params: {
          appOrderId: app_order_id.value,
        }
      })

      const { paymentStatus } = res.data
      MSpaymentStatus.value = paymentStatus
      if (paymentStatus == "PAID") {
        Toast("支付成功")
      }
    }

    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + "/payment/pay/build", {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: "MEDSCI_WEB",
        payType: way.value == 'ALL' ? 'MWEB' :  way.value == 'WX' ? 'JSAPI' : 'NATIVE'
      })

      if (res2.code != 'SUCCESS') {
        Toast(res2.msg)
        return
      }

      const res3 = await request.post(baseUrl + "/payment/pay/order", {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value
      })

      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement('div')
        div.innerHTML =  aliH5.html
        document.body.appendChild(div)
        document.forms[0].submit()
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl
      }
      if (wechatH5) {
        window.location.href = wechatH5.h5Url
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          {
              appId: wechatJsapi.appId,
              timeStamp: wechatJsapi.timeStamp,
              nonceStr: wechatJsapi.nonceStr,
              package: wechatJsapi.packageStr,
              signType: wechatJsapi.signType,
              paySign: wechatJsapi.paySign,
          },
          function(res) {
            if (res.err_msg == 'get_brand_wcpay_request:ok') {
              Toast.success('支付成功！')
            }
          }
      )
      }
    }

    return {
      ...toRefs(state),
      loading,
      userInfo,
      active,
      guize,
      time,

      actions,
      show,
      showImg,
      qrCodeUrlImg,
      isEnd,

      Login,
      buy,
      Pay,
      testPlay,
      getQueryVariable,
      wxShare,
      link,
      showGuize,
      formatTime,
      onFinish,
      onSelect
    };
  }
})
</script>

<style scoped lang="stylus">
  .box {
    font-size 16px
    position relative
    // max-width 750Px
    max-width 10rem
    margin 0 auto
    margin-bottom: env(safe-area-inset-bottom);
    //padding-bottom: 48px;
    display: flex;
    .image {
      width 100% 
    }

    .couse {
      position: absolute;
      top 53px;
      right: 5px
      width 200px
    }

    .guize {
      width 79px
      position absolute
      top 142px
      right 0
    }

    .img-back{
      height 151px
      width 100%
      background-size: 100% 100%
      position absolute
      top 992px
      left 50%
      transform: translate(-50%)
      z-index 100
      box-sizing border-box
      
    }
    .last {
      position fixed
      left 0
      bottom 0
      right 0
      // width 100vw
      margin auto
      width 10rem
      .top {
        height 40px
        display: flex
        background-color #FFC700
        display: flex
        align-items: center
        .img {
          width 50px
          height 32px
          margin 0 5px 0 8px
        }
        .van-count-down {
          display: flex
          align-items: center
        }
        .block {
          display: flex
          &-item {
            background-color #ffffff
            color #333333
            width 20px
            height 25px
            text-align center
            font-size 20px
            font-weight blod
            line-height 25px
            border-radius 5px
            margin 0 2px
          }
        }
        .colon {
          color #333333
          font-size 8px
          margin 0 1px
          &.a {
            margin-top: 10px
          }
          .mill {
            height 12px
            line-height 12px
          }
        }
      }
      .bottom {
        height 50px
        padding 40px  0px
        display: flex
        justify-content center
        background-color #001b72
        img{
          width 200px
        }
        &-left {
          background-color #ffffff
          width calc(10rem - 169px)
          color rgba(114,49,7,1)
          display: flex
          align-items: center
          justify-content: center;
          // padding 0 17px
          box-sizing border-box
          font-size 16px
          .Num {
            color #010101
            font-weight 500
            span {
              color #FF5F00
              font-size 22px
              font-weight bold
            }
          }

          .discount {
            font-size 12px
            color rgba(1,1,1,0.5)
          }
        
        }
        &-right {
          background-color #FF5400
          width 169px
          color #ffffff
          text-align center
          height 50px
          line-height 50px
          font-size 19px
          &.cancel {
            background-color #cccccc
          }
        }
      }
    }
  }

  .van-popup {
    text-align center
    .title-box {
      text-align center
    }
    .btn-box {
      text-align center
    }
    .title {
      width 85px
      margin-top 20px
    }
    .content {
      font-size 13px
      box-sizing border-box
      padding 15px 20px 25px
      color #723107
      .item {
        display: flex
        margin 5px 0
        div {
          &:first-child {
            flex 0 0 80px
          }
        }
        a {
          color #723107
        }
      }
    }

    .btn {
      width 256px
      margin 0 auto
    }
  }

  .wrapper {
    height 100%
    display flex
    justify-content center
    align-items center
    flex-wrap wrap
    .text {
      font-size 18px
      color #ffffff
      width 100%
      text-align center
    }
  }
</style>