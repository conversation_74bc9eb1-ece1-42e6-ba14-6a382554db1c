<template>
  <div :class="activeDetail.participateButton ? 'box2': 'box'">
    <img class="image" :src="activeDetail.poster" alt="">
    <!-- 日常活动类型 -->
    <div v-if="activeDetail.type == 1">
      <!-- 固定在页面下方 -->
      <div class="bottom" v-if="!activeDetail.participateButton" @click="join(activeDetail.status)">
        <img :src="activeDetail.participateButtonPicture" alt="">
      </div>
      <!-- 悬浮在页面下方 -->
      <div class="bottom2" v-else @click="join(activeDetail.status)">
        <img :src="activeDetail.participateButtonPicture" alt="">
      </div>
    </div>  
    <!-- 助力活动类型 -->
    <div v-if="activeDetail.type == 2">
      <div class='my-invite-btn' @click="showInvitePerson">我的邀请</div>
      <div class="bottom support-bottom" v-if="!activeDetail.participateButton">
        <img :src="activeDetail.participateButtonPicture" alt="" @click="join(activeDetail.status)">
        <img :src="activeDetail.participateButtonPictureRight" alt="" @click="shareActivity(activeDetail.status)">
      </div>
      <div class="bottom2 support-bottom2" v-else>
        <img :src="activeDetail.participateButtonPicture" alt="" @click="join(activeDetail.status)">
        <img :src="activeDetail.participateButtonPictureRight" alt="" @click="shareActivity(activeDetail.status)">
      </div>
    </div>
  </div>
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper">
      <div class="img-wrap">
        <img :src="qrCodeUrlImg" style="width: 100%;height: 100%" alt="">
      </div>
    </div>
  </van-overlay>
  <van-overlay :show="showPostImg" @click="showPostImg = false">
    <div class="wrapper" style="height:100%;">
      <div class="img-wrap" style="height:100%;overflow:scroll">
        <img :src="postImgUrl" style="width: 100%;" alt="">
      </div>
    </div>
  </van-overlay>
  <!-- 我的邀请 -->
  <van-overlay :show="showMyInvite" @click="showMyInvite = false">
    <div class="wrapper" style="height:100%;">
      <div class="content-box">
        <div class='top-wrap'>
          <img src="https://static.medsci.cn/public-image/ms-image/<EMAIL>" style="width: 100%" alt="">
          <div class="total">我的邀请 <span>{{ totalSize }}</span></div>
        </div>
        <div class='botm-wrap'>
          <div class='title'><span class='name'>用户昵称</span><span style='padding-left:70px'>参与时间</span></div>
          <div class='list' v-if="recommenderList&&recommenderList.length > 0">
            <div v-for="item in recommenderList" class='list-item' :key="item.id"><span class='name'>{{ item.username }}</span><span class="time">{{ item.createdTime.replace(/[^\d]/g,'.') }}</span></div>
          </div>
          <div class="empty-list" v-else>
            <img src="https://static.medsci.cn/public-image/ms-image/30c680d0-4b91-11ed-b66b-937b834e3ef9_编组@2x.png" style="width:55px"/>
            <p style="font-size:12px;color:#999">暂无受邀的好友，赶紧去邀请吧</p>
          </div>
        </div>
        <div class="close" style="text-align:center">
          <van-button icon="cross" round type="primary" color="rgba(0,0,0,0.80)" size="small"/>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import { msBaseUrl, activityBaseUrl } from "@/utils/sanofiConfig"

export default ({
  name: 'support',
  components: {
  },
  setup: () => {
    const route = useRoute()
    const router = useRouter()
    const activeId = ref("")
    const activeDetail = reactive({})

    const showImg = ref(false);
    const qrCodeUrlImg = ref('')
    const userInfo = reactive({
      info: {},
    })
    const isParticipate = ref(false)
    const showPostImg = ref(false)
    const postImgUrl = ref("")

    const showMyInvite = ref(false)
    const totalSize = ref(0)
    const recommenderList = reactive([])

    onMounted(async () => {
      activeId.value = route.query.id
      // 埋点
      getMaiDian()
      // 获取活动详情
      getActivityDetail()
      // 主站埋点
      if(window.navigator.userAgent.includes('medsci_app')){
        getMedsciMaiDian('medsci_app')
      }else {
        getMedsciMaiDian('medsci_site')
      }
    });
    // 主站埋点
    const getMedsciMaiDian = (sign) => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      const script = document.createElement('script')
      script.id = 'maidian'
      script.src = 'https://img.medsci.cn/web/js/demo/msstatis.min.js'
      const s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(script, s)

      script.onload = function() {
        const appId = 1
        const channel = sign
        const id = userInfos.userId
        const token = userInfos.token.accessToken
        window.MsStatis.init(appId, channel, id, token)
        window.MsStatis.disableEvent = ['pushState', 'popstate']

        // isMaidianLoad = true
        // if(memorize.length) {
        //   memorize.forEach(params => {
        //     window.MsStatis.setStatisRule(params)
        //   })
        // }
      }
    }
    const getActivityDetail = async () => {
      let res = await request.get(activityBaseUrl+`/medsciActivity/getActivityTemplateById/${activeId.value}`)
      if(res.status == 200) {
        Object.assign(activeDetail, res.data)
        document.title = activeDetail.name
        // 用户没登陆去登录
        getUserInfo()
        wxShare({
          title:  activeDetail.shareTitle,
          summary: activeDetail.shareSummary,
          thumb: activeDetail.shareIcon
        })
      }
    }
    // 埋点
    const getMaiDian = async () => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token ? userInfos.token.accessToken : ""
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/insertActivityLog', params)
      if(res.status == 200) {
        console.log('埋点了')
      }
    }
    // 判断用户是否登录
    const getUserInfo = async () => {
      const userInfos = Cookies.get('userInfo');
      if (!userInfos) {
        // &sso_sessionid=c8aa5301313_4389ad3c1cb348d99540ec85d77a193d
        const sso_sessionid = route.query.sso_sessionid || getQueryVariable("sso_sessionid")
        if (sso_sessionid) {
          const res = await request.post('/medsciUser/getLoginUserInfoBySid', { sessionid: sso_sessionid });
          userInfo.info = res.data;
          isLimitComplete()
        } else {
          if(window.location.href.includes('code=') && window.location.href.includes('state=')) {

          } else {
            var agent = navigator.userAgent.toLowerCase()
            if (agent.match(/MicroMessenger/i) == 'micromessenger') {
              // 微信环境，默认微信授权登录
              authWechat()
            } else {
              Login()
            }
          }
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom()
        }
        isLimitComplete()
      }
    }
    // 用户登陆后 是否限制完善信息
    const isLimitComplete = async () => {
      getUserInvitationPoster()
      getUserRecommender()
      // 是否限制完善信息
      if(activeDetail.usersPerfectInformation) {
        const res = await request.get(msBaseUrl+'/perfectInfo/userInfoStatus?encryptionUserId='+ userInfo.info.userId)
        if(res.data.isCompleteInfo){
          // 用户参加活动状态
          // getUsersParticipateStatus()
        } else {
          addPerfectInfoDom()
        }
      }else {
        // 用户参加活动状态
        // getUsersParticipateStatus()
      }
    }
    // 获取用户参加活动状态
    const getUsersParticipateStatus = async () => {
      let userInfos = JSON.parse(Cookies.get('userInfo'))
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token.accessToken
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipateStatus', params)
      if(res.status == 200){
      // 用户参与过  有二维码 弹出二维码 没有二维码 点击按钮已经参与活动
        isParticipate.value = true
        if(res.data && res.data.enterpriseWechatQrCode){
          qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
          showImg.value = true
        } else {
          Toast('您已参与过该活动！')
        }
      }else {
        isParticipate.value = false
        Toast(res.message)
      }
    }
    const join = async (val) => {
       if(val == 0) {
        Toast('该活动未审核通过，请您先审核！')
       } else {
        enterActivity()
        //  if(isParticipate.value){
        //   Toast('您已参与过该活动！')
        //  } else {
        //     let userInfos = JSON.parse(Cookies.get('userInfo'))
        //     let params = {
        //       "activityTemplateId": activeId.value,
        //       "token": userInfos.token.accessToken
        //     }
        //     const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipate', params)
        //     if(res.status == 200 || res.status == 1013){
        //       if(res.data&&res.data.enterpriseWechatQrCode) {
        //         qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
        //         showImg.value = true
        //       } else {
        //         res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
        //       }
        //     } else{
        //       Toast(res.message)
        //     }
        //  }
       }
    }
    // 参加活动
    const enterActivity = async () => {
        let userInfos = JSON.parse(Cookies.get('userInfo'))
        let params = activeDetail.type == 1 ? {
          "activityTemplateId": activeId.value,
          "token": userInfos.token.accessToken
        }: {
          "activityTemplateId": activeId.value,
          "recommender": route.query.recommender ? route.query.recommender : '',
          "token": userInfos.token.accessToken
        }
        const res = await request.post(activityBaseUrl+'/medsciActivity/usersParticipate', params)
        if(res.status == 200 || res.status == 1013){
          if(res.data && res.data.enterpriseWechat) {
            if(res.data.enterpriseWechatQrCode) {
              qrCodeUrlImg.value = res.data.enterpriseWechatQrCode
              showImg.value = true
            } else {
              res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
            }
          } else {
            res.status == 200 ? Toast('您已经报名成功！') : Toast('您已参与过该活动！')
          }
        } else{
          Toast(res.message)
        }
    }
    const wxShare = async (sub) => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      let apiUrl = window.location.href.split("#")[0];
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: activeDetail.type == 1 ? window.location.href : (window.location.href.includes("test") ? `https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?test&id=${activeId.value}&recommender=${userInfos.userId?userInfos.userId: ''}` : `https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?id=${activeId.value}&recommender=${userInfos.userId?userInfos.userId: ''}`),
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: activeDetail.type == 1 ? window.location.href : (window.location.href.includes("test") ? `https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?test&id=${activeId.value}&recommender=${userInfos.userId?userInfos.userId: ''}` : `https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?id=${activeId.value}&recommender=${userInfos.userId?userInfos.userId: ''}`),
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }
    // 跳转登录
    const Login = () => {
      addLoginDom()
    }
    // 邀请弹出二维码海报
    const getUserInvitationPoster = async () => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token ? userInfos.token.accessToken : ""
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/userInvitationPoster', params)
      if(res.status == 200) {
        postImgUrl.value = res.data.activityInvitationPoster
      }
    }
    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    }
    const shareActivity = (val) => {
      if(val == 0){
        Toast('该活动未审核通过，请您先审核！')
      } else {
        showPostImg.value = true
      }
    }
    // 用户推荐列表
    const getUserRecommender = async () => {
      let userInfos = Cookies.get('userInfo') ? JSON.parse(Cookies.get('userInfo')) : {}
      let params = {
        "activityTemplateId": activeId.value,
        "token": userInfos.token ? userInfos.token.accessToken : ""
      }
      const res = await request.post(activityBaseUrl+'/medsciActivity/getUserRecommender', params)
      if(res.status == 200) {
        recommenderList.length = 0
        recommenderList.push(...res.data)
        totalSize.value = res.totalSize
      } else {
        totalSize.value = res.totalSize
      }
    }
    // 我的邀请弹框
    const showInvitePerson = () => {
      if(activeDetail.status == 0) {
        Toast('该活动未审核通过，请您先审核！')
      } else {
        showMyInvite.value = true
      }
    }
    return {
      showImg,
      qrCodeUrlImg,
      activeDetail,
      userInfo,
      postImgUrl,
      showPostImg,
      wxShare,
      Login,
      join,
      shareActivity,
      showMyInvite,
      showInvitePerson,
      totalSize,
      recommenderList
    };
  }
})
</script>
<style scoped lang="stylus">
.van-overlay{
  display: flex
  align-items: center
  justify-content: center
  .wrapper{
    width: 100%
    height:300px
    display: grid
    justify-content: center
    align-content: center
    padding: 0px 20px
    .img-wrap{
      width: 100%;
      height 230px
    }
    .content-box{
      width 330px
      height 100%
      overflow-y:scroll
      box-sizing border-box
      &::-webkit-scrollbar {
        width:4px;
      }
      &::-webkit-scrollbar-track {
        -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.2);
        border-radius:0px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius:10px;
        background:rgba(0,0,0,0.2);
        -webkit-box-shadow:inset006pxrgba(0,0,0,0.2);
      }
      .top-wrap{
        position: relative
        .total{
          font-family: 'PingFangSC-bold';
          width:100%
          text-align: center
          position: absolute
          bottom: 20px
          font-size: 25px
          font-weight: 550;
          background-image:-webkit-linear-gradient(right,#fff,#EEEC38,#fff);

          -webkit-background-clip:text; 

          -webkit-text-fill-color:transparent; 
        }
      }
      .botm-wrap{
        padding: 16px 22px;
        margin-top -11px
        background #fff
        border-radius 0px 0px 16px 16px
        .title{
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 11px;
          color: #999;
          letter-spacing: 0;
          display flex
          align-items center
          justify-content space-between
          span{
            width 50%
            display inline-block
          }
          span.name{
            text-align left
          }
        }
        .empty-list{
          height 300px
          display flex
          flex-direction: column
          align-items: center;
          justify-content: center
        }
        .list{
          margin-top 15px
          background #fff
          font-family: PingFangSC-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #000000;
          letter-spacing: 0;
          height 300px
          overflow-y scroll
          &::-webkit-scrollbar {
            width:4px;
          }
          &::-webkit-scrollbar-track {
            -webkit-box-shadow:inset 0 0 6px rgba(0,0,0,0.2);
            border-radius:0px;
          }
          &::-webkit-scrollbar-thumb {
            border-radius:10px;
            background:rgba(0,0,0,0.2);
            -webkit-box-shadow:inset006pxrgba(0,0,0,0.2);
          }
          .list-item{
            height 36px
            display flex
            align-items center
            justify-content space-between
          }
          span{
            width 50%
            display inline-block
            text-align center
          }
          span.name{
            text-align left

            width: 110px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
          }
          span.time{ 
            color: #666;
          }
        }
      }
    }
  }
}
  .box {
    position relative
    max-width 10rem
    margin 0 auto
    margin-bottom: env(safe-area-inset-bottom);
    padding-bottom: 1.6rem;
    font-size: 0;
    .image {
      width 100% 
    }
    .my-invite-btn{
      position absolute
      top 23px
      right 20px
      font-size 14px
      width: 80px;
      height: 28px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      background: rgba(0,0,0,0.60);
      border-radius: 14px;
      text-align center
      line-height 28px
    }
    .bottom {
      height 1.6rem
      position: fixed;
      right: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      text-align: center
      max-width: 10rem;
      img{
        width: 100%
        max-width: 10rem;
        height: 100%
      }
    }
    .support-bottom{
      display: flex
      align-items: center
      justify-content: space-between
      img{
        width: 50%
      }
    }
  }
  .box2{
      position relative
      max-width 10rem
      margin 0 auto
      margin-bottom: env(safe-area-inset-bottom);
      padding-bottom: 3rem;
      font-size: 0;
      .image {
        width 100% 
      }
    .my-invite-btn{
      position absolute
      top 23px
      right 20px
      font-size 14px
      width: 80px;
      height: 28px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      background: rgba(0,0,0,0.60);
      border-radius: 14px;
      text-align center
      line-height 28px
    }
    .bottom2{
      height 3rem
      max-width: 10rem;
      position: fixed;
      right: 0;
      left: 0;
      bottom: 0;
      margin: auto;
      box-sizing: border-box
      padding: 30px 30px;
      text-align: center
      img{
        width: 100%
        height: 100%
        max-width: 10rem;
        border-radius: 20px
      }
    }
    .support-bottom2{
      display: flex
      align-items: center
      justify-content: space-between
      img{
        width: 48%
        border-radius: 26px
      }
    }
  }
</style>