<template>
  <div class="box">
    <false-data></false-data>

    <img class="image" :src="url" alt="" v-if="!isAll" />
    <img
      class="image"
      src="@/assets/元旦-心血管.png"
      alt=""
      v-if="isAll"
    />

    <img class="guize" @click="showGuize" :src="url1" alt="" />
    <img
      class="guize1"
      @click="goPage"
      :src="url2"
      alt=""
      v-if="!isAll"
      style="top: 22.72%"
    />
    <img class="guize1" @click="goPage" :src="url2" alt="" v-if="isAll" />

    <div class="img-back" v-if="isMobile && !isAll">
      <div style="display: flex; justify-content: center">
        <img
        style="width: 80%;margin-bottom:0.3rem"
          src="https://static.medsci.cn/public-image/ms-image/b8c02a10-a4a3-11ee-a5c7-fd0174f02b25_xxg_kcxz(1).png"
          alt=""
        />
      </div>
      <div style="height: calc(15.81333rem); overflow: auto">
        <div
          class="item"
          :class="{ lastss: index == 3 }"
          v-for="(item, index) in list"
          :key="index"
          @click="getClass(item)"
        >
          <div class="left-btn">
            <!-- <div class="no-active" v-if="!item.active"></div>
            <img class="active" v-else :src="url3" alt="" /> -->
            <div class="no-active" v-if="!item.active && item.isBuy==0"></div>
            <img class="active" v-if="item.active && item.isBuy==0" :src="url3" alt="" />
            <img class="active" v-if="item.isBuy!=0" :src="url6" alt="" />
          </div>
          <div class="doctor">
            <img class="cover" :src="item.cover" alt="" />
            <div class="doctor-info">
              <div>
                <span>讲师：</span>
                <span v-if="item.speaker">{{ item.speaker.name }}</span>
                <span v-else></span>
              </div>
              <div>
                <span>课题名：</span>
                <span>{{ item.title }}</span>
              </div>

              <div class="click-price">
                <img
                  class="click"
                  @click.stop="testPlay(item)"
                  :src="item.isBuy != 0 ? url5: url4"
                  alt=""
                />
                <div class="price">原价：{{ item.price }}元</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <a @click="getCourse">
        <img
          v-if="isAll"
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/df7e3440-a097-11ee-a5c7-fd0174f02b25_all.png"
          alt=""
        />
        <img
          v-else
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/16ef2ab0-a098-11ee-a5c7-fd0174f02b25_part.png"
          alt=""
        />
      </a>
    </div>
    <div class="img-back1" v-if="isMobile && isAll">
      <div style="display: flex; justify-content: center">
        <img
        style="width: 80%;margin-bottom:0.3rem"
          src="https://static.medsci.cn/public-image/ms-image/b8c02a10-a4a3-11ee-a5c7-fd0174f02b25_xxg_kcxz(1).png"
          alt=""
        />
      </div>
      <div style="height: calc(16.81333rem - 3.42rem); overflow: auto">
        <div
          class="item"
          :class="{ lastss: index == 3 }"
          v-for="(item, index) in list"
          :key="index"
          @click="getClass(item)"
        >
          <div class="left-btn">
            <!-- <div class="no-active" v-if="!item.active"></div>
            <img class="active" v-else :src="url3" alt="" /> -->
            <div class="no-active" v-if="!item.active && item.isBuy==0"></div>
            <img class="active" v-if="item.active && item.isBuy==0" :src="url3" alt="" />
            <img class="active" v-if="item.isBuy!=0" :src="url6" alt="" />
          </div>
          <div class="doctor">
            <img class="cover" :src="item.cover" alt="" />
            <div class="doctor-info">
              <div>
                <span>讲师：</span>
                <span v-if="item.speaker">{{ item.speaker.name }}</span>
                <span v-else></span>
              </div>
              <div>
                <span>课题名：</span>
                <span>{{ item.title }}</span>
              </div>

              <div class="click-price">
                <img
                  class="click"
                  @click.stop="testPlay(item)"
                  :src="item.isBuy != 0 ? url5: url4"
                  alt=""
                />
                <div class="price">原价：{{ item.price }}元</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <a @click="getCourse">
        <img
          v-if="isAll"
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/df7e3440-a097-11ee-a5c7-fd0174f02b25_all.png"
          alt=""
        />
        <img
          v-else
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/16ef2ab0-a098-11ee-a5c7-fd0174f02b25_part.png"
          alt=""
        />
      </a>
    </div>

    <div class="img-back-pc" v-if="!isMobile && isAll">
      <div style="display: flex; justify-content: center">
        <img
          style="width: 80%;margin-bottom:1rem"
          src="https://static.medsci.cn/public-image/ms-image/b8c02a10-a4a3-11ee-a5c7-fd0174f02b25_xxg_kcxz(1).png"
          alt=""
        />
      </div>
      <div style="height: calc(16.81333rem - 3.42rem); overflow: auto">
        <div
          class="item"
          :class="{ lastss: index == 3 }"
          v-for="(item, index) in list"
          :key="index"
          @click="getClass(item)"
        >
          <div class="left-btn">
            <div class="no-active" v-if="!item.active && item.isBuy==0"></div>
            <img class="active" v-if="item.active && item.isBuy==0" :src="url3" alt="" />
            <img class="active" v-if="item.isBuy!=0" :src="url6" alt="" />
          </div>
          <div class="doctor">
            <img class="cover" :src="item.cover" alt="" />
            <div
              class="doctor-info"
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-around;
              "
            >
              <div>
                <div>
                  <span>讲师：</span>
                  <span v-if="item.speaker">{{ item.speaker.name }}</span>
                  <span v-else></span>
                </div>
                <div>
                  <span>课题名：</span>
                  <span>{{ item.title }}</span>
                </div>
              </div>

              <div class="click-price">
                <img
                  class="click"
                  @click.stop="testPlay(item)"
                  :src="url4"
                  alt=""
                />
                <div class="price">原价：{{ item.price }}元</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <a @click="getCourse">
        <img
          v-if="isAll"
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/df7e3440-a097-11ee-a5c7-fd0174f02b25_all.png"
          alt=""
        />
        <img
          v-else
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/16ef2ab0-a098-11ee-a5c7-fd0174f02b25_part.png"
          alt=""
        />
      </a>
    </div>
    <div class="img-back-pc1" v-if="!isMobile && !isAll">
      <div style="display: flex; justify-content: center">
        <img
        style="width: 80%;margin-bottom:1rem"
          src="https://static.medsci.cn/public-image/ms-image/b8c02a10-a4a3-11ee-a5c7-fd0174f02b25_xxg_kcxz(1).png"
          alt=""
        />
      </div>
      <div style="height: calc(19.81333rem); overflow: auto">
        <div
          class="item"
          :class="{ lastss: index == 3 }"
          v-for="(item, index) in list"
          :key="index"
          @click="getClass(item)"
        >
          <div class="left-btn">
            <div class="no-active" v-if="!item.active && item.isBuy==0"></div>
            <img class="active" v-if="item.active && item.isBuy==0" :src="url3" alt="" />
            <img class="active" v-if="item.isBuy!=0" :src="url6" alt="" />
          </div>
          <div class="doctor">
            <img class="cover" :src="item.cover" alt="" />
            <div
              class="doctor-info"
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-around;
              "
            >
              <div>
                <div>
                  <span>讲师：</span>
                  <span v-if="item.speaker">{{ item.speaker.name }}</span>
                  <span v-else></span>
                </div>
                <div>
                  <span>课题名：</span>
                  <span>{{ item.title }}</span>
                </div>
              </div>

              <div class="click-price">
                <img
                  class="click"
                  @click.stop="testPlay(item)"
                  :src="url4"
                  alt=""
                />
                <div class="price">原价：{{ item.price }}元</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <a @click="getCourse">
        <img
          v-if="isAll"
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/df7e3440-a097-11ee-a5c7-fd0174f02b25_all.png"
          alt=""
        />
        <img
          v-else
          class="wechat"
          src="https://static.medsci.cn/public-image/ms-image/16ef2ab0-a098-11ee-a5c7-fd0174f02b25_part.png"
          alt=""
        />
      </a>
    </div>

    <div class="last" v-if="isMobile">
      <!-- <div class="top">
        <img
          class="img"
          src="https://static.medsci.cn/public-image/ms-image/faff5180-3538-11ec-8e2f-1389d01aad85_time.png"
          alt=""
        />
        <van-count-down millisecond :time="time" @finish="onFinish">
          <template #default="timeData">
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.days)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">天</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.hours)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">时</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.minutes)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">分</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.seconds)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">秒</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.milliseconds)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon">
              <div class="mill">毫</div>
              <div class="mill">秒</div>
            </span>
          </template>
        </van-count-down>
      </div> -->

      <div class="bottom">
        <div class="bottom-left">
          <div class="radio" @click="allInOne">
            <div class="no-active" v-if="!active"></div>
            <img class="active" v-else :src="url3" alt="" />
            全选
          </div>
          <div class="info">
            <div>合计：{{ activityPrice }}元</div>
            <div class="discount">已优惠{{ preferentialPrice }}元</div>
          </div>
        </div>
        <div
          class="bottom-right"
          style="background-color: #b51c1c"
          :class="{ cancel: isEnd }"
          @click="buy"
        >
          结算
        </div>
      </div>
    </div>
    <div class="last1" v-else>
      <!-- <div class="top">
        <img
          class="img"
          src="https://static.medsci.cn/public-image/ms-image/faff5180-3538-11ec-8e2f-1389d01aad85_time.png"
          alt=""
        />
        <van-count-down millisecond :time="time" @finish="onFinish">
          <template #default="timeData">
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.days)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">天</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.hours)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">时</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.minutes)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">分</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.seconds)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon a">秒</span>
            <div class="block">
              <div
                class="block-item"
                v-for="(t, key) in formatTime(timeData.milliseconds)"
                :key="key"
              >
                {{ t }}
              </div>
            </div>
            <span class="colon">
              <div class="mill">毫</div>
              <div class="mill">秒</div>
            </span>
          </template>
        </van-count-down>
      </div> -->

      <div class="bottom">
        <div class="bottom-left">
          <div class="radio" @click="allInOne">
            <div class="no-active" v-if="!active"></div>
            <img class="active" v-else :src="url3" alt="" />
            全选
          </div>
          <div class="info">
            <div>合计：{{ activityPrice }}元</div>
            <div class="discount">已优惠{{ preferentialPrice }}元</div>
          </div>
        </div>
        <div
          class="bottom-right"
          style="background-color: #b51c1c"
          :class="{ cancel: isEnd }"
          @click="buy"
        >
          结算
        </div>
      </div>
    </div>
  </div>

  <!-- 弹窗 -->
  <van-popup
    class="popup"
    round
    v-model:show="guize"
    position="bottom"
    :style="{ height: '348px' }"
  >
    <div class="title-box">
      <img
        class="title"
        src="https://static.medsci.cn/public-image/ms-image/092f4e20-a2cb-11ee-a5c7-fd0174f02b25_hdgz(1).png"
        alt=""
      />
    </div>

    <div class="content" style="padding-top: 24px">
      <!-- <div class="item">
        <div>活动时间：</div>
        <div>2023.12.26-2024.1.8</div>
      </div> -->
      <div class="item">
        <div>活动价格：</div>
        <div>购买1件149元，2-3件在原价基础上7折，4件及以上在原价基础上6折</div>
      </div>
      <div class="item">
        <div>有 效 期：</div>
        <div>课程购买后长期有效</div>
      </div>
      <div class="item">
        <div>关于退课：</div>
        <div>线上课程属于虚拟产品，开通后即可学习，不退不换</div>
      </div>
      <!-- <div class="item">
          <div>关于退换：</div>
          <div>线上课程属于虚拟产品，一经购买不支持退换</div>
        </div>
        <div class="item">
          <div>助教微信：</div>
          <div>
            <a href="tel:18917895313">18917895313</a>
          </div>
        </div> -->
    </div>

    <div class="btn-box">
      <img
        @click="guize = false"
        class="btn"
        src="https://static.medsci.cn/public-image/ms-image/d4fafd60-a2d0-11ee-a5c7-fd0174f02b25_know.png"
        alt=""
      />
    </div>
    <div class="content" style="padding-top: 0">
      <div style="text-align: center">更多问题可直接咨询发布此链接的小助手</div>
    </div>
  </van-popup>
  <van-popup
    v-if="isMobile"
    class="popup"
    round
    v-model:show="tishi"
    position="center"
    :style="{ width: '90%', height: '157px' }"
  >
    <!-- <div class="title-box">
      <img
        class="title"
        src="https://static.medsci.cn/public-image/ms-image/092f4e20-a2cb-11ee-a5c7-fd0174f02b25_hdgz(1).png"
        alt=""
      />
    </div> -->

    <div class="content">
      <div
        style="
          text-align: center;
          color: #333333;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.47rem;
          font-weight: 400;
        "
      >
        <img
          style="width: 0.6rem; margin-top: 1px; margin-right: 7px"
          src="https://static.medsci.cn/public-image/ms-image/2dfb0700-a0a8-11ee-a5c7-fd0174f02b25_success.png"
          alt=""
        />
        领取成功，请选择课程~
      </div>
    </div>

    <div class="btn-box">
      <img
        @click="tishi = false"
        class="btn"
        style="width: 8rem"
        src="https://static.medsci.cn/public-image/ms-image/0e001340-a2cf-11ee-a5c7-fd0174f02b25_知道了.png"
        alt=""
      />
    </div>
  </van-popup>
  <van-popup
    v-if="!isMobile"
    class="popup"
    round
    v-model:show="tishi"
    position="center"
    :style="{ width: '90%', height: '248px' }"
  >
    <!-- <div class="title-box">
      <img
        class="title"
        src="https://static.medsci.cn/public-image/ms-image/092f4e20-a2cb-11ee-a5c7-fd0174f02b25_hdgz(1).png"
        alt=""
      />
    </div> -->

    <div class="content">
      <div
        style="
          text-align: center;
          color: #333333;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.47rem;
          font-weight: 400;
        "
      >
        <img
          style="width: 0.6rem; margin-top: 1px; margin-right: 7px"
          src="https://static.medsci.cn/public-image/ms-image/2dfb0700-a0a8-11ee-a5c7-fd0174f02b25_success.png"
          alt=""
        />
        领取成功，请选择课程~
      </div>
    </div>

    <div class="btn-box">
      <img
        @click="tishi = false"
        class="btn"
        style="width: 8rem"
        src="https://static.medsci.cn/public-image/ms-image/0e001340-a2cf-11ee-a5c7-fd0174f02b25_知道了.png"
        alt=""
      />
    </div>
  </van-popup>
</template>

<script>
import { ref, reactive, onMounted, toRefs, watch, computed } from "vue";
import request from "@/utils/request";
import Cookies from "js-cookie";
import { useRouter, useRoute } from "vue-router";
import { Toast } from "vant";
import falseData from "@/components/falseData.vue";

export default {
  name: "HomePage",
  components: {
    falseData,
  },
  setup: () => {
    const isMobile = ref(Boolean);
    const url = ref("");
    const url1 = ref("");
    const url2 = ref("");
    const url3 = ref("");
    const url4 = ref("");
    const url5 = ref("");
    const url6 = ref("");
    const type = ref("");
    const router = useRouter();
    const route = useRoute();
    const isAll = ref(true);
    const show = ref(false);
    const loading = ref(false);
    const active = ref(false);
    const guize = ref(false);
    const tishi = ref(false);
    const priceLoading = ref(false);
    const time = ref(
      new Date("2024/01/08 23:59:59").getTime() - new Date().getTime()
    ); // 还剩多少毫秒
    const isEnd = ref(false);
    const activityPrice = ref(0); // 合计价格
    const preferentialPrice = ref(0); // 已优惠价格
    const courseDiscountPriceList = ref(0); // 合计价格
    const activeList = computed(() =>
      state.list.filter((i) => i.active && i.isBuy==0).map((j) => j.id)
    );
    const userInfo = reactive({
      info: {},
    });
    const state = reactive({
      list: [],
      listAll: [],
    });

    onMounted(async () => {
      if (window.innerWidth < 768) {
        // 移动端样式
        isMobile.value = true;
      } else {
        isMobile.value = false;
        // PC端样式
      }
      document.title = "好课福利狂欢";
      url5.value =
        "https://static.medsci.cn/public-image/ms-image/0fa20290-a3c3-11ee-a5c7-fd0174f02b25_study.png";
      url6.value =
      "https://static.medsci.cn/public-image/ms-image/23e522f0-a3c8-11ee-a5c7-fd0174f02b25_ygm_btn.png";
      url.value =
        "https://static.medsci.cn/public-image/ms-image/7d9c5e30-a2c9-11ee-a5c7-fd0174f02b25_元旦-心血管切图(1).png";
      url1.value =
        "https://static.medsci.cn/public-image/ms-image/d0044ba0-a2ca-11ee-a5c7-fd0174f02b25_xxggz_btn(1).png";
      url2.value =
        "https://static.medsci.cn/public-image/ms-image/d1415820-a2cd-11ee-a5c7-fd0174f02b25_xxg_btn(1).png";
      url3.value =
        "https://static.medsci.cn/public-image/ms-image/9c60df80-a2ec-11ee-a5c7-fd0174f02b25_选中3.png";
      url4.value =
        "https://static.medsci.cn/public-image/ms-image/f7161630-a0a2-11ee-a5c7-fd0174f02b25_shikan2.png";
      // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      // state.msg = res.data;
      wxShare({
        title: "好课福利狂欢",
        summary: "心血管精选系列课限时活动中",
        thumb:
          "https://static.medsci.cn/public-image/ms-image/f10a9d40-a095-11ee-a5c7-fd0174f02b25_share3.png",
      });
      if (time.value < 0) {
        isEnd.value = true;
      }
      // wxShare({
      //   title: '【梅斯医学】钜惠双11，好课折上折！',
      //   summary: '4大爆款精品课降价还打折，此时不下手更待何时？',
      //   thumb: 'https://static.medsci.cn/public-image/ms-image/c6738650-360c-11ec-8e2f-1389d01aad85_icon9.png'
      // })
      getInit();
    });

    // const getInit = async () => {
    //   const res = await request.post("/activity/receptionPageList", {
    //     classify: 2,
    //     courseType: 0,
    //     // "department": 20653,
    //     department: 20611,
    //     pageIndex: 0,
    //     pageSize: 4,
    //     projectId: 1,
    //     getSingleCount: 1,
    //   });
    //   state.listAll = res.data.map((i) => {
    //     return {
    //       ...i,
    //       active: false,
    //     };
    //   });
    //   if (!isAll.value) {
    //     state.list = state.listAll;
    //   } else {
    //     state.list = state.listAll.slice(0, 4);
    //   }
    //   console.log(state.list, "state.list");
    // };
    const getInit = async () => {
      const userInfos = Cookies.get('userInfo')
      const res = await request.post("/activity/course", {
        department: "cardiovascular",
        category:"new-year-day",
        userId: userInfos ? JSON.parse(userInfos).plaintextUserId : '',
      });
      state.listAll = res.result.map((i) => {
        return {
          ...i,
          active: false,
        };
      });
      if (!isAll.value) {
        state.list = state.listAll;
      } else {
        state.list = state.listAll.slice(0, 4);
      }
      // console.log(state.list, "state.list");
    };
    const getCourse = () => {
      console.log(isAll.value, "isAll.value");
      isAll.value = !isAll.value;
      if (!isAll.value) {
        state.list = state.listAll;
      } else {
        state.list = state.listAll.slice(0, 4);
      }
      active.value = state.list.every(item => item.active);
    };

    const wxShare = async (sub) => {
      const apiUrl = window.location.href.split("#")[0];
      // console.log(apiUrl)
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl,
        },
      });
      wx.config(res.data);
      wx.error(function (error) {
        console.log(error);
      });
      wx.ready(function () {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxgNew",
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/9/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/9/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxgNew",
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/9/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/9/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    };

    // 选课
    const getClass = (item) => {
      if (priceLoading.value) {
        return;
      }
      item.active = !item.active;
      active.value = state.list.every(item => item.active);
      getCount();
    };

    const getCount = async () => {
      priceLoading.value = true;
      const res = await request.post("/activity/course/price", {
        ids: activeList.value,
        department: "cardiovascular",
        category:"new-year-day",
      });
      priceLoading.value = false;
      activityPrice.value = res.result.activityPrice;
      courseDiscountPriceList.value = res.result.courseDiscountPriceList;
      preferentialPrice.value = res.result.preferentialPrice;
    };

    const allInOne = () => {
      active.value = !active.value;
      state.list.forEach((item, index) => {
        state.list[index].active = active.value;
      });
      if (!active.value) {
        state.list.forEach((item) => {
          item.active = false;
        });
      }
      getCount();
    };

    const getQueryVariable = (variable) => {
      const query = window.location.search.substring(1);
      const vars = query.split("&");
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split("=");
        if (pair[0] === variable) {
          return pair[1];
        }
      }
      return false;
    };

    const buy = async () => {
      // console.log(isEnd.value);
      // if (isEnd.value) {
      //   return;
      // }
      // console.log(isEnd.value);
      // const userInfos = Cookies.get('userInfo')

      const userInfos = {
        avatar:
          "https://img.medsci.cn/20220302/9e0e9949d639494e81c456655f27fde4/21b2b6a50ff44351a713c4b5cf649508.jpg",
        mobile: "16621177880",
        plaintextUserId: 6809063,
        projectCode: "MSYX",
        projectId: 1,
        projectName: "梅斯医学",
        projectType: 0,
        projectUserStatus: 1,
        realName: "ww",
        redirectUrl:
          "https://www.medsci.cn?sso_sessionid=609a6809063_76ea6cac1c6b48b5b677efc659979941",
        token: {
          accessToken:
            "eyJhbGciOiJSUzI1NiJ9.eyJzdWIiOiJtczMwMDAwMDAxNzI0M…Jo40X24WmR3U9EupaGA3liM-GaWcOYD-B7RenFxqyuoqtsJ4g",
          accessTokenExpireTime: 630720000,
          refreshToken:
            "eyJhbGciOiJSUzI1NiJ9.eyJzdWIiOiJtczMwMDAwMDAxNzI0M…uCVcrrHjivpdDdVtFspQKzWWNnRA3gjN_IpeeQoTvA9yTOFpI",
        },
        userId: "609a6809063",
        userName: "ms3000000172421950",
        userStatus: 1,
        wechatOpenid: "owfSV6GQwbaWprWGsXUnfqyfKwHI",
      };
      if (!userInfos) {
        const ssoSessionid =
          route.query.sso_sessionid || getQueryVariable("sso_sessionid");
        if (ssoSessionid) {
          const res = await request.post("/medsciUser/getLoginUserInfoBySid", {
            sessionid: ssoSessionid,
          });
          userInfo.info = res.data;
          Pay();
        } else {
          Login();
        }
      } else {
        // userInfo.info = JSON.parse(userInfos)
        userInfo.info = userInfos;
        console.log(3);
        Pay();
      }
    };

    // 跳转登录
    const Login = () => {
      addLoginDom();
    };

    // 试看
    const testPlay = (item) => {
      if(item.isBuy != 0){
        window.location.href = item.url
        // return
      }else{
        window.location.href = item.url
      }
      // if (item.encodeId == "368fa230625") {
      //   window.location.href = "https://class.medsci.cn/study/9a26ee961c9";
      //   return;
      // }
      // if (item.encodeId) {
      //   window.location.href = `https://class.medsci.cn/study/${item.encodeId}`;
      // }
    };

    // 购买
    const Pay = async () => {
      if (!state.list.filter((i) => i.active).length) {
        Toast("未选择课程");
        return;
      }
      // console.log(state.list.filter((i) => i.active));
      sessionStorage.setItem(
        "ms_class_list",
        JSON.stringify(state.list.filter((i) => i.active && i.isBuy == 0))
      );
      sessionStorage.setItem(
        "ms_class_type",
        "cardiovascular"
      );
      sessionStorage.setItem("ms_class_price", activityPrice.value);
      sessionStorage.setItem("ms_class_discount", JSON.stringify(courseDiscountPriceList.value));
      console.log(1);

      router.push("/orderYd");

      // if (res.status == 200) {
      //   Toast.success(res.message)
      //   localStorage.setItem("zen0818", "1")
      // } else {
      //   show.value = true
      // }
    };

    const link = () => {
      const u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      } else {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      }
    };

    const goPage = () => {
      // window.scrollTo(0, 1200)
      tishi.value = true;
    };

    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time);
        return [val[0], val[1]];
      } else {
        return [0, time];
      }
    };

    const showGuize = () => {
      guize.value = true;
    };
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    };

    return {
      ...toRefs(state),
      isEnd,
      loading,
      priceLoading,
      userInfo,
      show,
      active,
      guize,
      time,
      activityPrice,
      preferentialPrice,
      allInOne,
      url,
      isAll,
      type,
      tishi,
      url1,
      url2,
      url3,
      url4,
      url5,
      url6,
      isMobile,

      Login,
      buy,
      Pay,
      testPlay,
      getQueryVariable,
      wxShare,
      link,
      getClass,
      showGuize,
      formatTime,
      onFinish,
      goPage,
      getCourse,
      courseDiscountPriceList
    };
  },
};
</script>

<style scoped lang="stylus">
.box {
  font-size 16px
  position relative
  max-width 750Px
  margin 0 auto
  margin-bottom: env(safe-area-inset-bottom);
  .image {
    width 100%
  }

  .guize {
    width 79px
    position absolute
    top 19px
    right 0
  }
  .guize1 {
    width 82%
    position absolute
    top 23.72%
    right 9.3%
  }

  .img-back{
    height 700.5px
    width 350.5px
    background-color #fffef7
    border-radius 8px
    // background-image: url("https://static.medsci.cn/public-image/ms-image/b819c3c0-a2e9-11ee-a5c7-fd0174f02b25_kcxz.png")
    // background-size: 100% 100%
    position absolute
    // top 935.5px
    // bottom 100px
    top 25.3rem
    left 50%
    transform: translate(-50%)
    z-index 100
    box-sizing border-box
    // padding-top 87px
    padding-top 30px
    a{
        display flex
        justify-content center
        margin-top 9px
    }
    .wechat {
      width 90%

    }
    .item {
      display: flex
      height 130px
      align-items: center
      margin 0 17px
      border-bottom 1px solid rgba(119,50,11,0.2)
      position relative
      font-size 13px
      color #723107
      line-height 20px
      .click-price {
        display flex
        margin-top 10px
        .click {
          width 70px
          height 25px
          margin-right 12.5px
        }
        .price {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/2c748cf0-361d-11ec-8e2f-1389d01aad85_price.png')
          text-decoration line-through
        }
        .price2 {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/69366530-a0a3-11ee-a5c7-fd0174f02b25_yuanjia2.png')
          text-decoration line-through
        }
      }

      &.lastss {
        border-bottom none
      }
      .left-btn {
        width 38px
        text-align left
        .no-active {
          width 22px
          height 22px
          border-radius: 50%
          border 1px solid rgba(119,50,11,0.2)
        }
        .active {
          width 23px
          height 23px
        }
      }
      .doctor {
        display: flex
        .cover {
          object-fit: cover
          width 77px
          height 100px
          margin-right 15px
          border-radius 5px
        }
        .doctor-info {

        }
      }
    }
  }
  .img-back1{
    // height 700.5px
    width 350.5px
    background-color #fffef7
    border-radius 8px
    // background-image: url("https://static.medsci.cn/public-image/ms-image/b819c3c0-a2e9-11ee-a5c7-fd0174f02b25_kcxz.png")
    // background-size: 100% 100%
    position absolute
    // top 935.5px
    // bottom 100px
    top 25.3rem
    left 50%
    transform: translate(-50%)
    z-index 100
    box-sizing border-box
    // padding-top 87px
    padding-top 30px
    a{
        display flex
        justify-content center
        margin-top 9px
    }
    .wechat {
      width 90%
      margin-bottom 0.5rem
    }
    .item {
      display: flex
      height 130px
      align-items: center
      margin 0 17px
      border-bottom 1px solid rgba(119,50,11,0.2)
      position relative
      font-size 13px
      color #723107
      line-height 20px
      .click-price {
        display flex
        margin-top 10px
        .click {
          width 70px
          height 25px
          margin-right 12.5px
        }
        .price {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/2c748cf0-361d-11ec-8e2f-1389d01aad85_price.png')
          text-decoration line-through
        }
        .price2 {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/69366530-a0a3-11ee-a5c7-fd0174f02b25_yuanjia2.png')
          text-decoration line-through
        }
      }

      &.lastss {
        border-bottom none
      }
      .left-btn {
        width 38px
        text-align left
        .no-active {
          width 22px
          height 22px
          border-radius: 50%
          border 1px solid rgba(119,50,11,0.2)
        }
        .active {
          width 23px
          height 23px
        }
      }
      .doctor {
        display: flex
        .cover {
          object-fit: cover
          width 77px
          height 100px
          margin-right 15px
          border-radius 5px
        }
        .doctor-info {

        }
      }
    }
  }
 
  .img-back-pc{
    height 680.5px
    width 435.5px
    background-color #fffef7
    border-radius 8px
    // background-image: url("https://static.medsci.cn/public-image/ms-image/b819c3c0-a2e9-11ee-a5c7-fd0174f02b25_kcxz.png")
    // background-size: 100% 100%
    position absolute
    // top 915.5px
    // bottom 100px
    top 32rem
    left 50%
    transform: translate(-50%)
    z-index 100
    box-sizing border-box
    // padding-top 87px
    padding-top 40px
    a{
        display flex
        justify-content center
        margin-top 19px
    }
    .wechat {
      width 90%
    }
    .item {
      display: flex
      height 130px
      align-items: center
      margin 0 17px
      border-bottom 1px solid rgba(119,50,11,0.2)
      position relative
      font-size 13px
      color #723107
      line-height 20px
      .click-price {
        display flex
        margin-top 10px
        .click {
          width 70px
          height 25px
          margin-right 12.5px
        }
        .price {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/2c748cf0-361d-11ec-8e2f-1389d01aad85_price.png')
          text-decoration line-through
        }
        .price2 {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/69366530-a0a3-11ee-a5c7-fd0174f02b25_yuanjia2.png')
          text-decoration line-through
        }
      }

      &.lastss {
        border-bottom none
      }
      .left-btn {
        width 38px
        text-align left
        .no-active {
          width 22px
          height 22px
          border-radius: 50%
          border 1px solid rgba(119,50,11,0.2)
        }
        .active {
          width 23px
          height 23px
        }
      }
      .doctor {
        display: flex
        .cover {
          object-fit: cover
          width 77px
          height 100px
          margin-right 15px
          border-radius 5px
        }
        .doctor-info {

        }
      }
    }
  }
  .img-back-pc1{
    // height 680.5px
    width 435.5px
    background-color #fffef7
    border-radius 8px
    // background-image: url("https://static.medsci.cn/public-image/ms-image/b819c3c0-a2e9-11ee-a5c7-fd0174f02b25_kcxz.png")
    // background-size: 100% 100%
    position absolute
    // top 915.5px
    // bottom 100px
    top 31.4rem
    left 50%
    transform: translate(-50%)
    z-index 100
    box-sizing border-box
    // padding-top 87px
    padding-top 40px
    a{
        display flex
        justify-content center
        margin-top 22px
        margin-bottom 22px
    }
    .wechat {
      width 90%
    }
    .item {
      display: flex
      height 130px
      align-items: center
      margin 0 17px
      border-bottom 1px solid rgba(119,50,11,0.2)
      position relative
      font-size 13px
      color #723107
      line-height 20px
      .click-price {
        display flex
        margin-top 10px
        .click {
          width 70px
          height 25px
          margin-right 12.5px
        }
        .price {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/2c748cf0-361d-11ec-8e2f-1389d01aad85_price.png')
          text-decoration line-through
        }
        .price2 {
          width 85px
          height 25px
          text-align center
          line-height 25px
          background-size 100% 100%
          background-image url('https://static.medsci.cn/public-image/ms-image/69366530-a0a3-11ee-a5c7-fd0174f02b25_yuanjia2.png')
          text-decoration line-through
        }
      }

      &.lastss {
        border-bottom none
      }
      .left-btn {
        width 38px
        text-align left
        .no-active {
          width 22px
          height 22px
          border-radius: 50%
          border 1px solid rgba(119,50,11,0.2)
        }
        .active {
          width 23px
          height 23px
        }
      }
      .doctor {
        display: flex
        .cover {
          object-fit: cover
          width 77px
          height 100px
          margin-right 15px
          border-radius 5px
        }
        .doctor-info {

        }
      }
    }
  }
  .last {
    position fixed
    left 0
    bottom 0
    width 100%
    max-width 1000px
    z-index 101
    .top {
      height 40px
      display: flex
      background-color #FFC700
      display: flex
      align-items: center
      .img {
        width 50px
        height 32px
        margin 0 5px 0 8px
      }
      .van-count-down {
        display: flex
        align-items: center
      }
      .block {
        display: flex
        &-item {
          background-color #ffffff
          color #333333
          width 20px
          height 25px
          text-align center
          font-size 20px
          font-weight blod
          line-height 25px
          border-radius 5px
          margin 0 2px
        }
      }
      .colon {
        color #333333
        font-size 8px
        margin 0 1px
        &.a {
          margin-top: 10px
        }
        .mill {
          height 12px
          line-height 12px
        }
      }
    }
    .bottom {
      height 50px
      display: flex
      &-left {
        background-color #ffffff
        width calc(100vw - 155px)
        color rgba(114,49,7,1)
        display: flex
        align-items: center
        .radio {
          width 88px
          display: flex
          font-size 13px
          align-items: center
          .no-active {
            width 17px
            height 17px
            border-radius: 50%
            border 1px solid rgba(119,50,11,0.2)
            margin 0 7px 0 15px
          }
          .active {
            width 18px
            height 18px
            margin 0 8px 0 15px
          }
        }
        .info {
          font-size 13px
          .discount {
            color rgba(142,142,142,1)
          }
        }
      }
      &-right {
        background-color rgba(255,95,0,1)
        width 155px
        color #ffffff
        text-align center
        height 50px
        line-height 50px
        font-size 19px
        &.cancel {
          background-color #cccccc
        }
      }
    }
  }
  .last1 {
    position fixed
    // left 106.5px
    // left 15.57%
    bottom 0
    width 12.51rem
    // max-width 1000px
    z-index 101
    .top {
      height 40px
      display: flex
      background-color #FFC700
      display: flex
      align-items: center
      .img {
        width 50px
        height 32px
        margin 0 5px 0 8px
      }
      .van-count-down {
        display: flex
        align-items: center
      }
      .block {
        display: flex
        &-item {
          background-color #ffffff
          color #333333
          width 20px
          height 25px
          text-align center
          font-size 20px
          font-weight blod
          line-height 25px
          border-radius 5px
          margin 0 2px
        }
      }
      .colon {
        color #333333
        font-size 8px
        margin 0 1px
        &.a {
          margin-top: 10px
        }
        .mill {
          height 12px
          line-height 12px
        }
      }
    }
    .bottom {
      height 50px
      display: flex
      &-left {
        background-color #ffffff
        width calc(100vw - 155px)
        color rgba(114,49,7,1)
        display: flex
        align-items: center
        .radio {
          width 88px
          display: flex
          font-size 13px
          align-items: center
          .no-active {
            width 17px
            height 17px
            border-radius: 50%
            border 1px solid rgba(119,50,11,0.2)
            margin 0 7px 0 15px
          }
          .active {
            width 18px
            height 18px
            margin 0 8px 0 15px
          }
        }
        .info {
          font-size 13px
          .discount {
            color rgba(142,142,142,1)
          }
        }
      }
      &-right {
        background-color rgba(255,95,0,1)
        width 155px
        color #ffffff
        text-align center
        height 50px
        line-height 50px
        font-size 19px
        &.cancel {
          background-color #cccccc
        }
      }
    }
  }
}

.van-popup {
  text-align center
  .title-box {
    text-align center
  }
  .btn-box {
    text-align center
  }
  .title {
    width 85px
    // margin-top 20px
    margin-top 34px
  }
  .content {
    font-size 13px
    box-sizing border-box
    // padding 15px 20px 25px
    padding 40px 20px 25px
    // color #723107
    color #333333
    .item {
      display: flex
      margin 5px 0
      div {
        &:first-child {
          flex 0 0 80px
        }
      }
      a {
        color #723107
      }
    }
  }

  .btn {
    // width 256px
    width 340px
    margin 0 auto
  }
}
</style>
