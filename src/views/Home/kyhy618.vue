<template>
  <div class="box">
    <false-data ></false-data>

    <img class="image" src="https://static.medsci.cn/public-image/ms-image/39c88340-f5c4-11ec-a1b8-6123b3ff61ea_kyhy618.png" alt="">
    <div class="bottom">
        <div class="left">
           <img src="https://static.medsci.cn/public-image/ms-image/162c6330-e6f0-11ec-a1b8-6123b3ff61ea_ky_left.png" alt="">
        </div>
        <div class="right" @click="buy">
          <img src="https://static.medsci.cn/public-image/ms-image/162c6330-e6f0-11ec-a1b8-6123b3ff61ea_ky_righ.png" alt="">
        </div>
    </div>
  </div>

  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    @select="onSelect"
    cancel-text="取消"
    close-on-click-action
  />
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper">
      <div @click.stop="">
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="">
      </div>
    </div>
  </van-overlay>
  <van-overlay :show="showQrcode" @click="showQrcode = false" center>
      <div class="dialog-wrapper" @click.stop="">
        <img class="img1" src="https://static.medsci.cn/public-image/ms-image/cb18e310-e6f3-11ec-a1b8-6123b3ff61ea_kyhy618_code.png" alt="">
      </div>
    </van-overlay>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import falseData from '@/components/falseData.vue';
import msSwiper from "./msSwiper.vue"
import { baseUrl } from "@/utils/config"
import { baseUrlSix } from "@/utils/config618"

export default ({
  name: 'kyhy618',
  components: {
    falseData,
    msSwiper
  },
  setup: () => {
    const router = useRouter()
    const route = useRoute()

    const loading = ref(false);
    const active = ref(false)
    const guize = ref(false)
    const time = ref(new Date("2022/03/16 14:00:00").getTime() - new Date().getTime())   // 还剩多少毫秒

    const isBay = ref(false);
    const way = ref('')
    const app_order_id = ref('')
    const MSpaymentStatus = ref("");
    const wxOpenId = ref("");
    const show = ref(false);
    const showImg = ref(false);
    const showQrcode = ref(false);
    const qrCodeUrlImg = ref("");
    const accessAppId = ref('college')
    const isEnd = ref(false)
    const actions = [
      { name: '支付宝支付' },
      { name: '微信支付' },
    ];
    const onSelect = (item) => {
      show.value = false;
      MobilePay(item.name == '支付宝支付' ? 'ALI' : 'WX')
    };

    const userInfo = reactive({
      info: {},
    })
    const state = reactive({
      msg: {}
    })

    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    }
    
    onMounted(async () => {
      document.title = '爱学618，科研会员福利购！'
      wxShare({
        title: "爱学618，科研会员福利购！",
        summary: "现在加入会员赠送3个月会员时长！",
        thumb: "https://static.medsci.cn/public-image/ms-image/23dc0240-e661-11ec-a1b8-6123b3ff61ea_kyhy618_icon.png"
      })
      // getInit()
      // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      // state.msg = res.data;
      // if(state.msg && state.msg.activityEndTime) {
      //   state.msg.activityEndTime = state.msg.activityEndTime.replace(/-/g, '/');
      //   time.value = new Date(state.msg.activityEndTime).getTime() - new Date().getTime()
      // }
      // if (time.value < 0) {
      //   isEnd.value = true
      // }
      let userAgent = navigator.userAgent;
      if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
        if(route.query.openId || getQueryVariable("openId")) {
          buy()
        }
      }
    });


    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      // console.log(apiUrl)
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: 'https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kyhy618',
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: 'https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kyhy618',
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }


    const getAuth = () => {
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
        way.value = "WX"
        //微信扫码
        const AppId = 'wx9096048917ec59ab'
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href)
          const RedirectUrl = encodeURIComponent(
              `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          )
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`
        } else {
          wxOpenId.value = openId
        }
      } else if (userAgent != null && userAgent.indexOf('AlipayClient') > -1) {
        way.value = "ALI"
      } else {
        way.value = "ALL"
      }
    }

    const buy = async () => {
      // if (isEnd.value) {
      //   return
      // }
      getAuth()
      const userInfos = Cookies.get('userInfo');
      Toast.loading({
        duration: 0,
        message: '提交中...',
        loadingType: 'spinner',
      });
      if (!userInfos) {
        const sso_sessionid = route.query.sso_sessionid || getQueryVariable("sso_sessionid")
        if (sso_sessionid) {
          const res = await request.post('/medsciUser/getLoginUserInfoBySid', { sessionid: sso_sessionid });
          userInfo.info = res.data;
          Pay()
        } else {
          Login()
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom()
        }
        Pay()
      }
    }

    // 跳转登录
    const Login = () => {
      addLoginDom()
    }

    const testPlay = (item) => {
      if (item.link) {
        window.location.href = item.link
      }
    }

    const Pay = async () => {
      // const { userId, userName, realName, mobile, email, plaintextUserId } = userInfo.info;
      const res = await request.post(baseUrlSix + "/medsci-activity/pay/member-card", {
        user_id: userInfo.info.plaintextUserId,
        ciphertext_user_id: userInfo.info.userId,
        mobile: userInfo.info.mobile,
        user_name: userInfo.info.userName,
        real_name: userInfo.info.realName,
        email: userInfo.info.email,
        type: 'scientific_card',
      });
      if(res.code !== 200) {
        Toast.clear()
        Toast(res.msg || '请稍后再试')
      }
      app_order_id.value = res.data.data;
      Toast.clear()
      selectPayWay()
    }

    const link = () => {
      var u = navigator.userAgent;
      const isAndroid =  u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href = "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"
      } else {
        window.location.href = "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"
      }
    }
    
    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time)
        return [val[0], val[1]]
      } else {
        return [0, time]
      }
    }

    const showGuize = () => {
      guize.value = true
    }
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    }


    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == "ALL") {
          show.value = true
        } else {
          MobilePay(way.value)
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + "/payment/pay/merge_qrcode", {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value
        })
        const { qrCodeUrl } = res.data;
        if (qrCodeUrl) {
          showImg.value = true
          qrCodeUrlImg.value = qrCodeUrl
        }

        const inter = setInterval(() => {
          getStatus()
          if (MSpaymentStatus.value == 'PAID') {
            showImg.value = false
            memberCardStatus()
            clearInterval(inter)
          }
        }, 3000);
      }
      isBay.value = false;
    }
    const memberCardStatus = async () => {
      const res = await request.post(baseUrlSix + "/medsci-activity/pay/member-card-status", {
        order_id: app_order_id.value,
        type: 'scientific_card',
      });
      console.log(res, '下单成功')
    }
    const getStatus = async () => {
      const res = await request.get(baseUrl + "/payment/pay/query", {
        params: {
          appOrderId: app_order_id.value,
        }
      })

      const { paymentStatus } = res.data
      MSpaymentStatus.value = paymentStatus
      if (paymentStatus == "PAID") {
        showQrcode.value = true
        Toast("支付成功")
      }
    }

    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + "/payment/pay/build", {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: "MEDSCI_WEB",
        payType: way.value == 'ALL' ? 'MWEB' :  way.value == 'WX' ? 'JSAPI' : 'NATIVE'
      })

      if (res2.code != 'SUCCESS') {
        Toast(res2.msg)
        return
      }
      const res3 = await request.post(baseUrl + "/payment/pay/order", {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value
      })

      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement('div')
        div.innerHTML =  aliH5.html
        document.body.appendChild(div)
        document.forms[0].submit()
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl
      }
      if (wechatH5) {
        window.location.href = wechatH5.h5Url
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          'getBrandWCPayRequest',
          {
              appId: wechatJsapi.appId,
              timeStamp: wechatJsapi.timeStamp,
              nonceStr: wechatJsapi.nonceStr,
              package: wechatJsapi.packageStr,
              signType: wechatJsapi.signType,
              paySign: wechatJsapi.paySign,
          },
          function(res) {
            if (res.err_msg == 'get_brand_wcpay_request:ok') {
              Toast.success('支付成功！')
              showQrcode.value = true
            }
          }
      )
      }
    }

    return {
      ...toRefs(state),
      loading,
      userInfo,
      active,
      guize,
      time,

      actions,
      show,
      showImg,
      showQrcode,
      qrCodeUrlImg,
      isEnd,

      Login,
      buy,
      Pay,
      testPlay,
      getQueryVariable,
      wxShare,
      link,
      showGuize,
      formatTime,
      onFinish,
      onSelect,
      memberCardStatus
    };
  }
})
</script>

<style scoped lang="stylus">
  .box {
    position relative
    max-width 10rem
    margin 0 auto
    margin-bottom: env(safe-area-inset-bottom);
    padding-bottom: 1.6rem;
    font-size: 0;
    .image {
      width 100% 
    }

    .couse {
      position: absolute;
      top 53px;
      right: 5px
      width 200px
    }

    .guize {
      width 79px
      position absolute
      top 142px
      right 0
    }

    .img-back{
      height 151px
      width 100%
      background-size: 100% 100%
      position absolute
      top 992px
      left 50%
      transform: translate(-50%)
      z-index 100
      box-sizing border-box
      
    }
    .last {
      position fixed
      right 0
      left 0
      bottom 0
      margin auto
      width 10rem
      .top {
        height 40px
        display: flex
        background-color #FFD893
        display: flex
        align-items: center
        .img {
          width 50px
          height 32px
          margin 0 5px 0 8px
        }
        .van-count-down {
          display: flex
          align-items: center
        }
        .block {
          display: flex
          &-item {
            background-color #ffffff
            color #333333
            width 20px
            height 25px
            text-align center
            font-size 20px
            font-weight blod
            line-height 25px
            border-radius 5px
            margin 0 2px
          }
        }
        .colon {
          color #333333
          font-size 8px
          margin 0 1px
          &.a {
            margin-top: 10px
          }
          .mill {
            height 12px
            line-height 12px
          }
        }
      }
      .bottom {
        height 50px
        display: flex
        &-left {
          background-color #ffffff
          width calc(10rem - 169px)
          color rgba(114,49,7,1)
          display: flex
          align-items: center
          justify-content: center
          box-sizing border-box
          font-size 12px
          .Num {
            color #010101
            font-weight 500
          }
        }
        &-right {
          background-color rgba(206,48,49,1)
          width 169px
          color #ffffff
          text-align center
          height 50px
          line-height 50px
          font-size 15px
          font-weight 500
        }
      }
    }
  }

  .van-popup {
    text-align center
    .title-box {
      text-align center
    }
    .btn-box {
      text-align center
    }
    .title {
      width 85px
      margin-top 20px
    }
    .content {
      font-size 13px
      box-sizing border-box
      padding 15px 20px 25px
      color #723107
      .item {
        display: flex
        margin 5px 0
        div {
          &:first-child {
            flex 0 0 80px
          }
        }
        a {
          color #723107
        }
      }
    }

    .btn {
      width 256px
      margin 0 auto
    }
  }

  .wrapper {
    height 100%
    display flex
    justify-content center
    align-items center
    flex-wrap wrap
    .text {
      font-size 18px
      color #ffffff
      width 100%
      text-align center
    }
  }
  .bottom{
      position: fixed;
      left: 0;
      bottom: 0;
      width: 10rem;
      height: 1.6rem;
      display: flex;
      align-items: center;
      z-index: 1;
      right: 0;
      margin: 0 auto;
      background-color: #fff;
  }
  .left{
      width: 5.68rem;
      height: 1.6rem;
      font-size: 0;
      img{
        width: 100%;
        height: 100%;
      }
  }
  .right{
      width: 4.32rem;
      height: 1.6rem;
      font-size: 0;
      img{
        width: 100%;
        height: 100%;
      }
  }
  // 弹窗
  .dialog-wrapper{
    width: 5.93rem;
    height: 8.89rem;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 0.45rem;
    overflow: hidden;
    .img1{
      width: 5.93rem;
      height: 8.89rem;
      // border-radius: 0.45rem;
    }
    .img2{
      width 3.51rem;
      height: 3.43rem;
      position: absolute;
      left: 0.59rem;
      top: 2.97rem;
    }
    .img3{
      width 3.51rem;
      height: 3.43rem;
      position: absolute;
      right: 0.59rem;
      top: 2.97rem;
    }
  }
</style>
