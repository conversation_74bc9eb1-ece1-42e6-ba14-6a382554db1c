<template>
  <div class="box">
    <img
      class="img"
      src="https://static.medsci.cn/public-image/ms-image/d3466d10-97ef-11ee-926a-11c8565c20b2_introduce.png"
      alt=""
    />
    <!-- <div class="last"> -->
      <div class="bottom" @click="getBtn">
        <img style="width:100%" src="https://static.medsci.cn/public-image/ms-image/56141d70-97f3-11ee-926a-11c8565c20b2_btn1.png" alt="" />
      </div>
    <!-- </div> -->
    <!-- <div class="guwen1" @click="showDialog"></div>
    <div class="guwen2" @click="showDialog"></div> -->
  </div>
  <van-overlay :show="dialogVisible" @click="show = false">
    <div
      class="wrapper"
      @click.stop
      style="display: flex; flex-direction: column"
    >
      <div class="block" style="width: 8.8rem; position: relative">
        <!-- <div style="position: absolute; top: 23%; left: 24%" v-if="isMobile">
          <img src="@/assets/code.png" />
        </div>
        <div style="position: absolute; top: 28%; left: 37%" v-else>
          <img src="@/assets/code.png" />
        </div> -->
        <img
          style="width: 100%; height: 100%"
          src="@/assets/dialog.png"
          alt=""
        />
        <img
        style="width: 50%; position: absolute; bottom: 31%; left: 24.8%"
          src="@/assets/code.png"
          alt=""
        />
        <img
          @click="close"
          style="width: 8%; position: absolute; bottom: -9%; left: 46%"
          src="@/assets/close.png"
          alt=""
        />
      </div>
    </div>
  </van-overlay>

  <!-- <van-action-sheet v-model:show="show" :actions="actions" @select="onSelect" cancel-text="取消" close-on-click-action />
  <van-overlay :show="showImg" @click="showImg = true">
    <div class="wrapper" @click.stop>
      <div>
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="" />
      </div>
    </div>
  </van-overlay> -->
</template>

<script>
import { ref, reactive, onMounted, toRefs } from "vue";
import request from "@/utils/request";
import Cookies from "js-cookie";
import { useRouter, useRoute } from "vue-router";
import { Toast } from "vant";
import falseData from "@/components/falseData.vue";
import msSwiper from "./msSwiper.vue";
import { baseUrl } from "@/utils/config";
import { baseUrlFreeVip } from "@/utils/configFreeVip";
import { msBaseUrl } from "@/utils/sanofiConfig";

// import dayjs from "dayjs";

export default {
  name: "openCourse",
  components: {
    falseData,
    msSwiper,
  },
  setup: () => {
    const url = ref('')
    const type = ref('')
    const isMobile = ref(Boolean)
    const router = useRouter();
    const route = useRoute();
    const timing = ref(false);
    const curVal = ref("1");
    const vipType = ref("");
    const loading = ref(false);
    const active = ref(false);
    const guize = ref(false);
    const time = ref(
      new Date("2032/02/20 23:59:59").getTime() - new Date().getTime()
    ); // 还剩多少毫秒

    const isBay = ref(false);
    const way = ref("");
    const app_order_id = ref("");
    const MSpaymentStatus = ref("");
    const wxOpenId = ref("");
    const show = ref(true);
    const showImg = ref(false);
    const qrCodeUrlImg = ref("");
    const accessAppId = ref("college");
    const isEnd = ref(false);
    const dialogVisible = ref(false);
    const actions = [{ name: "支付宝支付" }, { name: "微信支付" }];
    const onSelect = (item) => {
      show.value = false;
      MobilePay(item.name == "支付宝支付" ? "ALI" : "WX");
    };

    const userInfo = reactive({
      info: {},
    });
    const state = reactive({
      msg: {},
    });
    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    };
    onMounted(async () => {
      if(window.innerWidth < 768) {
      // 移动端样式
      isMobile.value = true
    } else {
      isMobile.value = false
      // PC端样式
    } 
    document.title = "科研支持服务介绍";
      //   console.log( dayjs(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss'));
      //   console.log(new Date("2023/08/19 00:00:00").getTime()); // 8/19
      //   console.log(new Date("2023/08/20 24:00:00").getTime()); // 8/20
      // if(!route.query.type){
      //   type.value = "journal"
      //   document.title = '期刊会员'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/d7461690-9632-11ee-926a-11c8565c20b2_jounal.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "期刊会员",
      //   //   summary: `月度会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }else if(route.query.type == 'fund'){
      //   type.value = "fund"
      //   document.title = '基金会员'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/34edcdc0-9664-11ee-926a-11c8565c20b2_fund.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "基金会员",
      //   //   summary: `心内科课程会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }else if(route.query.type == 'open'){
      //   type.value = "open"
      //   document.title = '公开课会员'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/17eb6470-9665-11ee-926a-11c8565c20b2_open.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "公开课会员",
      //   //   summary: `心内科课程会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }else if(route.query.type == 'guide'){
      //   type.value = "guide"
      //   document.title = '指南会员'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/5dea3f00-9665-11ee-926a-11c8565c20b2_article.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "临床指南会员",
      //   //   summary: `心内科课程会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }else if(route.query.type == 'class1'){
      //   type.value = "class1"
      //   document.title = '科研精品课会员'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/a45e2280-9665-11ee-926a-11c8565c20b2_class1.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "科研精品课会员",
      //   //   summary: `心内科课程会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }else if(route.query.type == 'class2'){
      //   type.value = "class2"
      //   document.title = '心血管精品课'
      //   url.value = 'https://static.medsci.cn/public-image/ms-image/e9d24940-9665-11ee-926a-11c8565c20b2_class2.png'
      //   // const res = await request.post("/activity/memberCardDetail", {id: '****************'})
      //   // state.msg = res.data;
      //   // wxShare({
      //   //   title: "科研精品课会员",
      //   //   summary: `心内科课程会员仅需`+ state.msg.activityPrice +`元`,
      //   //   thumb: "https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"
      //   // })
      // }
      // document.title = "呼吸守护者-共抗混合感染";
      // wxShare({
      //   title: "呼吸守护者-共抗混合感染",
      //   summary: "驰援临床一线公益活动，梅斯医学呼吸科精品课季度会员免费送",
      //   thumb:
      //     "https://static.medsci.cn/public-image/ms-image/0c7ae180-94e7-11ee-926a-11c8565c20b2_share.png",
      // });
      // let userAgent = navigator.userAgent;
      // if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
      //     if (route.query.openId || getQueryVariable("openId")) {
      //         buy()
      //     }
      // }
      //   const end = new Date("2023/8/20").getTime();
      //   const now = new Date().getTime();
      //   if (now - end > 0) {
      //     timing.value = true;
      //   }
      const userInfos = Cookies.get("userInfo");
      // if (!userInfos) {
      //   Login();
      // }
      // 埋点
      request.get(baseUrlFreeVip + "/medsci-activity/visit", {
        params: {
          user_id: userInfo.info.plaintextUserId,
          ciphertext_user_id: userInfo.info.userId,
          event_type: "view",
          type: "new_user_register",
        },
      });
    });

    // const wxShare = async (sub) => {
    //   let apiUrl = window.location.href.split("#")[0];
    //   // console.log(apiUrl)
    //   const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
    //     params: {
    //       url: apiUrl,
    //     },
    //   });
    //   wx.config(res.data);
    //   wx.error(function (error) {
    //     console.log(error);
    //   });
    //   wx.ready(function () {
    //     wx.onMenuShareAppMessage({
    //       // 微信内朋友分享
    //       title: sub.title, // 分享标题
    //       desc: sub.summary, // 分享描述
    //       // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    //       link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/ecard",
    //       imgUrl: sub.thumb, // 分享图标
    //       success: function () {
    //         // 设置成功
    //         //  console.log(window.location.href,'555')
    //       },
    //     });
    //     wx.onMenuShareTimeline({
    //       // 微信朋友圈
    //       title: sub.title, // 分享标题
    //       desc: sub.summary, // 分享描述
    //       // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    //       link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/ecard",
    //       imgUrl: sub.thumb, // 分享图标
    //       success: function () {
    //         // 设置成功
    //         //  console.log(window.location.href,'555')
    //       },
    //     });
    //   });
    // };
    // 用户登陆后 完善信息
    const isLimitComplete = async () => {
      // 完善信息
      const res = await request.get(
        msBaseUrl +
          "/perfectInfo/userInfoStatus?encryptionUserId=" +
          userInfo.info.userId
      );
      if (res.data.isCompleteInfo) {
        getBtn1()
        // Pay();
      } else {
        addPerfectInfoDom();
      }
    };
    const getBtn1 = async () => {
      // Toast.clear();
      dialogVisible.value = true;
    };
    const close = async () => {
      dialogVisible.value = false;
    };

    const getAuth = () => {
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf("MicroMessenger") > -1) {
        way.value = "WX";
        //微信扫码
        const AppId = "wx9096048917ec59ab";
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href);
          const RedirectUrl = encodeURIComponent(
            `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          );
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
        } else {
          wxOpenId.value = openId;
        }
      } else if (userAgent != null && userAgent.indexOf("AlipayClient") > -1) {
        way.value = "ALI";
      } else {
        way.value = "ALL";
      }
    };
    //点击确认领取
    const getBtn = async () => {
          getBtn1()
    };

    // 跳转登录
    const Login = () => {
      addLoginDom();
    };

    const testPlay = (item) => {
      if (item.link) {
        window.location.href = item.link;
      }
    };

    const Pay = async () => {
      // const { userId, userName, realName, mobile, email, plaintextUserId } = userInfo.info;
      let nowTime = new Date().getTime(),
        startTime = new Date("2023/11/07 00:00:00").getTime(),
        endTime = new Date("2023/11/11 23:59:59").getTime();
      // if (nowTime < startTime) {
      //   Toast("活动还没开始，再等等哦");
      //   return false;
      // } else if (nowTime > startTime && nowTime < endTime) {
      //   //   Toast("进行中");
      // } else if (nowTime > endTime) {
      //   Toast("活动已结束，下次再来吧");
      //   return false;
      // }

      // const resData = await request.post(
      //   baseUrlFreeVip + "/medsci-activity/attend-status",
      //   {
      //     order_id: app_order_id.value,
      //     type: "new_user_register",
      //     mobile: userInfo.info.mobile,
      //   }
      // );
      // if (resData.data.status == 0) {

      // let obj = {
      //   1: "scientific_research",
      //   2: "guider",
      //   3: "journal",
      //   4: "nsfc",
      //   5: "breathe",
      //   6: "nerve",
      //   7: "cardiovascular",
      //   8: "skin",
      //   9: "ophthalmology",
      // };
      const res = await request.post(
        baseUrlFreeVip + "/medsci-activity/give/member-card",
        {
          user_id: userInfo.info.plaintextUserId,
          ciphertext_user_id: userInfo.info.userId,
          mobile: userInfo.info.mobile,
          user_name: userInfo.info.userName,
          real_name: userInfo.info.realName,
          type: "breathing_give",
        }
      );

      // console.log(res);
      if (res.code !== 200 && res.code !== 205) {
        Toast.clear();
        Toast(res.msg || "请稍后再试");
      }
      if (res.code == 200) {
        Toast.clear();
        Toast("恭喜您已成功领取呼吸精品课季度会员");
        window.location.href =
          "https://class.medsci.cn/list/0-12-1-20408-0-0-0";
      }
      if (res.code == 205) {
        Toast.clear();
        Toast("您已经领取呼吸精品课季度会员，直接学习即可");
        window.location.href =
          "https://class.medsci.cn/list/0-12-1-20408-0-0-0";
      }

      // app_order_id.value = res.data.data;
      // Toast.clear();
      // Toast(res.msg);
      // selectPayWay();
      // }
    };

    const link = () => {
      var u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      } else {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      }
    };

    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time); // time +''
        return [val[0], val[1]];
      } else {
        return [0, time];
      }
    };

    const showGuize = () => {
      guize.value = true;
    };
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    };

    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == "ALL") {
          show.value = true;
        } else {
          MobilePay(way.value);
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + "/payment/pay/merge_qrcode", {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value,
        });
        const { qrCodeUrl } = res.data;
        if (qrCodeUrl) {
          showImg.value = true;
          qrCodeUrlImg.value = qrCodeUrl;
        }

        const inter = setInterval(() => {
          getStatus();
          if (MSpaymentStatus.value == "PAID") {
            showImg.value = false;
            // memberCardStatus();
            clearInterval(inter);
          }
        }, 3000);
      }
      isBay.value = false;
    };
    // const memberCardStatus = async () => {
    //   const res = await request.post(
    //     baseUrlFreeVip + "/medsci-activity/pay/member-card-status",
    //     {
    //       order_id: app_order_id.value,
    //       type: "new_user_register",
    //     }
    //   );
    //   console.log(res, "下单成功");
    // };

    const getStatus = async () => {
      const res = await request.get(baseUrl + "/payment/pay/query", {
        params: {
          appOrderId: app_order_id.value,
        },
      });

      const { paymentStatus } = res.data;
      MSpaymentStatus.value = paymentStatus;
      if (paymentStatus == "PAID") {
        Toast("支付成功");
        window.location.href = "https://open.medsci.cn/";
        // console.log(curVal);
        // //微信支付成功跳转
        // wx.queryShareInterface({
        //   shareTicket: shareTicket,
        //   success(res) {
        //     if (res.shareTickets) {
        //       // 支付成功
        //       if (curVal.value == '3') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/sci/index.do'
        //       })
        //       } else if (curVal.value == '2') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/guideline/index.do'
        //       })
        //       } else if (curVal.value == '4') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/sci/nsfc.do?utm_campaign'
        //       })
        //       } else if (['1', '5', '6', '7', '8', '9'].includes(curVal.value)) {
        //         wx.redirectTo({
        //         url: 'https://class.medsci.cn/'
        //       })
        //       }
        //     }
        //   }
        // })
        // if (curVal.value == '3') {
        //   window.location.href = "https://www.medsci.cn/sci/index.do";
        // } else if (curVal.value == '2') {
        //   window.location.href = "https://www.medsci.cn/guideline/index.do";
        // } else if (curVal.value == '4') {
        //   window.location.href = "https://www.medsci.cn/sci/nsfc.do?utm_campaign";
        // } else if (['1', '5', '6', '7', '8', '9'].includes(curVal.value)) {
        //   window.location.href = "https://class.medsci.cn/";
        // }
      }
    };

    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + "/payment/pay/build", {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: "MEDSCI_WEB",
        payType:
          way.value == "ALL" ? "MWEB" : way.value == "WX" ? "JSAPI" : "NATIVE",
      });

      if (res2.code != "SUCCESS") {
        Toast(res2.msg);
        return;
      }

      const res3 = await request.post(baseUrl + "/payment/pay/order", {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value,
      });

      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data;
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement("div");
        div.innerHTML = aliH5.html;
        document.body.appendChild(div);
        document.forms[0].submit();
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl;
      }
      if (wechatH5) {
        window.location.href = wechatH5.h5Url;
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          "getBrandWCPayRequest",
          {
            appId: wechatJsapi.appId,
            timeStamp: wechatJsapi.timeStamp,
            nonceStr: wechatJsapi.nonceStr,
            package: wechatJsapi.packageStr,
            signType: wechatJsapi.signType,
            paySign: wechatJsapi.paySign,
          },
          function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
              Toast.success("支付成功！");
              window.location.href = "https://open.medsci.cn/";
            }
          }
        );
      }
    };

    return {
      curVal,
      ...toRefs(state),
      loading,
      userInfo,
      active,
      guize,
      time,
      vipType,
      actions,
      show,
      showImg,
      qrCodeUrlImg,
      isEnd,
      timing,
      dialogVisible,
      isMobile,
      url,
      type,

      getBtn,
      Login,
      Pay,
      testPlay,
      getQueryVariable,
      // wxShare,
      link,
      showGuize,
      formatTime,
      onFinish,
      onSelect,
      isLimitComplete,
      getBtn1,
      close,
    };
  },
};
</script>

<style scoped lang="stylus">
    .box {
  width: 10rem;
  font-size: 0;
  padding-bottom: 1.33rem;
  margin: 0 auto;
  .img{
      width: 10rem;
  }
  .bottom{
      position: fixed;
      left: 0;
      bottom: 0;
      width: 10rem;
      // height: 1.33rem;
      height: 1.6rem;
      line-height: 1.6rem;
      display: flex;
      z-index: 1;
      right: 0;
      margin: 0 auto;
  }
  .left{
      width: 5.65rem;
      color: #333;
      font-weight: 600;
      font-size: 0.47rem;
      background-color: #FFFFFF;
      flex: none;
      // font-size: 0.43rem;
      text-align: center;
  }
  .right{
      width: 4.35rem;
      color: #fff;
      background-color: #FF6600;
      font-size: 0.49rem;
      text-align: center;
  }
  .right-2{
    background: #bdbdbd;
    font-size: 0.43rem;
  }
  .guwen1{
      width: 9rem;
      height: 1.5rem;
      position: absolute;
      left: 0.5rem;
      top: 26.5rem;
      opacity: 0;
      right: 0.5rem;
      margin: 0 auto;
  }
  .guwen2{
      width: 9rem;
      height: 1.5rem;
      position: absolute;
      left: 0.5rem;
      top: 46.6rem;
      opacity: 0;
      right: 0.5rem;
      margin: 0 auto;
  }
}

// 弹窗
.dialog-wrapper{
  width: 8.8rem;
  height: 10.2rem;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  border-radius: 0.45rem;
  overflow: hidden;
  .img1{
    width: 8.8rem;
    height: 10.2rem;
    border-radius: 0.45rem;
  }
  .img2{
    width 3.51rem;
    height: 3.43rem;
    position: absolute;
    left: 0.59rem;
    top: 2.97rem;
  }
  .img3{
    width 3.51rem;
    height: 3.43rem;
    position: absolute;
    right: 0.59rem;
    top: 2.97rem;
  }
}
.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.block {
  // width: 120px;
  // height: 120px;
  // background-color: #fff;
}
</style>
