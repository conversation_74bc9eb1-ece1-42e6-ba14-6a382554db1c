<template>
  <div class="box">
    <false-data></false-data>

    <!-- <img class="image" src="https://static.medsci.cn/public-image/ms-image/84009580-e24e-11ec-a1b8-6123b3ff61ea_gzr.png" alt=""> -->
    <img class="image" src="https://static.medsci.cn/public-image/ms-image/3623c370-7dff-11ee-8419-490633d98253_bcg.png"
      alt="" />

    <!-- <img class="couse" src="https://static.medsci.cn/public-image/ms-image/2694b4b0-360c-11ec-8e2f-1389d01aad85_999.gif" alt=""> -->
    <!-- <ms-swiper class="img-back"></ms-swiper> -->


    <div class="last">
      <div class="bottom" @click="getBtn">
        <img src="https://static.medsci.cn/public-image/ms-image/43325a90-7dff-11ee-8419-490633d98253_btn.png" alt="" />
      </div>
    </div>
  </div>

  <van-action-sheet v-model:show="show" :actions="actions" @select="onSelect" cancel-text="取消" close-on-click-action />
  <van-overlay :show="showImg" @click="showImg = false">
    <div class="wrapper" @click.stop>
      <div>
        <div class="text">扫码完成支付</div>
        <img :src="qrCodeUrlImg" alt="" />
      </div>
    </div>
  </van-overlay>
</template>
  
<script>
import { ref, reactive, onMounted, toRefs } from "vue";
import request from "@/utils/request";
import Cookies from "js-cookie";
import { useRouter, useRoute } from "vue-router";
import { Toast } from "vant";
import falseData from "@/components/falseData.vue";
import msSwiper from "./msSwiper.vue";
import { baseUrl } from "@/utils/config";
import { baseUrlFreeVip } from "@/utils/configFreeVip";
import { msBaseUrl } from "@/utils/sanofiConfig"

// import dayjs from "dayjs";

export default {
  name: "double11",
  components: {
    falseData,
    msSwiper,
  },
  setup: () => {
    const router = useRouter();
    const route = useRoute();
    const timing = ref(false);
    const curVal = ref("1");
    const vipType = ref("");
    const loading = ref(false);
    const active = ref(false);
    const guize = ref(false);
    const time = ref(
      new Date("2032/02/20 23:59:59").getTime() - new Date().getTime()
    ); // 还剩多少毫秒

    const isBay = ref(false);
    const way = ref("");
    const app_order_id = ref("");
    const MSpaymentStatus = ref("");
    const wxOpenId = ref("");
    const show = ref(false);
    const showImg = ref(false);
    const qrCodeUrlImg = ref("");
    const accessAppId = ref("college");
    const isEnd = ref(false);
    const actions = [{ name: "支付宝支付" }, { name: "微信支付" }];
    const onSelect = (item) => {
      show.value = false;
      MobilePay(item.name == "支付宝支付" ? "ALI" : "WX");
    };

    const userInfo = reactive({
      info: {},
    });
    const state = reactive({
      msg: {},
    });
    const getQueryVariable = (variable) => {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    };

    onMounted(async () => {
      //   console.log( dayjs(new Date().getTime()).format('YYYY-MM-DD HH:mm:ss'));
      //   console.log(new Date("2023/08/19 00:00:00").getTime()); // 8/19
      //   console.log(new Date("2023/08/20 24:00:00").getTime()); // 8/20

      document.title = "公开课会员&双11";
      wxShare({
        title: "公开课会员&双11",
        summary: "4折开通，解锁上千节临床+科研+医院管理课程",
        thumb:
          "https://static.medsci.cn/public-image/ms-image/a06e2900-7e0e-11ee-8419-490633d98253_分享图.png",
      });
      // let userAgent = navigator.userAgent;
      // if (userAgent != null && userAgent.indexOf('MicroMessenger') > -1) {
      //     if (route.query.openId || getQueryVariable("openId")) {
      //         buy()
      //     }
      // }
      //   const end = new Date("2023/8/20").getTime();
      //   const now = new Date().getTime();
      //   if (now - end > 0) {
      //     timing.value = true;
      //   }
      const userInfos = Cookies.get("userInfo");
      // if (!userInfos) {
      //   Login();
      // }
      // 埋点
      request.get(baseUrlFreeVip + "/medsci-activity/visit", {
        params: {
          user_id: userInfo.info.plaintextUserId,
          ciphertext_user_id: userInfo.info.userId,
          event_type: "view",
          type: "new_user_register",
        },
      });
    });

    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      // console.log(apiUrl)
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl,
        },
      });
      wx.config(res.data);
      wx.error(function (error) {
        console.log(error);
      });
      wx.ready(function () {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/double11",
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: "https://static.medsci.cn/product/medsci/active/zen0214/index.html#/double11",
          imgUrl: sub.thumb, // 分享图标
          success: function () {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    };

    const getAuth = () => {
      const userAgent = navigator.userAgent;
      const openId = route.query.openId || getQueryVariable("openId");
      if (userAgent != null && userAgent.indexOf("MicroMessenger") > -1) {
        way.value = "WX";
        //微信扫码
        const AppId = "wx9096048917ec59ab";
        if (!openId) {
          const ReturnUrl = encodeURIComponent(window.location.href);
          const RedirectUrl = encodeURIComponent(
            `http://api.center.medsci.cn/api/wechat/access-token/${AppId}?returnUrl=${ReturnUrl}`
          );
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${AppId}&redirect_uri=${RedirectUrl}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;
        } else {
          wxOpenId.value = openId;
        }
      } else if (userAgent != null && userAgent.indexOf("AlipayClient") > -1) {
        way.value = "ALI";
      } else {
        way.value = "ALL";
      }
    };
    //点击确认领取
    const getBtn = async () => {
      getAuth();
      Toast.loading({
        duration: 0,
        message: "提交中...",
        loadingType: "spinner",
      });
      const userInfos = Cookies.get("userInfo");
      if (!userInfos) {
        const sso_sessionid =
          route.query.sso_sessionid || getQueryVariable("sso_sessionid");
        if (sso_sessionid) {
          const res = await request.post("/medsciUser/getLoginUserInfoBySid", {
            sessionid: sso_sessionid,
          });
          userInfo.info = res.data;
          Pay();
        } else {
          Login();
        }
      } else {
        userInfo.info = JSON.parse(userInfos);
        if (!userInfo.info.mobile) {
          addLoginDom();
        } else{
          Pay()
        }
        // else {
        //   isLimitComplete()
        // }
      }
    };

    // 跳转登录
    const Login = () => {
      addLoginDom();
    };

    const testPlay = (item) => {
      if (item.link) {
        window.location.href = item.link;
      }
    };

    const Pay = async () => {
      // const { userId, userName, realName, mobile, email, plaintextUserId } = userInfo.info;
      let nowTime = new Date().getTime(),
        startTime = new Date("2023/11/07 00:00:00").getTime(),
        endTime = new Date("2023/11/11 23:59:59").getTime();
      // if (nowTime < startTime) {
      //   Toast("活动还没开始，再等等哦");
      //   return false;
      // } else if (nowTime > startTime && nowTime < endTime) {
      //   //   Toast("进行中");
      // } else if (nowTime > endTime) {
      //   Toast("活动已结束，下次再来吧");
      //   return false;
      // }

      // const resData = await request.post(
      //   baseUrlFreeVip + "/medsci-activity/attend-status",
      //   {
      //     order_id: app_order_id.value,
      //     type: "new_user_register",
      //     mobile: userInfo.info.mobile,
      //   }
      // );
      // if (resData.data.status == 0) {

        // let obj = {
        //   1: "scientific_research",
        //   2: "guider",
        //   3: "journal",
        //   4: "nsfc",
        //   5: "breathe",
        //   6: "nerve",
        //   7: "cardiovascular",
        //   8: "skin",
        //   9: "ophthalmology",
        // };
        const res = await request.post(
          baseUrlFreeVip +
          "/medsci-activity/pay/member-card/" ,
          {
            user_id: userInfo.info.plaintextUserId,
            ciphertext_user_id: userInfo.info.userId,
            mobile: userInfo.info.mobile,
            user_name: userInfo.info.userName,
            real_name: userInfo.info.realName,
            type: "double_eleven_card",
          }
        );

        // console.log(res);
        if (res.code !== 200 && res.code !== 205) {
          Toast.clear();
          Toast(res.msg || "请稍后再试");
        }
        app_order_id.value = res.data.data;
        Toast.clear();
        selectPayWay();
      // }
    };

    const link = () => {
      var u = navigator.userAgent;
      const isAndroid = u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
      if (isAndroid) {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      } else {
        window.location.href =
          "https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news";
      }
    };

    // 时间处理
    const formatTime = (time) => {
      if (time >= 10) {
        const val = String(time); // time +''
        return [val[0], val[1]];
      } else {
        return [0, time];
      }
    };

    const showGuize = () => {
      guize.value = true;
    };
    // 完成
    const onFinish = () => {
      isEnd.value = true;
    };

    const selectPayWay = async () => {
      if (window.innerWidth < 750) {
        // 移动端支付
        if (way.value == "ALL") {
          show.value = true;
        } else {
          MobilePay(way.value);
        }
      } else {
        // pc 弹出二维码
        const res = await request.post(baseUrl + "/payment/pay/merge_qrcode", {
          accessAppId: accessAppId.value,
          appOrderId: app_order_id.value,
        });
        const { qrCodeUrl } = res.data;
        if (qrCodeUrl) {
          showImg.value = true;
          qrCodeUrlImg.value = qrCodeUrl;
        }

        const inter = setInterval(() => {
          getStatus();
          if (MSpaymentStatus.value == "PAID") {
            showImg.value = false;
            // memberCardStatus();
            clearInterval(inter);
          }
        }, 3000);
      }
      isBay.value = false;
    };
    // const memberCardStatus = async () => {
    //   const res = await request.post(
    //     baseUrlFreeVip + "/medsci-activity/pay/member-card-status",
    //     {
    //       order_id: app_order_id.value,
    //       type: "new_user_register",
    //     }
    //   );
    //   console.log(res, "下单成功");
    // };

    const getStatus = async () => {
      const res = await request.get(baseUrl + "/payment/pay/query", {
        params: {
          appOrderId: app_order_id.value,
        },
      });

      const { paymentStatus } = res.data;
      MSpaymentStatus.value = paymentStatus;
      if (paymentStatus == "PAID") {
        Toast("支付成功");
        window.location.href = "https://open.medsci.cn/";
        // console.log(curVal);
        // //微信支付成功跳转
        // wx.queryShareInterface({
        //   shareTicket: shareTicket,
        //   success(res) {
        //     if (res.shareTickets) {
        //       // 支付成功
        //       if (curVal.value == '3') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/sci/index.do'
        //       })
        //       } else if (curVal.value == '2') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/guideline/index.do'
        //       })
        //       } else if (curVal.value == '4') {
        //         wx.redirectTo({
        //         url: 'https://www.medsci.cn/sci/nsfc.do?utm_campaign'
        //       })
        //       } else if (['1', '5', '6', '7', '8', '9'].includes(curVal.value)) {
        //         wx.redirectTo({
        //         url: 'https://class.medsci.cn/'
        //       })
        //       }
        //     }
        //   }
        // })
        // if (curVal.value == '3') {
        //   window.location.href = "https://www.medsci.cn/sci/index.do";
        // } else if (curVal.value == '2') {
        //   window.location.href = "https://www.medsci.cn/guideline/index.do";
        // } else if (curVal.value == '4') {
        //   window.location.href = "https://www.medsci.cn/sci/nsfc.do?utm_campaign";
        // } else if (['1', '5', '6', '7', '8', '9'].includes(curVal.value)) {
        //   window.location.href = "https://class.medsci.cn/";
        // }
      }
    };

    // 移动端支付
    const MobilePay = async (payChannel) => {
      const res2 = await request.post(baseUrl + "/payment/pay/build", {
        accessAppId: accessAppId.value,
        appOrderId: app_order_id.value,
        payChannel,
        paySource: "MEDSCI_WEB",
        payType:
          way.value == "ALL" ? "MWEB" : way.value == "WX" ? "JSAPI" : "NATIVE",
      });

      if (res2.code != "SUCCESS") {
        Toast(res2.msg);
        return;
      }

      const res3 = await request.post(baseUrl + "/payment/pay/order", {
        accessAppId: accessAppId.value,
        payOrderId: res2.data.payOrderId,
        openId: wxOpenId.value,
      });

      const { aliH5, aliQR, wechatH5, wechatJsapi } = res3.data;
      // 支付宝支付
      if (aliH5) {
        const div = document.createElement("div");
        div.innerHTML = aliH5.html;
        document.body.appendChild(div);
        document.forms[0].submit();
      }
      if (aliQR) {
        window.location.href = aliQR.payUrl;
      }
      if (wechatH5) {
        window.location.href = wechatH5.h5Url;
      }
      if (wechatJsapi) {
        WeixinJSBridge.invoke(
          "getBrandWCPayRequest",
          {
            appId: wechatJsapi.appId,
            timeStamp: wechatJsapi.timeStamp,
            nonceStr: wechatJsapi.nonceStr,
            package: wechatJsapi.packageStr,
            signType: wechatJsapi.signType,
            paySign: wechatJsapi.paySign,
          },
          function (res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
              Toast.success("支付成功！");
              window.location.href = "https://open.medsci.cn/";
            }
          }
        );
      }
    };

    return {
      curVal,
      ...toRefs(state),
      loading,
      userInfo,
      active,
      guize,
      time,
      vipType,
      actions,
      show,
      showImg,
      qrCodeUrlImg,
      isEnd,
      timing,

      getBtn,
      Login,
      Pay,
      testPlay,
      getQueryVariable,
      wxShare,
      link,
      showGuize,
      formatTime,
      onFinish,
      onSelect,
      // isLimitComplete
    };
  },
};
</script>
  
<style scoped lang="stylus">
      .box {
        font-size 16px
        position relative
        // max-width 750Px
        max-width 10rem
        margin 0 auto
        margin-bottom: env(safe-area-inset-bottom);
        // padding-bottom: 48px;
        display: flex;
        .image {
          width 100%
          position: relative
          height: auto;
        }
        .content{
          position: absolute
          top: 11.73rem
          width: 100%
          height: auto;
        }
    .content1{
      width:9.08rem
      padding-left: 0.453rem
    }
   .content_box{
     margin-top: 11.5px;position: relative;
   }
    .btn{
      position: absolute
      padding-left: 296px
      width: 19px
      right:11.33%
      top:36%
    }
  
        .couse {
          position: absolute;
          top 53px;
          right: 5px
          width 200px
        }
  
        .guize {
          width 79px
          position absolute
          top 142px
          right 0
        }
  
        .img-back{
          height 151px
          width 100%
          background-size: 100% 100%
          position absolute
          top 992px
          left 50%
          transform: translate(-50%)
          z-index 100
          box-sizing border-box
  
        }
        .last {
          position fixed
          left 0
          bottom 0
          right 0
          // width 100vw
          margin auto
          width 10rem
          .top {
            height 40px
            display: flex
            background-color #FFC700
            display: flex
            align-items: center
            .img {
              width 50px
              height 32px
              margin 0 5px 0 8px
            }
            .van-count-down {
              display: flex
              align-items: center
            }
            .block {
              display: flex
              &-item {
                background-color #ffffff
                color #333333
                width 20px
                height 25px
                text-align center
                font-size 20px
                font-weight blod
                line-height 25px
                border-radius 5px
                margin 0 2px
              }
            }
            .colon {
              color #333333
              font-size 8px
              margin 0 1px
              &.a {
                margin-top: 10px
              }
              .mill {
                height 12px
                line-height 12px
              }
            }
          }
          .bottom {
            height 50px
            display: flex
            img{
              width:100%
            }
          }
        }
      }
  
      .van-popup {
        text-align center
        .title-box {
          text-align center
        }
        .btn-box {
          text-align center
        }
        .title {
          width 85px
          margin-top 20px
        }
        .content {
          font-size 13px
          box-sizing border-box
          padding 15px 20px 25px
          color #723107
          .item {
            display: flex
            margin 5px 0
            div {
              &:first-child {
                flex 0 0 80px
              }
            }
            a {
              color #723107
            }
          }
        }
  
        .btn {
          width 256px
          margin 0 auto
        }
      }
  
      .wrapper {
        height 100%
        display flex
        justify-content center
        align-items center
        flex-wrap wrap
        .text {
          font-size 18px
          color #ffffff
          width 100%
          text-align center
        }
      }
  
  
      @media screen and (max-width: 600px) {
  //   body {
  // background-color: lightblue;
  // }
  }
  </style>
  