

<template>
  <div class="box">
    <img class="img"  src="https://static.medsci.cn/public-image/ms-image/202501231549zxqh.jpg" alt="">
    <img class="btn" @click="joinActivity" src="https://static.medsci.cn/public-image/ms-image/202501231549zxqh_btn.png">
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
import { Toast } from 'vant';
import { baseUrlSix } from "@/utils/config618"

export default ({
  name: 'ms12',
  components: {
  },
  setup: () => {
    const joinStatus = ref(false)
    const timing = ref(false);
    const userInfo = reactive({
      info: {},
    })

    onMounted(async () => {
     document.title = '中华核心指导，特惠来袭'
      wxShare({
        title: "中华核心指导，特惠来袭",
        summary: "活动时间1月22日-2月15日，仅限100名，点击报名开启您的文字之旅",
        thumb: "https://static.medsci.cn/public-image/ms-image/202501231549zxqh_share.png"
      })
      const end = (new Date('2025/2/16')).getTime()
      const now = (new Date()).getTime()
      if(now-end > 0) {
        timing.value = true;
      }
      const userInfos = Cookies.get('userInfo');
      // userInfo.info = {"avatar":"http://thirdwx.qlogo.cn/mmopen/vi_32/gjR5H01q089ibicoupdmf2tYdbfx3hPL6fxbMS7NM9dEvaicANS4rACp1aDmDDsuQs8fqqXJrNruPvNLfuuAEVPWQ/132","email":"<EMAIL>","getRoleLoginResponses":[{"identityFlag":0,"roleId":8,"roleName":"前台角色"}],"mobile":"16621177880","plaintextUserId":2530807,"projectCode":"MSYX","projectId":1,"projectName":"梅斯医学","projectType":0,"projectUserStatus":1,"realName":"www","redirectUrl":"https://www.medsci.cn?sso_sessionid=5cb72530807_7b13fced5f4740eba43021fb1b3f9f57","token":{"accessToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************","accessTokenExpireTime":630720000,"refreshToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************"},"userId":"5cb72530807","userName":"伍达辉","userStatus":1,"wechatOpenid":"oAcsesyHWw-mbA2VEMWE9OTWBrkU"};
      if(!userInfos) {
         Login()
      } else {
        userInfo.info = JSON.parse(userInfos);
        attendStatus()
      }
      // 埋点
      request.get(baseUrlSix+"/medsci-activity/visit", {
        params: {
            user_id: userInfo.info.plaintextUserId,
            ciphertext_user_id: userInfo.info.userId,
            event_type: 'view',
            type: 'research_carnival',
        }
      })
    });
    const wxShare = async (sub) => {
      let apiUrl = window.location.href.split("#")[0];
      const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
        params: {
          url: apiUrl
        }
      })
      wx.config(res.data)
      wx.error(function(error) {
        console.log(error)
      })
      wx.ready(function() {
        wx.onMenuShareAppMessage({
          // 微信内朋友分享
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
        wx.onMenuShareTimeline({
          // 微信朋友圈
          title: sub.title, // 分享标题
          desc: sub.summary, // 分享描述
          // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          link: window.location.href,
          imgUrl: sub.thumb, // 分享图标
          success: function() {
            // 设置成功
            //  console.log(window.location.href,'555')
          },
        });
      });
    }
    // 获取是否参加活动
    const attendStatus = async() => {
      const res = await request.post(baseUrlSix+"/medsci-activity/attend-status", {
        mobile: userInfo.info.mobile,
        type: 'research_carnival'
      })
      if(res.data && res.data.status == 1) {
          joinStatus.value = true
      } else {
          joinStatus.value = false
      }
	  }
    // 参加活动
    const joinActivity = async() => {
         if(!Cookies.get('userInfo')) {
          Login()
          return false
        }
        if(timing.value){
            Toast('本次活动已结束，如有相关服务需求，请拨打电话咨询：400-0583-188，也可以联系您的专属学术顾问咨询~')
            return false
        }
        if(joinStatus.value) {
            Toast('您已经报名成功，请留意电话信息，将有专属学术顾问与您联系')
        } else {
            const res = await request.post(baseUrlSix+"/medsci-activity/research_carnival", {
                user_id: userInfo.info.plaintextUserId,
                ciphertext_user_id: userInfo.info.userId,
                mobile: userInfo.info.mobile,
                user_name: userInfo.info.userName,
                real_name: userInfo.info.realName,
                email: userInfo.info.email,
            })
            if(res.code == 200) {
                Toast.success('恭喜您，报名成功，将有专属学术顾问与您联系')
                joinStatus.value = true;
            }
            if(res.code != 200 && res.code != 205) {
                Toast(res.msg || '服务繁忙，请稍后再试')
            }
        }
	  }
    // 跳转登录
    const Login = () => {
      addLoginDom()
    }
    return {
      userInfo,
      timing,
      wxShare,
      Login,
      joinStatus,
      joinActivity,
    };
  }
})
</script>

<style scoped lang="stylus">
  .box {
    width: 10rem;
    font-size: 0;
    margin: 0 auto;
    position:relative;
    .btn{
        position:fixed;
        bottom:0.5rem;
        width:9rem;
        z-index:100;
        left:calc((100% - 9rem) / 2)
    }
    .img{
        width: 10rem;
    }
    .bottom{
        position: fixed;
        left: 0;
        bottom: 0;
        width: 10rem;
        height: 1.33rem;
        line-height: 1.33rem;
        display: flex;
        z-index: 1;
        right: 0;
        margin: 0 auto;
    }
    .left{
        width: 5.65rem;
        color: #333;
        background-color: #FFFFFF;
        flex: none;
        font-size: 0.43rem;
        text-align: center;
    }
    .right{
        width: 4.35rem;
        color: #fff;
        background-color: #FF6600;
        font-size: 0.49rem;
        text-align: center;
    }
    .right-2{
      background: #bdbdbd;
      font-size: 0.43rem;
    }
    .guwen1{
        width: 9rem;
        height: 1.5rem;
        position: absolute;
        left: 0.5rem;
        top: 26.5rem;
        opacity: 0;
        right: 0.5rem;
        margin: 0 auto;
    }
    .guwen2{
        width: 9rem;
        height: 1.5rem;
        position: absolute;
        left: 0.5rem;
        top: 46.6rem;
        opacity: 0;
        right: 0.5rem;
        margin: 0 auto;
    }
  }

  // 弹窗
  .dialog-wrapper{
    width: 8.8rem;
    height: 10.2rem;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 0.45rem;
    overflow: hidden;
    .img1{
      width: 8.8rem;
      height: 10.2rem;
      border-radius: 0.45rem;
    }
    .img2{
      width 3.51rem;
      height: 3.43rem;
      position: absolute;
      left: 0.59rem;
      top: 2.97rem;
    }
    .img3{
      width 3.51rem;
      height: 3.43rem;
      position: absolute;
      right: 0.59rem;
      top: 2.97rem;
    }
  }
</style>