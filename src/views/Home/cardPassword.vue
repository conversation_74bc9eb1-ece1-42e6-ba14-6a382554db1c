<template>
  <div class="box">
    <img class="logo" src="https://static.medsci.cn/public-image/ms-image/fc0ffef0-5100-11ed-b66b-937b834e3ef9_kami.png" alt="">
    <div class="head">
      <span v-if="userInfo.info.userName">你好，{{userInfo.info.userName}}</span>
      <span v-if="!userInfo.info.userName">你好，请先</span>
      <span class="head-name" v-if="!userInfo.info.userName" @click="Login">登录</span>
      <span></span>
    </div>
    <div class="label">卡密</div>
    <div>
      <!-- <el-input v-model="pwd" placeholder="请输入内容"></el-input> -->
      <van-field class="pwd" v-model="exchangeCode" placeholder="请输入卡密" :disabled="isOpen" />
      <div class="pwd-hint">请输入8位英文和数字组合格式的卡密</div>
      <van-button class="btn" type="primary" :disabled="isOpen" @click="activate">立即激活</van-button>
    </div>
    <div class="footer">
      <span>激活同意会员卡 </span>
      <span class="rule" @click="showDialog">使用规则</span>
    </div>
    <van-popup v-model:show="show">
      <div class="dialog-wrapper">
        <p>1.本卡激活后不可转让他人</p>
        <p>2.本产品为数字阅读作品，不支持7天无理由退货</p>
        <p>3.在法律允许范围内，最终解释权归梅斯医学所有</p>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import request from "@/utils/request"
import Cookies from 'js-cookie'
// import { useRouter, useRoute } from 'vue-router'
import { Toast } from 'vant';
import { baseUrlSix } from "@/utils/config618"
import { msBaseUrl, vipBaseUrl } from "@/utils/sanofiConfig"

export default ({
  name: 'cardPassword',
  components: {
  },
  setup: () => {
    // const router = useRouter()
    // const route = useRoute()
    const exchangeCode = ref('');
    const isOpen = ref(true);
    const show = ref(false);
    const userInfo = reactive({
      info: {},
    })

    onMounted(async () => {
      document.title = '卡密激活'
      const userInfos = Cookies.get('userInfo');
      console.log(userInfos, 1)
      // userInfo.info = {"avatar":"http://thirdwx.qlogo.cn/mmopen/vi_32/gjR5H01q089ibicoupdmf2tYdbfx3hPL6fxbMS7NM9dEvaicANS4rACp1aDmDDsuQs8fqqXJrNruPvNLfuuAEVPWQ/132","email":"<EMAIL>","getRoleLoginResponses":[{"identityFlag":0,"roleId":8,"roleName":"前台角色"}],"mobile":"16621177880","plaintextUserId":2530807,"projectCode":"MSYX","projectId":1,"projectName":"梅斯医学","projectType":0,"projectUserStatus":1,"realName":"www","redirectUrl":"https://www.medsci.cn?sso_sessionid=5cb72530807_7b13fced5f4740eba43021fb1b3f9f57","token":{"accessToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************","accessTokenExpireTime":630720000,"refreshToken":"*********************************************************************************************************************************************************************************************************************************************************************************************************"},"userId":"5cb72530807","userName":"伍达辉","userStatus":1,"wechatOpenid":"oAcsesyHWw-mbA2VEMWE9OTWBrkU"};
      if(!userInfos) {
        // Login()
        // isLimitComplete();
      } else {
        console.log(1)
        userInfo.info = JSON.parse(userInfos);
        isOpen.value = false;
        isLimitComplete();
      }
      // addPerfectInfoDom()
      // wxShare({
      //   title: "618超值盛典，梅斯科研服务全场85折！",
      //   summary: "史无前例，统统85折！母语化润色、专业中译英、审查润色、临床研究方案指导......",
      //   thumb: "https://static.medsci.cn/public-image/ms-image/eb76afc0-e239-11ec-a1b8-6123b3ff61ea_618.png"
      // })
    });
    // const wxShare = async (sub) => {
    //   let apiUrl = window.location.href.split("#")[0];
    //   const res = await request.get("https://ypxcx.medsci.cn/ean/share", {
    //     params: {
    //       url: apiUrl
    //     }
    //   })
    //   wx.config(res.data)
    //   wx.error(function(error) {
    //     console.log(error)
    //   })
    //   wx.ready(function() {
    //     wx.onMenuShareAppMessage({
    //       // 微信内朋友分享
    //       title: sub.title, // 分享标题
    //       desc: sub.summary, // 分享描述
    //       // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    //       link: window.location.href,
    //       imgUrl: sub.thumb, // 分享图标
    //       success: function() {
    //         // 设置成功
    //         //  console.log(window.location.href,'555')
    //       },
    //     });
    //     wx.onMenuShareTimeline({
    //       // 微信朋友圈
    //       title: sub.title, // 分享标题
    //       desc: sub.summary, // 分享描述
    //       // link: window.location.href.includes("test") ? 'https://static.medsci.cn/product/medsci/active/zen1111/test/10-2/index.html' : 'https://static.medsci.cn/product/medsci/active/zen1111/10/index.html', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
    //       link: window.location.href,
    //       imgUrl: sub.thumb, // 分享图标
    //       success: function() {
    //         // 设置成功
    //         //  console.log(window.location.href,'555')
    //       },
    //     });
    //   });
    // }
    const jsCallApp_goPage = (params) => {
        if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) { // ios
            window.webkit.messageHandlers.toModule.postMessage(params)
        } else if (navigator.userAgent.indexOf('Android') !== -1) { // 安卓
            let jsonParams = JSON.stringify(params)
            window.Interface.toModule(jsonParams)
        }
    }
    // 参加活动
    const activate = async() => {
      console.log(exchangeCode.value, 'exchangeCode.value')
      if(!exchangeCode.value) {
        Toast('请输入8位英文和数字组合格式的卡密')
        return false
      }
      // vipBaseUrl + 
      const res = await request.post(vipBaseUrl + "/user/exchange", {
          userId: userInfo.info.userId,
          exchangeUsername: userInfo.info.userName,
          exchangeCode: exchangeCode.value,
      })
      console.log(res, 'rrr')
      if(res.status == 200) {
          Toast.success('激活成功')
          if(window.navigator.userAgent.includes('medsci_app')){
            if(res.data && res.data.propertyName) {
              switch (res.data.propertyName) {
                case '课程会员':
                  jsCallApp_goPage({type:"course",id:""})
                  break;
                case '指南会员':
                  jsCallApp_goPage({type:"guider",id:""})
                  break;
                case '基金会员':
                  var txt = window.location.href.includes('test') ? 'https://portal-test.medsci.cn/sci/nsfc.do' : 'https://www.medsci.cn/sci/nsfc.do'
                  jsCallApp_goPage({type:"nsfc",id:txt})
                  break;
                default:
                  break;
              }
            }
            return false
          }
          var url = '';
          var test = window.location.href.includes('test') ? true : false;
          if(res.data && res.data.propertyName) {
            switch (res.data.propertyName) {
              case '课程会员':
                url = test ? 'https://edu-test.medsci.cn/' : 'https://class.medsci.cn/'
                break;
              case '指南会员':
                url = test ? 'https://portal-test.medsci.cn/guideline/index.do': 'https://www.medsci.cn/guideline/index.do'
                break;
              case '基金会员':
                url = test ? 'https://portal-test.medsci.cn/sci/nsfc.do' : 'https://www.medsci.cn/sci/nsfc.do'
                break;
              default:
                break;
            }
          }
          setTimeout(function() {
            if(url) {
              window.location.href = url;
            }
          }, 2000)
      }
      if(res.status != 200) {
          Toast(res.message || '服务繁忙，请稍后再试')
      }
	  }
    // 跳转登录
    const Login = () => {
      addLoginDom()
    }
    // 用户登陆后 完善信息
    const isLimitComplete = async () => {
      // 完善信息
      const res = await request.get(msBaseUrl+'/perfectInfo/userInfoStatus?encryptionUserId='+ userInfo.info.userId)
      console.log(res, 'rreeesss')
      if(res.data.isCompleteInfo){
        // 用户参加活动状态
        // getUsersParticipateStatus()
      } else {
        addPerfectInfoDom()
      }
      
    }
    const showDialog = () => {
      show.value = true
    }
    return {
      userInfo,
      isOpen,
      // wxShare,
      Login,
      showDialog,
      show,
      activate,
      isLimitComplete,
      exchangeCode,
      jsCallApp_goPage,
    };
  }
})
</script>

<style scoped lang="stylus">
  .van-cell:after{
    border: none;
  }
  .box{
    width: 10rem;
    font-size: 0;
    margin: 0 auto;
    padding: 0 0.75rem;
    box-sizing: border-box;
    .logo{
      width: 3rem;
      height: 2.97rem;
      margin: 0.89rem auto 1.44rem;
      display: block;
    }
    .head{
      color: #000;
      font-size: 0.5rem;
      font-weight: 600;
      margin-bottom: 0.6rem
      .head-name{
        color: #2F92EE;
      }
    }
    .label{
      font-size: 0.4rem;
      margin-bottom: 0.5rem;
    }
    .pwd{
      width: 8.48rem;
      height: 1.17rem;
      border: 1px solid #DDDDDD;
      border-radius: 0.08rem;
      margin-bottom: 0.5rem;
    }
    .pwd-hint{
      font-size: 0.32rem;
      color: #ADADAD;
      margin-bottom: 0.8rem
    }
    .btn{
      width: 8.48rem;
      height: 1.17rem;
      border-radius: 0.08rem;
      margin-bottom: 0.3rem;
    }
    .footer{
      font-size: 0.32rem;
      color: #333;
      .rule{
        color: #2F92EE;
      }
    }
  }

  // 弹窗
  .dialog-wrapper{
    width: 8rem;
    margin: auto;
    overflow: hidden;
    padding: 0.2rem 0.4rem 0.6rem;
    font-size: 0.36rem;
    color: #232323;
  }
</style>