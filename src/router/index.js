import { createRouter, createWebHashHistory, createWebHistory } from "vue-router";

const routes = [
  {
    path: "/",
    name: "Home",
    meta: {
      title: "首页",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/sj",
    name: "Shenjing",
    meta: {
      title: "神经科会员首发",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ms618",
    name: "ms618",
    meta: {
      title: "科研狂欢，研定618",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/sjk618",
    name: "Shenjing618",
    meta: {
      title: "618抄底价购神经科会员，999元再赠3个月时长！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/zhinan",
    name: "zhinan",
    meta: {
      title: "梅斯医学指南会员",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/618",
    name: "618",
    meta: {
      title: "618超值盛典",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/charge",
    name: "charge",
    meta: {
      title: "梅斯预充值送好礼",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/hx",
    name: "huxXi",
    meta: {
      title: "呼吸科会员首发",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/hxk618",
    name: "hxk618",
    meta: {
      title: "618抄底价购呼吸科会员，999元再赠3个月时长！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/kyhy618",
    name: "kyhy618",
    meta: {
      title: "爱学618，科研会员福利购！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/xxg",
    name: "xinXueGuan",
    meta: {
      title: "心血管科会员首发",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/xnk618",
    name: "xnk618",
    meta: {
      title: "618抄底价购心内科会员，899元再赠6个月时长！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/order",
    name: "Order",
    meta: {
      title: "首页",
      keepAlive: true
    },
    component: () => import("../views/Order/index.vue")
  },
  {
    path: "/orderYd",
    name: "orderYd",
    meta: {
      title: "首页",
      keepAlive: true
    },
    component: () => import("../views/Order/orderYd.vue")
  },
  {
    path: "/kap7",
    name: "kap7",
    meta: {
      title: "【KAP产品独家首发】定金1元抵扣2万元！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/kap2022",
    name: "kap2022",
    meta: {
      title: "知信行调查研究 KAP涨价倒计时",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/sanofi",
    name: "sanofi",
    meta: {
      title: "赛诺菲",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/support",
    name: "support",
    meta: {
      title: "助力活动",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/cardPassword",
    name: "cardPassword",
    meta: {
      title: "卡密激活",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/doubleEleven",
    name: "doubleEleven",
    meta: {
      title: "11.11超值盛典 • 语言服务多买多优惠！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/double11",
    name: "double11",
    meta: {
      title: "公开课会员&双11",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/openCourse",
    name: "openCourse",
    meta: {
      title: "公开课会员早鸟发布",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/breathMember",
    name: "breathMember",
    meta: {
      title: "呼吸守护者-共抗混合感染",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/doubleElevens",
    name: "doubleElevens",
    meta: {
      title: "双11，研之有礼",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/zhinan7day",
    name: "zhinan7day",
    meta: {
      title: "老用户回归礼，7天指南VIP",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/yk",
    name: "yk",
    meta: {
      title: "眼科精品课会员",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/pf",
    name: "pf",
    meta: {
      title: "皮肤科精品课会员",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ms11",
    name: "ms11",
    meta: {
      title: "双11科研狂欢购",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ms12",
    name: "ms12",
    meta: {
      title: "双12年终盛典，好价来袭 就要果断！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/jj",
    name: "jj",
    meta: {
      title: "基金会员",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/kyhy",
    name: "kyhy",
    meta: {
      title: "科研会员",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/centVip",
    name: "centVip",
    meta: {
      title: "819医师节，0.01元任选梅斯会员！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/centVips",
    name: "centVips",
    meta: {
      title: "新注册用户好礼！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/centVipNew",
    name: "centVipNew",
    meta: {
      title: "梅斯特别分享0.01元任选10大会员！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ecard",
    name: "ecard",
    meta: {
      // title: "新注册用户好礼！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ecardIntro",
    name: "ecardIntro",
    meta: {
      // title: "新注册用户好礼！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyBreath",
    name: "nyBreath",
    meta: {
      title: "迎元旦，好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyScience",
    name: "nyScience",
    meta: {
      title: "迎元旦，好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyxxg",
    name: "nyxxg",
    meta: {
      title: "迎元旦，好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyBreathNew",
    name: "nyBreathNew",
    meta: {
      title: "好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyScienceNew",
    name: "nyScienceNew",
    meta: {
      title: "好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nyxxgNew",
    name: "nyxxgNew",
    meta: {
      title: "好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/guideLine",
    name: "guideLine",
    meta: {
      title: "梅斯全科室指南会员年卡",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/nsfc",
    name: "nsfc",
    meta: {
      title: "梅斯基金会员年卡",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/sci",
    name: "sci",
    meta: {
      title: "梅斯期刊会员年卡",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/scale",
    name: "scale",
    meta: {
      title: "梅斯医学计算公式会员年卡",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/zxqh",
    name: "zxqh",
    meta: {
      title: "中华核心指导，特惠来袭",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/ky90",
    name: "ky90",
    meta: {
      title: "90 天科研陪跑营，重磅登场！",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/sjNew",
    name: "sjNew",
    meta: {
      title: "好课福利狂欢",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/618Agent",
    name: "618Agent",
    meta: {
      title: "科研Agent-618重磅首发",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/618ranbao",
    name: "618ranbao",
    meta: {
      title: "燃爆618  年中狂欢节",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/618yucun",
    name: "618yucun",
    meta: {
      title: "618会员礼遇  宠的就是你",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
  {
    path: "/aixly",
    name: "aixly",
    meta: {
      title: "AI赋能-课题申报冲刺",
      keepAlive: true
    },
    component: () => import("../views/home/<USER>")
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  // history: createWebHistory(),
  // base: "/product/618/",
  routes
})

export default router