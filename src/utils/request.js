import axios from "axios";
import { Toast } from "vant";
const service = axios.create({
  timeout: 15000, // request timeout
  // baseURL: process.env.NODE_ENV == 'production' ? "https://customer.medsci.cn" : ''
  baseURL: process.env.NODE_ENV == 'production' ? "https://customer.medsci.cn" : 'https://customer.medsci.cn'
});
// 发起请求之前的拦截器
service.interceptors.request.use(
  config => {
    // // 如果有token 就携带tokon
    // const token = window.localStorage.getItem("accessToken");
    // if (token) {
    //   config.headers.common.Authorization = token;
    // }
    return config;
  },
  error => Promise.reject(error)
);
// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.status !== 200 && res.code !== 'SUCCESS' && res.code !== 200 && (res.meta && res.meta.code !== 200)) {
      Toast(res.message || res.msg)
      return Promise.reject(new Error(res.message || "Error"));
    } else {
      return res;
    }
  },
  error => {
    return Promise.reject(error);
  }
);
export default service;