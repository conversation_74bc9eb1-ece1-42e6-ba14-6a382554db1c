<template>

</template>

<script>
import { onMounted, ref } from 'vue'
import { Notification } from 'element3'
import 'element3/lib/theme-chalk/index.css'

export default ({
  setup() {
    const status = ref(true)
    onMounted(() => {
      var arr = [
        "13****8715",
        "MS***92828",
        "MS***67011",
        "MS***3655",
        "MS***760",
        "夏天",
        "MS***290",
        "MS***007",
        "MS***988",
        "MS***009",
        "远处的黎明",
        "MS***112",
        "MS***709",
        "特困人群",
        "春暖花开",
        "MS***656",
        "一盏灯",
        "学习者",
        "MS***842",
        "MS***28977",
        "张铁军",
        "MS***7622",
        "撒哈拉没有沙漠",
        "MS***0522",
        "雪花飞舞",
        "xhwx82",
        "亚峰",
        "西西里可的阳光",
        "MS92***71",
        "<PERSON><PERSON>",
        "胰路前行",
        "<PERSON>_CJ",
        "笳攸",
        "水",
        "于磊",
        "谭爱丽",
        "Sālimah .M",
        "chenweiting",
        "楼外楼",
        "ritor",
        "hinadayao",
        "sunny",
        "陈波",
        "66",
        "Jambalaya945",
        "A",
        "liy",
        "开心果",
        "diend2kk",
        "可",
        "Plateau Wolf",
        "feish.Ma",
        "MS***65353",
        "MS***2322",
        "MS***7711",
        "MS***0981",
        "kk",
        "Dr. CS Wong",
        "木头鱼",
        "MS***008",
        "追梦者",
        "MS***567",
        "白日梦",
        "是喜庆儿吖",
        "嘟嘟",
        "MS***7780",
        "MS***9878",
        "MS***6798",
        "MS***346",
        "MS***457",
        "MS***009",
        "MS***964",
        "MS***289",
        "流逝的白色青春",
        "祝福满满",
        "6****61",
        "ms****4567",
        "MS***190",
        "罗儿",
        "心境",
        "MS***098",
        "4****4166",
        "ms****1489",
        "ms****0137",
        "戴*平",
        "ms****8123",
        "li****3000",
        "ms****5664",
        "ms****6656",
        "h****木木",
        "简****jane",
        "ji****star",
        "ms****3761",
        "ms****5473",
        "ms****8046",
        "ms****2370",
        "高",
        "MS82***75",
        "MS***6357",
        "Catherine沁",
        "MS***7622",
        "漫步云端",
        "JeanTao",
        "钱钱",
        "爱吃洋芋的柯南",
        "Alice",
        "scottle",
        "碧水蓝天",
        "MS***782"
      ]
      const options = {
        showClose: false,
      }
      if (status.value && process.env.NODE_ENV == 'production') {
        status.value = false
        setInterval(() => {
          options.message = `${arr[Math.floor(Math.random() * 112)]}已开通`
          Notification(options)
        }, 1500);
      }
    })
  },
})
</script>