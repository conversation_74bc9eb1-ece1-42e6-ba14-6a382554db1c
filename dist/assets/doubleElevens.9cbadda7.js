import{_ as se,u as te,a as oe,r as i,b as N,o as ae,t as ne,c as P,d as ie,e as ce,f as c,g as R,w as re,F as le,T as a,h as de,p as pe,i as me}from"./index.8f4d7f86.js";import{s as u,C as W}from"./js.cookie.ad72bcd1.js";import{m as ue}from"./msSwiper.7d61aaec.js";import{a as fe}from"./configkap.935700b6.js";import{b as S}from"./config.3aca39f6.js";import{b as D}from"./config618.d924d98a.js";const ve={name:"doubleEleven",components:{msSwiper:ue},setup:()=>{te();const o=oe(),r=i(!1),M=i(!1),B=i(!1),f=i(""),g=i(""),w=i(""),h=i(""),p=i(""),A=i(!1),L=i(!1),O=i(""),x=i("college"),b=i(!1),F=i(new Date("2022/11/30 23:59:59").getTime()-new Date().getTime()),j=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],J=e=>{A.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},l=N({info:{}}),v=N({msg:{}}),y=i(!1),k=e=>{for(var s=window.location.search.substring(1),n=s.split("&"),t=0;t<n.length;t++){var m=n[t].split("=");if(m[0]==e)return m[1]}return!1};ae(async()=>{document.title="\u53CC11\uFF0C\u7814\u4E4B\u6709\u793C",E({title:"\u53CC11\uFF0C\u7814\u4E4B\u6709\u793C",summary:"\u4E13\u4E1A\u5BFC\u5E08\u4E3A\u60A8\u670D\u52A1\uFF0C\u6298\u6263+\u5145\u503C\u597D\u793C\uFF0C\u7EDF\u7EDF\u7ED9\u5230\u60A8~",thumb:"https://static.medsci.cn/public-image/ms-image/b351b7d0-73eb-11ee-9deb-cbc105e8d30c_share.png"})});const E=async e=>{let s=window.location.href.split("#")[0];const n=await u.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(n.data),wx.error(function(t){console.log(t)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},V=async()=>{const e=document.querySelector(".inputMobile"),s=/^1[3-9]\d{9}$/,n=e.value;if(n!="")if(s.test(n)){const t=await u.post(D+"/medsci-activity/two-three/double_eleven_two",{mobile:n});t.code==200?a("\u60A8\u5DF2\u62A5\u540D\u6210\u529F\uFF0C3\u4E2A\u5DE5\u4F5C\u65E5\u5185\u4F1A\u6709\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB\uFF01"):t.code==205?a("\u60A8\u5DF2\u62A5\u540D\uFF0C\u65E0\u9700\u91CD\u590D\u63D0\u4EA4\uFF01"):a(t.msg)}else a("\u8BF7\u8F93\u5165\u5408\u6CD5\u7684\u624B\u673A\u53F7");else a("\u8BF7\u8F93\u5165\u624B\u673A\u53F7")},H=async()=>{if(console.log(y.value,"rrr"),b.value)return a("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;if(!W.get("userInfo"))return T(),!1;if(!y.value){const e=await u.post(D+"/medsci-activity/double_eleven",{user_id:l.info.plaintextUserId,ciphertext_user_id:l.info.userId,mobile:l.info.mobile,user_name:l.info.userName,real_name:l.info.realName,email:l.info.email});e.code==200&&(a.success("\u62A5\u540D\u6210\u529F"),y.value=!0),e.code==205&&(a("\u60A8\u5DF2\u62A5\u540D\uFF0C\u7B49\u5F85\u56DE\u8BBF"),y.value=!0),e.code!=200&&e.code!=205&&a(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},X=async()=>{if(b.value)return a("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;const e=navigator.userAgent,s=o.query.openId||k("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){f.value="WX";const t="wx9e48b4288d6af69e";if(s)h.value=s;else{const m=encodeURIComponent(window.location.href),_=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${t}?returnUrl=${m}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${t}&redirect_uri=${_}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;return}}else e!=null&&e.indexOf("AlipayClient")>-1?f.value="ALI":f.value="ALL";const n=W.get("userInfo");if(a.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),n)l.info=JSON.parse(n),l.info.mobile||addLoginDom(),U();else{const t=o.query.sso_sessionid||k("sso_sessionid");if(t){const m=await u.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});l.info=m.data,U()}else T()}},T=()=>{addLoginDom()},Q=e=>{e.link&&(window.location.href=e.link)},U=async()=>{const{userId:e,userName:s,realName:n,mobile:t,email:m,plaintextUserId:_}=l.info,d=await u.post(fe+"/openOrder/addActivityOrder",{activityId:v.msg.id,itemNum:1,itemPicPath:"",activityName:v.msg.name,itemPrice:v.msg.money,projectId:1,orderType:v.msg.type,mobile:t,payment:v.msg.money,userId:e,nikeName:s,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});d.status==200?(g.value=d.data,a.clear(),K()):a(d.message)},z=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},K=async()=>{if(window.innerWidth<750)f.value=="ALL"?A.value=!0:$(f.value);else{const e=await u.post(S+"/payment/pay/merge_qrcode",{accessAppId:x.value,appOrderId:g.value}),{qrCodeUrl:s}=e.data;s&&(L.value=!0,O.value=s);const n=setInterval(()=>{G(),w.value=="PAID"&&clearInterval(n)},3e3)}B.value=!1},G=async()=>{const e=await u.get(S+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:s}=e.data;w.value=s,s=="PAID"&&a("\u652F\u4ED8\u6210\u529F")},$=async e=>{const s=await u.post(S+"/payment/pay/build",{accessAppId:x.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:f.value=="ALL"?"MWEB":f.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){a(s.msg);return}const n=await u.post(S+"/payment/pay/order",{accessAppId:x.value,payOrderId:s.data.payOrderId,openId:h.value}),{aliH5:t,aliQR:m,wechatH5:_,wechatJsapi:d}=n.data;if(t){const I=document.createElement("div");I.innerHTML=t.html,document.body.appendChild(I),document.forms[0].submit()}m&&(window.location.href=m.payUrl),_&&a.fail("\u7528\u5FAE\u4FE1\u7AEF\u6253\u5F00\u8FDB\u884C\u652F\u4ED8"),d&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:d.appId,timeStamp:d.timeStamp,nonceStr:d.nonceStr,package:d.packageStr,signType:d.signType,paySign:d.paySign},function(I){I.err_msg=="get_brand_wcpay_request:ok"&&a.success("\u652F\u4ED8\u6210\u529F\uFF01")})},Y=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},Z=()=>{b.value=!0},ee=()=>{let e=navigator.userAgent;/mobile/i.test(e)?window.scrollTo({top:200,behavior:"smooth"}):window.scrollTo({top:400,behavior:"smooth"}),q(),setTimeout(()=>{cancelAnimationFrame(p.value)},1500)},q=()=>{let e=document.querySelector(".summitBtn");e.style.position="relative",e.style.left=`${Math.random()*5-2.5}px`,e.style.top=`${Math.random()*5-2.5}px`,p.value=requestAnimationFrame(q)};return{animationFrame:p,shakeImage:q,applyBtn:V,scrollToTop:ee,...ne(v),loading:r,userInfo:l,active:M,actions:j,show:A,showImg:L,qrCodeUrlImg:O,isEnd:b,Login:T,buy:X,Pay:U,testPlay:Q,getQueryVariable:k,wxShare:E,link:z,onSelect:J,formatTime:Y,onFinish:Z,time:F,joinActivity:H,joinStatus:y}}},C=o=>(pe("data-v-065b070a"),o=o(),me(),o),ge={class:"box"},ye=C(()=>c("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/3faa0a70-7478-11ee-beaa-d366b7f78c29_bcg.png",alt:""},null,-1)),we={class:"content"},he=C(()=>c("div",{class:"mobile_box"},[c("input",{class:"inputMobile",placeholder:"\u8BF7\u586B\u5199\u624B\u673A\u53F7"})],-1)),be={class:"content_box"},_e={class:"last"},Ie={class:"bottom"},Se={class:"wrapper"},Ae=C(()=>c("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),xe=["src"];function ke(o,r,M,B,f,g){const w=P("van-action-sheet"),h=P("van-overlay");return ie(),ce(le,null,[c("div",ge,[ye,c("div",we,[he,c("div",be,[c("img",{class:"summitBtn",onClick:r[0]||(r[0]=(...p)=>o.applyBtn&&o.applyBtn(...p)),style:{width:"5.2rem"},src:"https://static.medsci.cn/public-image/ms-image/a404f7b0-73c3-11ee-9deb-cbc105e8d30c_btn.png",alt:""})])]),c("div",_e,[c("div",Ie,[c("img",{onClick:r[1]||(r[1]=(...p)=>o.scrollToTop&&o.scrollToTop(...p)),src:"https://static.medsci.cn/public-image/ms-image/eb1e9110-73c3-11ee-9deb-cbc105e8d30c_footerBtn.png",alt:""})])])]),R(w,{show:o.show,"onUpdate:show":r[2]||(r[2]=p=>o.show=p),actions:o.actions,onSelect:o.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),R(h,{show:o.showImg,onClick:r[4]||(r[4]=p=>o.showImg=!1)},{default:re(()=>[c("div",Se,[c("div",{onClick:r[3]||(r[3]=de(()=>{},["stop"]))},[Ae,c("img",{src:o.qrCodeUrlImg,alt:""},null,8,xe)])])]),_:1},8,["show"])],64)}var Le=se(ve,[["render",ke],["__scopeId","data-v-065b070a"]]);export{Le as default};
