import{_ as se,u as te,a as ne,r as t,b as P,o as ae,t as oe,c as b,d as ie,e as ce,f as l,g as U,w as re,F as le,T as u,h as de,p as pe,i as me}from"./index.8f4d7f86.js";import{C as N,s as d}from"./js.cookie.ad72bcd1.js";import{f as ue}from"./falseData.c0306b2a.js";import{m as fe}from"./msSwiper.7d61aaec.js";import{b as v}from"./config.3aca39f6.js";import{b as V}from"./configFreeVip.d32e6c36.js";import"./sanofiConfig.a80970f2.js";const ge={name:"sci",components:{falseData:ue,msSwiper:fe},setup:()=>{te();const n=ne(),c=t(!1),C=t("1"),q=t(""),T=t(!1),L=t(!1),f=t(!1),h=t(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),y=t(!1),o=t(""),g=t(""),B=t(""),M=t(""),_=t(!1),I=t(!1),D=t(""),S=t("college"),O=t(!1),z=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],j=e=>{_.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},i=P({info:{}}),F=P({msg:{}}),x=e=>{for(var s=window.location.search.substring(1),a=s.split("&"),r=0;r<a.length;r++){var p=a[r].split("=");if(p[0]==e)return p[1]}return!1};ae(async()=>{document.title="\u6885\u65AF\u671F\u520A\u4F1A\u5458\u5E74\u5361",R({title:"\u6885\u65AF\u671F\u520A\u4F1A\u5458\u5E74\u5361",summary:"\u70B9\u51FB\u7ACB\u5373\u5F00\u901A",thumb:"https://static.medsci.cn/public-image/ms-image/202404161732_\u671F\u520A\u5206\u4EAB.png"}),N.get("userInfo"),d.get(V+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"new_user_register"}})});const R=async e=>{let s=window.location.href.split("#")[0];const a=await d.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(a.data),wx.error(function(r){console.log(r)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/sci",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/sci",imgUrl:e.thumb,success:function(){}})})},J=()=>{const e=navigator.userAgent,s=n.query.openId||x("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){o.value="WX";const a="wx9096048917ec59ab";if(s)M.value=s;else{const r=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${r}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?o.value="ALI":o.value="ALL"},H=async()=>{J(),u.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=N.get("userInfo");if(e)i.info=JSON.parse(e),i.info.mobile?A():addLoginDom();else{const s=n.query.sso_sessionid||x("sso_sessionid");if(s){const a=await d.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});i.info=a.data,A()}else W()}},W=()=>{addLoginDom()},X=e=>{e.link&&(window.location.href=e.link)},A=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const e=await d.post(V+"/medsci-activity/pay/member-card/",{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,type:"journal_card"});e.code!==200&&e.code!==205&&(u.clear(),u(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),g.value=e.data.data,u.clear(),Z()},Q=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},G=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},K=()=>{f.value=!0},Y=()=>{O.value=!0},Z=async()=>{if(window.innerWidth<750)o.value=="ALL"?_.value=!0:$(o.value);else{const e=await d.post(v+"/payment/pay/merge_qrcode",{accessAppId:S.value,appOrderId:g.value}),{qrCodeUrl:s}=e.data;s&&(I.value=!0,D.value=s);const a=setInterval(()=>{ee(),B.value=="PAID"&&(I.value=!1,clearInterval(a))},3e3)}y.value=!1},ee=async()=>{const e=await d.get(v+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:s}=e.data;B.value=s,s=="PAID"&&(u("\u652F\u4ED8\u6210\u529F"),window.location.href="https://www.medsci.cn/sci/index.do")},$=async e=>{const s=await d.post(v+"/payment/pay/build",{accessAppId:S.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:o.value=="ALL"?"MWEB":o.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){u(s.msg);return}const a=await d.post(v+"/payment/pay/order",{accessAppId:S.value,payOrderId:s.data.payOrderId,openId:M.value}),{aliH5:r,aliQR:p,wechatH5:E,wechatJsapi:m}=a.data;if(r){const w=document.createElement("div");w.innerHTML=r.html,document.body.appendChild(w),document.forms[0].submit()}p&&(window.location.href=p.payUrl),E&&(window.location.href=E.h5Url),m&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:m.appId,timeStamp:m.timeStamp,nonceStr:m.nonceStr,package:m.packageStr,signType:m.signType,paySign:m.paySign},function(w){w.err_msg=="get_brand_wcpay_request:ok"&&(u.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://www.medsci.cn/sci/index.do")})};return{curVal:C,...oe(F),loading:T,userInfo:i,active:L,guize:f,time:h,vipType:q,actions:z,show:_,showImg:I,qrCodeUrlImg:D,isEnd:O,timing:c,getBtn:H,Login:W,Pay:A,testPlay:X,getQueryVariable:x,wxShare:R,link:Q,showGuize:K,formatTime:G,onFinish:Y,onSelect:j}}},k=n=>(pe("data-v-0907f204"),n=n(),me(),n),we={class:"box"},ve=k(()=>l("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/20240612\u671F\u520A\u4F1A\u5458.jpg",alt:""},null,-1)),he={class:"last"},ye=k(()=>l("img",{src:"https://static.medsci.cn/public-image/ms-image/20240612\u671F\u520A\u4F1A\u5458\u65B0.png",alt:""},null,-1)),_e=[ye],Ie=k(()=>l("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Se=["src"];function xe(n,c,C,q,T,L){const f=b("false-data"),h=b("van-action-sheet"),y=b("van-overlay");return ie(),ce(le,null,[l("div",we,[U(f),ve,l("div",he,[l("div",{class:"bottom",onClick:c[0]||(c[0]=(...o)=>n.getBtn&&n.getBtn(...o))},_e)])]),U(h,{show:n.show,"onUpdate:show":c[1]||(c[1]=o=>n.show=o),actions:n.actions,onSelect:n.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),U(y,{show:n.showImg,onClick:c[3]||(c[3]=o=>n.showImg=!1)},{default:re(()=>[l("div",{class:"wrapper",onClick:c[2]||(c[2]=de(()=>{},["stop"]))},[l("div",null,[Ie,l("img",{src:n.qrCodeUrlImg,alt:""},null,8,Se)])])]),_:1},8,["show"])],64)}var Le=se(ge,[["render",xe],["__scopeId","data-v-0907f204"]]);export{Le as default};
