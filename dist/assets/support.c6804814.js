import{C as c,s as m}from"./js.cookie.ad72bcd1.js";import{_ as H,a as K,u as X,r as p,b as C,o as Y,c as M,d as l,e as d,f as o,n as N,S as Z,g as I,w as $,F as L,T as v,C as x,B,E as ee,p as te,i as se}from"./index.8f4d7f86.js";import{a as y,m as ie}from"./sanofiConfig.a80970f2.js";const oe={name:"support",components:{},setup:()=>{const e=K();X();const s=p(""),r=C({}),_=p(!1),b=p(""),g=C({info:{}});p(!1);const f=p(!1),w=p(""),n=p(!1),D=p(0),S=C([]);Y(async()=>{s.value=e.query.id,j(),z(),window.navigator.userAgent.includes("medsci_app")?P("medsci_app"):P("medsci_site")});const P=t=>{let a=c.get("userInfo")?JSON.parse(c.get("userInfo")):{};const i=document.createElement("script");i.id="maidian",i.src="https://img.medsci.cn/web/js/demo/msstatis.min.js";const u=document.getElementsByTagName("script")[0];u.parentNode.insertBefore(i,u),i.onload=function(){const Q=t,F=a.userId,G=a.token.accessToken;window.MsStatis.init(1,Q,F,G),window.MsStatis.disableEvent=["pushState","popstate"]}},z=async()=>{let t=await m.get(y+`/medsciActivity/getActivityTemplateById/${s.value}`);t.status==200&&(Object.assign(r,t.data),document.title=r.name,q(),U({title:r.shareTitle,summary:r.shareSummary,thumb:r.shareIcon}))},j=async()=>{let t=c.get("userInfo")?JSON.parse(c.get("userInfo")):{},a={activityTemplateId:s.value,token:t.token?t.token.accessToken:""};(await m.post(y+"/medsciActivity/insertActivityLog",a)).status==200&&console.log("\u57CB\u70B9\u4E86")},q=async()=>{const t=c.get("userInfo");if(t)g.info=JSON.parse(t),g.info.mobile||addLoginDom(),T();else{const i=e.query.sso_sessionid||E("sso_sessionid");if(i){const u=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:i});g.info=u.data,T()}else if(!(window.location.href.includes("code=")&&window.location.href.includes("state="))){var a=navigator.userAgent.toLowerCase();a.match(/MicroMessenger/i)=="micromessenger"?authWechat():A()}}},T=async()=>{R(),W(),r.usersPerfectInformation&&((await m.get(ie+"/perfectInfo/userInfoStatus?encryptionUserId="+g.info.userId)).data.isCompleteInfo||addPerfectInfoDom())},O=async t=>{t==0?v("\u8BE5\u6D3B\u52A8\u672A\u5BA1\u6838\u901A\u8FC7\uFF0C\u8BF7\u60A8\u5148\u5BA1\u6838\uFF01"):J()},J=async()=>{let t=JSON.parse(c.get("userInfo")),a=r.type==1?{activityTemplateId:s.value,token:t.token.accessToken}:{activityTemplateId:s.value,recommender:e.query.recommender?e.query.recommender:"",token:t.token.accessToken};const i=await m.post(y+"/medsciActivity/usersParticipate",a);i.status==200||i.status==1013?i.data&&i.data.enterpriseWechat?i.data.enterpriseWechatQrCode?(b.value=i.data.enterpriseWechatQrCode,_.value=!0):i.status==200?v("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF01"):v("\u60A8\u5DF2\u53C2\u4E0E\u8FC7\u8BE5\u6D3B\u52A8\uFF01"):i.status==200?v("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF01"):v("\u60A8\u5DF2\u53C2\u4E0E\u8FC7\u8BE5\u6D3B\u52A8\uFF01"):v(i.message)},U=async t=>{let a=c.get("userInfo")?JSON.parse(c.get("userInfo")):{},i=window.location.href.split("#")[0];const u=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:i}});wx.config(u.data),wx.error(function(h){console.log(h)}),wx.ready(function(){wx.onMenuShareAppMessage({title:t.title,desc:t.summary,link:r.type==1?window.location.href:window.location.href.includes("test")?`https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?test&id=${s.value}&recommender=${a.userId?a.userId:""}`:`https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?id=${s.value}&recommender=${a.userId?a.userId:""}`,imgUrl:t.thumb,success:function(){}}),wx.onMenuShareTimeline({title:t.title,desc:t.summary,link:r.type==1?window.location.href:window.location.href.includes("test")?`https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?test&id=${s.value}&recommender=${a.userId?a.userId:""}`:`https://static.medsci.cn/product/medsci/active/zen0214/index.html#/support?id=${s.value}&recommender=${a.userId?a.userId:""}`,imgUrl:t.thumb,success:function(){}})})},A=()=>{addLoginDom()},R=async()=>{let t=c.get("userInfo")?JSON.parse(c.get("userInfo")):{},a={activityTemplateId:s.value,token:t.token?t.token.accessToken:""};const i=await m.post(y+"/medsciActivity/userInvitationPoster",a);i.status==200&&(w.value=i.data.activityInvitationPoster)},E=t=>{for(var a=window.location.search.substring(1),i=a.split("&"),u=0;u<i.length;u++){var h=i[u].split("=");if(h[0]==t)return h[1]}return!1},V=t=>{t==0?v("\u8BE5\u6D3B\u52A8\u672A\u5BA1\u6838\u901A\u8FC7\uFF0C\u8BF7\u60A8\u5148\u5BA1\u6838\uFF01"):f.value=!0},W=async()=>{let t=c.get("userInfo")?JSON.parse(c.get("userInfo")):{},a={activityTemplateId:s.value,token:t.token?t.token.accessToken:""};const i=await m.post(y+"/medsciActivity/getUserRecommender",a);i.status==200&&(S.length=0,S.push(...i.data)),D.value=i.totalSize};return{showImg:_,qrCodeUrlImg:b,activeDetail:r,userInfo:g,postImgUrl:w,showPostImg:f,wxShare:U,Login:A,join:O,shareActivity:V,showMyInvite:n,showInvitePerson:()=>{r.status==0?v("\u8BE5\u6D3B\u52A8\u672A\u5BA1\u6838\u901A\u8FC7\uFF0C\u8BF7\u60A8\u5148\u5BA1\u6838\uFF01"):n.value=!0},totalSize:D,recommenderList:S}}},k=e=>(te("data-v-400c77a8"),e=e(),se(),e),ae=["src"],ne={key:0},re=["src"],ce=["src"],le={key:1},de={key:0,class:"bottom support-bottom"},ue=["src"],me=["src"],pe={key:1,class:"bottom2 support-bottom2"},ve=["src"],ge=["src"],fe={class:"wrapper"},he={class:"img-wrap"},ye=["src"],we={class:"wrapper",style:{height:"100%"}},Ie={class:"img-wrap",style:{height:"100%",overflow:"scroll"}},ke=["src"],_e={class:"wrapper",style:{height:"100%"}},be={class:"content-box"},De={class:"top-wrap"},Se=k(()=>o("img",{src:"https://static.medsci.cn/public-image/ms-image/<EMAIL>",style:{width:"100%"},alt:""},null,-1)),Ce={class:"total"},$e={class:"botm-wrap"},Be=k(()=>o("div",{class:"title"},[o("span",{class:"name"},"\u7528\u6237\u6635\u79F0"),o("span",{style:{"padding-left":"70px"}},"\u53C2\u4E0E\u65F6\u95F4")],-1)),Pe={key:0,class:"list"},Te={class:"name"},Ue={class:"time"},Ae={key:1,class:"empty-list"},Me=k(()=>o("img",{src:"https://static.medsci.cn/public-image/ms-image/30c680d0-4b91-11ed-b66b-937b834e3ef9_\u7F16\<EMAIL>",style:{width:"55px"}},null,-1)),Ne=k(()=>o("p",{style:{"font-size":"12px",color:"#999"}},"\u6682\u65E0\u53D7\u9080\u7684\u597D\u53CB\uFF0C\u8D76\u7D27\u53BB\u9080\u8BF7\u5427",-1)),Le=[Me,Ne],ze={class:"close",style:{"text-align":"center"}};function je(e,s,r,_,b,g){const f=M("van-overlay"),w=M("van-button");return l(),d(L,null,[o("div",{class:Z(e.activeDetail.participateButton?"box2":"box")},[o("img",{class:"image",src:e.activeDetail.poster,alt:""},null,8,ae),e.activeDetail.type==1?(l(),d("div",ne,[e.activeDetail.participateButton?(l(),d("div",{key:1,class:"bottom2",onClick:s[1]||(s[1]=n=>e.join(e.activeDetail.status))},[o("img",{src:e.activeDetail.participateButtonPicture,alt:""},null,8,ce)])):(l(),d("div",{key:0,class:"bottom",onClick:s[0]||(s[0]=n=>e.join(e.activeDetail.status))},[o("img",{src:e.activeDetail.participateButtonPicture,alt:""},null,8,re)]))])):N("",!0),e.activeDetail.type==2?(l(),d("div",le,[o("div",{class:"my-invite-btn",onClick:s[2]||(s[2]=(...n)=>e.showInvitePerson&&e.showInvitePerson(...n))},"\u6211\u7684\u9080\u8BF7"),e.activeDetail.participateButton?(l(),d("div",pe,[o("img",{src:e.activeDetail.participateButtonPicture,alt:"",onClick:s[5]||(s[5]=n=>e.join(e.activeDetail.status))},null,8,ve),o("img",{src:e.activeDetail.participateButtonPictureRight,alt:"",onClick:s[6]||(s[6]=n=>e.shareActivity(e.activeDetail.status))},null,8,ge)])):(l(),d("div",de,[o("img",{src:e.activeDetail.participateButtonPicture,alt:"",onClick:s[3]||(s[3]=n=>e.join(e.activeDetail.status))},null,8,ue),o("img",{src:e.activeDetail.participateButtonPictureRight,alt:"",onClick:s[4]||(s[4]=n=>e.shareActivity(e.activeDetail.status))},null,8,me)]))])):N("",!0)],2),I(f,{show:e.showImg,onClick:s[7]||(s[7]=n=>e.showImg=!1)},{default:$(()=>[o("div",fe,[o("div",he,[o("img",{src:e.qrCodeUrlImg,style:{width:"100%",height:"100%"},alt:""},null,8,ye)])])]),_:1},8,["show"]),I(f,{show:e.showPostImg,onClick:s[8]||(s[8]=n=>e.showPostImg=!1)},{default:$(()=>[o("div",we,[o("div",Ie,[o("img",{src:e.postImgUrl,style:{width:"100%"},alt:""},null,8,ke)])])]),_:1},8,["show"]),I(f,{show:e.showMyInvite,onClick:s[9]||(s[9]=n=>e.showMyInvite=!1)},{default:$(()=>[o("div",_e,[o("div",be,[o("div",De,[Se,o("div",Ce,[x("\u6211\u7684\u9080\u8BF7 "),o("span",null,B(e.totalSize),1)])]),o("div",$e,[Be,e.recommenderList&&e.recommenderList.length>0?(l(),d("div",Pe,[(l(!0),d(L,null,ee(e.recommenderList,n=>(l(),d("div",{class:"list-item",key:n.id},[o("span",Te,B(n.username),1),o("span",Ue,B(n.createdTime.replace(/[^\d]/g,".")),1)]))),128))])):(l(),d("div",Ae,Le))]),o("div",ze,[I(w,{icon:"cross",round:"",type:"primary",color:"rgba(0,0,0,0.80)",size:"small"})])])])]),_:1},8,["show"])],64)}var Ee=H(oe,[["render",je],["__scopeId","data-v-400c77a8"]]);export{Ee as default};
