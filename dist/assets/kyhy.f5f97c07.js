import{_ as K,u as Y,a as Z,r as n,b as N,o as ee,T as y,t as te,c as q,d as se,e as ae,f as d,g as M,w as ne,F as oe,h as ie,p as ce,i as re}from"./index.8f4d7f86.js";import{s as m,C as le}from"./js.cookie.ad72bcd1.js";import{f as de}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b}from"./config.3aca39f6.js";const me={name:"kyhy",components:{falseData:de,msSwiper:pe},setup:()=>{Y();const s=Z(),r=n(!1),E=n(!1),S=n(!1),v=n(new Date("2022/02/20 23:59:59").getTime()-new Date().getTime()),w=n(""),A=n(!1),c=n(""),u=n(""),f=n(""),L=n(""),k=n(!1),O=n(!1),P=n(""),h=n("college"),x=n(!1),$=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],j=e=>{k.value=!1,B(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},g=N({info:{}}),o=N({msg:{}}),I=e=>{for(var t=window.location.search.substring(1),a=t.split("&"),i=0;i<a.length;i++){var p=a[i].split("=");if(p[0]==e)return p[1]}return!1};ee(async()=>{let e;s.query.day==30?(document.title="\u79D1\u7814\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",T({title:"\u79D1\u7814\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",summary:"52\u4E2A\u4E13\u8F91\u8BFE\uFF0C\u8F7B\u677E\u53D1\u8868SCI\u62FF\u4E0B\u56FD\u81EA\u7136",thumb:"https://static.medsci.cn/public-image/ms-image/c6996440-8d34-11ec-bca5-7f892b5df5d6_study_enjoy.png"}),w.value="https://static.medsci.cn/public-image/ms-image/bf4063c0-f088-11ed-9b52-b908d10125b2_\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_16838704104457.png",e="****************"):(document.title="\u6885\u65AF\u533B\u5B66\u79D1\u7814\u5E74\u5EA6\u4F1A\u5458",T({title:"\u6885\u65AF\u533B\u5B66\u79D1\u7814\u5E74\u5EA6\u4F1A\u5458",summary:"52\u4E2A\u4E13\u8F91\u8BFE\uFF0C\u8F7B\u677E\u53D1\u8868SCI\u62FF\u4E0B\u56FD\u81EA\u7136",thumb:"https://static.medsci.cn/public-image/ms-image/c6996440-8d34-11ec-bca5-7f892b5df5d6_study_enjoy.png"}),w.value="https://static.medsci.cn/public-image/ms-image/e907d0c0-d9cd-11ed-b4b6-4d1e60dcd7df_\u6885\u65AF\u533B\u5B66\u79D1\u7814\u4F1A\u54582023\u5E74\u8BFE\u8868\u91CD\u78C5\u6765\u88AD.jpg",e="****************");const t=await m.post("/activity/memberCardDetail",{id:e});o.msg=t.data,o.msg&&o.msg.activityEndTime&&(o.msg.activityEndTime=o.msg.activityEndTime.replace(/-/g,"/"),v.value=new Date(o.msg.activityEndTime).getTime()-new Date().getTime()),v.value<0&&(x.value=!0);let a=navigator.userAgent;a!=null&&a.indexOf("MicroMessenger")>-1&&(s.query.openId||I("openId"))&&D()});const T=async e=>{let t=window.location.href.split("#")[0];const a=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(a.data),wx.error(function(i){console.log(i)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/",imgUrl:e.thumb,success:function(){}})})},z=()=>{const e=navigator.userAgent,t=s.query.openId||I("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){c.value="WX";const a="wx9096048917ec59ab";if(t)L.value=t;else{const i=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${i}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?c.value="ALI":c.value="ALL"},D=async()=>{z();const e=le.get("userInfo");if(y.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)g.info=JSON.parse(e),g.info.mobile||addLoginDom(),C();else{const t=s.query.sso_sessionid||I("sso_sessionid");if(t){const a=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});g.info=a.data,C()}else W()}},W=()=>{addLoginDom()},J=e=>{e.link&&(window.location.href=e.link)},C=async()=>{const{userId:e,userName:t,realName:a,mobile:i,email:p,plaintextUserId:U}=g.info,l=await m.post("/activity/createOrder",{itemId:o.msg.id,itemNum:1,itemPicPath:o.msg.cardImage,itemTitle:o.msg.cardName,itemPrice:o.msg.activityPrice,projectId:o.msg.projectId,orderType:1,mobile:i,payment:0,userId:e,nikeName:t,buyerMessage:"\u79D1\u7814\u5E74\u5EA6\u4F1A\u5458-\u56FD\u81EA\u7136\u57FA\u91D1\u6D3B\u52A8\u9875"});u.value=l.data,y.clear(),Q()},V=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},X=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},F=()=>{S.value=!0},H=()=>{x.value=!0},Q=async()=>{if(window.innerWidth<750)c.value=="ALL"?k.value=!0:B(c.value);else{const e=await m.post(b+"/payment/pay/merge_qrcode",{accessAppId:h.value,appOrderId:u.value}),{qrCodeUrl:t}=e.data;t&&(O.value=!0,P.value=t);const a=setInterval(()=>{G(),f.value=="PAID"&&clearInterval(a)},3e3)}A.value=!1},G=async()=>{const e=await m.get(b+"/payment/pay/query",{params:{appOrderId:u.value}}),{paymentStatus:t}=e.data;f.value=t,t=="PAID"&&y("\u652F\u4ED8\u6210\u529F")},B=async e=>{const t=await m.post(b+"/payment/pay/build",{accessAppId:h.value,appOrderId:u.value,payChannel:e,paySource:"MEDSCI_WEB",payType:c.value=="ALL"?"MWEB":c.value=="WX"?"JSAPI":"NATIVE"});if(console.log({accessAppId:h.value,appOrderId:u.value,payChannel:e,paySource:"MEDSCI_WEB",payType:c.value=="ALL"?"MWEB":c.value=="WX"?"JSAPI":"NATIVE"},"xxx"),t.code!="SUCCESS"){y(t.msg);return}const a=await m.post(b+"/payment/pay/order",{accessAppId:h.value,payOrderId:t.data.payOrderId,openId:L.value}),{aliH5:i,aliQR:p,wechatH5:U,wechatJsapi:l}=a.data;if(i){const _=document.createElement("div");_.innerHTML=i.html,document.body.appendChild(_),document.forms[0].submit()}p&&(window.location.href=p.payUrl),U&&(window.location.href=U.h5Url),l&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:l.appId,timeStamp:l.timeStamp,nonceStr:l.nonceStr,package:l.packageStr,signType:l.signType,paySign:l.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&y.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...te(o),loading:r,userInfo:g,active:E,guize:S,time:v,url:w,actions:$,show:k,showImg:O,qrCodeUrlImg:P,isEnd:x,Login:W,buy:D,Pay:C,testPlay:J,getQueryVariable:I,wxShare:T,link:V,showGuize:F,formatTime:X,onFinish:H,onSelect:j}}},R=s=>(ce("data-v-ff4138b2"),s=s(),re(),s),ue={class:"box"},fe=["src"],ge={class:"last"},ye=R(()=>d("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/869e7b40-ca24-11ed-8dcb-15ac2b21d063_\u6309\u94AE.png",alt:""},null,-1)),ve=[ye],we=R(()=>d("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),he=["src"];function Ie(s,r,E,S,v,w){const A=q("false-data"),c=q("van-action-sheet"),u=q("van-overlay");return se(),ae(oe,null,[d("div",ue,[M(A),d("img",{class:"image",src:s.url,alt:""},null,8,fe),d("div",ge,[d("div",{class:"bottom",onClick:r[0]||(r[0]=(...f)=>s.buy&&s.buy(...f))},ve)])]),M(c,{show:s.show,"onUpdate:show":r[1]||(r[1]=f=>s.show=f),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),M(u,{show:s.showImg,onClick:r[3]||(r[3]=f=>s.showImg=!1)},{default:ne(()=>[d("div",{class:"wrapper",onClick:r[2]||(r[2]=ie(()=>{},["stop"]))},[d("div",null,[we,d("img",{src:s.qrCodeUrlImg,alt:""},null,8,he)])])]),_:1},8,["show"])],64)}var xe=K(me,[["render",Ie],["__scopeId","data-v-ff4138b2"]]);export{xe as default};
