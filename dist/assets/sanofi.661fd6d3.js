import{C as l,s as d}from"./js.cookie.ad72bcd1.js";import{_ as $,a as j,r as f,b as D,o as L,c as q,d as y,e as h,f as r,S as O,g as P,w as E,F as J,T as u}from"./index.8f4d7f86.js";import{a as I,m as x}from"./sanofiConfig.a80970f2.js";const Q={name:"sanofi",components:{},setup:()=>{const s=j(),o=f(""),i=D({}),v=f(!1),g=f(""),c=D({info:{}});f(!1),L(async()=>{o.value=s.query.id,S(),p(),window.navigator.userAgent.includes("medsci_app")?m("medsci_app"):m("medsci_site")});const m=e=>{let a=l.get("userInfo")?JSON.parse(l.get("userInfo")):{};const t=document.createElement("script");t.id="maidian",t.src="https://img.medsci.cn/web/js/demo/msstatis.min.js";const n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n),t.onload=function(){const A=e,M=a.userId,N=a.token.accessToken;window.MsStatis.init(1,A,M,N),window.MsStatis.disableEvent=["pushState","popstate"]}},p=async()=>{let e=await d.get(I+`/medsciActivity/getActivityTemplateById/${o.value}`);e.status==200&&(Object.assign(i,e.data),document.title=i.name,B(),k({title:i.shareTitle,summary:i.shareSummary,thumb:i.shareIcon}))},S=async()=>{let e=l.get("userInfo")?JSON.parse(l.get("userInfo")):{},a={activityTemplateId:o.value,token:e.token?e.token.accessToken:""};(await d.post(I+"/medsciActivity/insertActivityLog",a)).status==200&&console.log("\u57CB\u70B9\u4E86")},B=async()=>{const e=l.get("userInfo");if(e)c.info=JSON.parse(e),c.info.mobile||addLoginDom(),_();else{const a=s.query.sso_sessionid||b("sso_sessionid");if(a){const t=await d.post("/medsciUser/getLoginUserInfoBySid",{sessionid:a});c.info=t.data,_()}else window.location.href.includes("code=")&&window.location.href.includes("state=")||C()}},_=async()=>{i.usersPerfectInformation&&((await d.get(x+"/perfectInfo/userInfoStatus?encryptionUserId="+c.info.userId)).data.isCompleteInfo||addPerfectInfoDom())},T=async e=>{e==0?u("\u8BE5\u6D3B\u52A8\u672A\u5BA1\u6838\u901A\u8FC7\uFF0C\u8BF7\u60A8\u5148\u5BA1\u6838\uFF01"):U()},U=async()=>{let e=JSON.parse(l.get("userInfo")),a={activityTemplateId:o.value,token:e.token.accessToken};const t=await d.post(I+"/medsciActivity/usersParticipate",a);t.status==200||t.status==1013?t.data&&t.data.enterpriseWechat?t.data.enterpriseWechatQrCode?(g.value=t.data.enterpriseWechatQrCode,v.value=!0):t.status==200?u("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF01"):u("\u60A8\u5DF2\u53C2\u4E0E\u8FC7\u8BE5\u6D3B\u52A8\uFF01"):t.status==200?u("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF01"):u("\u60A8\u5DF2\u53C2\u4E0E\u8FC7\u8BE5\u6D3B\u52A8\uFF01"):u(t.message)},k=async e=>{let a=window.location.href.split("#")[0];const t=await d.get("https://ypxcx.medsci.cn/ean/share",{params:{url:a}});wx.config(t.data),wx.error(function(n){console.log(n)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},C=()=>{addLoginDom()},b=e=>{for(var a=window.location.search.substring(1),t=a.split("&"),n=0;n<t.length;n++){var w=t[n].split("=");if(w[0]==e)return w[1]}return!1};return{showImg:v,qrCodeUrlImg:g,activeDetail:i,userInfo:c,wxShare:k,Login:C,join:T}}},V=["src"],W=["src"],F=["src"],z={class:"wrapper"},R={class:"img-wrap"},G=["src"];function H(s,o,i,v,g,c){const m=q("van-overlay");return y(),h(J,null,[r("div",{class:O(s.activeDetail.participateButton?"box2":"box")},[r("img",{class:"image",src:s.activeDetail.poster,alt:""},null,8,V),s.activeDetail.participateButton?(y(),h("div",{key:1,class:"bottom2",onClick:o[1]||(o[1]=p=>s.join(s.activeDetail.status))},[r("img",{src:s.activeDetail.participateButtonPicture,alt:""},null,8,F)])):(y(),h("div",{key:0,class:"bottom",onClick:o[0]||(o[0]=p=>s.join(s.activeDetail.status))},[r("img",{src:s.activeDetail.participateButtonPicture,alt:""},null,8,W)]))],2),P(m,{show:s.showImg,onClick:o[2]||(o[2]=p=>s.showImg=!1)},{default:E(()=>[r("div",z,[r("div",R,[r("img",{src:s.qrCodeUrlImg,style:{width:"100%",height:"100%"},alt:""},null,8,G)])])]),_:1},8,["show"])],64)}var Z=$(Q,[["render",H],["__scopeId","data-v-475b02f2"]]);export{Z as default};
