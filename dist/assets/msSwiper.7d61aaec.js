import{_ as Qe,R as Ze,b as Ke,o as et,t as tt,d as fe,e as ue,f as ge,F as it,E as rt,S as at,p as nt,i as st}from"./index.8f4d7f86.js";function Ge(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function Pe(i,e){i===void 0&&(i={}),e===void 0&&(e={}),Object.keys(e).forEach(function(r){typeof i[r]=="undefined"?i[r]=e[r]:Ge(e[r])&&Ge(i[r])&&Object.keys(e[r]).length>0&&Pe(i[r],e[r])})}var Re={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function G(){var i=typeof document!="undefined"?document:{};return Pe(i,Re),i}var ot={document:Re,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(i){return typeof setTimeout=="undefined"?(i(),null):setTimeout(i,0)},cancelAnimationFrame:function(i){typeof setTimeout!="undefined"&&clearTimeout(i)}};function A(){var i=typeof window!="undefined"?window:{};return Pe(i,ot),i}function lt(i,e){i.prototype=Object.create(e.prototype),i.prototype.constructor=i,i.__proto__=e}function we(i){return we=Object.setPrototypeOf?Object.getPrototypeOf:function(r){return r.__proto__||Object.getPrototypeOf(r)},we(i)}function ee(i,e){return ee=Object.setPrototypeOf||function(t,a){return t.__proto__=a,t},ee(i,e)}function dt(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function K(i,e,r){return dt()?K=Reflect.construct:K=function(a,n,s){var l=[null];l.push.apply(l,n);var o=Function.bind.apply(a,l),d=new o;return s&&ee(d,s.prototype),d},K.apply(null,arguments)}function ft(i){return Function.toString.call(i).indexOf("[native code]")!==-1}function be(i){var e=typeof Map=="function"?new Map:void 0;return be=function(t){if(t===null||!ft(t))return t;if(typeof t!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(t))return e.get(t);e.set(t,a)}function a(){return K(t,arguments,we(this).constructor)}return a.prototype=Object.create(t.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),ee(a,t)},be(i)}function ut(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}function pt(i){var e=i.__proto__;Object.defineProperty(i,"__proto__",{get:function(){return e},set:function(t){e.__proto__=t}})}var N=function(i){lt(e,i);function e(r){var t;return t=i.call.apply(i,[this].concat(r))||this,pt(ut(t)),t}return e}(be(Array));function Y(i){i===void 0&&(i=[]);var e=[];return i.forEach(function(r){Array.isArray(r)?e.push.apply(e,Y(r)):e.push(r)}),e}function Ve(i,e){return Array.prototype.filter.call(i,e)}function ct(i){for(var e=[],r=0;r<i.length;r+=1)e.indexOf(i[r])===-1&&e.push(i[r]);return e}function vt(i,e){if(typeof i!="string")return[i];for(var r=[],t=e.querySelectorAll(i),a=0;a<t.length;a+=1)r.push(t[a]);return r}function h(i,e){var r=A(),t=G(),a=[];if(!e&&i instanceof N)return i;if(!i)return new N(a);if(typeof i=="string"){var n=i.trim();if(n.indexOf("<")>=0&&n.indexOf(">")>=0){var s="div";n.indexOf("<li")===0&&(s="ul"),n.indexOf("<tr")===0&&(s="tbody"),(n.indexOf("<td")===0||n.indexOf("<th")===0)&&(s="tr"),n.indexOf("<tbody")===0&&(s="table"),n.indexOf("<option")===0&&(s="select");var l=t.createElement(s);l.innerHTML=n;for(var o=0;o<l.childNodes.length;o+=1)a.push(l.childNodes[o])}else a=vt(i.trim(),e||t)}else if(i.nodeType||i===r||i===t)a.push(i);else if(Array.isArray(i)){if(i instanceof N)return i;a=i}return new N(ct(a))}h.fn=N.prototype;function ht(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=Y(e.map(function(a){return a.split(" ")}));return this.forEach(function(a){var n;(n=a.classList).add.apply(n,t)}),this}function mt(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=Y(e.map(function(a){return a.split(" ")}));return this.forEach(function(a){var n;(n=a.classList).remove.apply(n,t)}),this}function gt(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=Y(e.map(function(a){return a.split(" ")}));this.forEach(function(a){t.forEach(function(n){a.classList.toggle(n)})})}function wt(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=Y(e.map(function(a){return a.split(" ")}));return Ve(this,function(a){return t.filter(function(n){return a.classList.contains(n)}).length>0}).length>0}function bt(i,e){if(arguments.length===1&&typeof i=="string")return this[0]?this[0].getAttribute(i):void 0;for(var r=0;r<this.length;r+=1)if(arguments.length===2)this[r].setAttribute(i,e);else for(var t in i)this[r][t]=i[t],this[r].setAttribute(t,i[t]);return this}function yt(i){for(var e=0;e<this.length;e+=1)this[e].removeAttribute(i);return this}function Tt(i){for(var e=0;e<this.length;e+=1)this[e].style.transform=i;return this}function Ct(i){for(var e=0;e<this.length;e+=1)this[e].style.transitionDuration=typeof i!="string"?i+"ms":i;return this}function St(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=e[0],a=e[1],n=e[2],s=e[3];typeof e[1]=="function"&&(t=e[0],n=e[1],s=e[2],a=void 0),s||(s=!1);function l(g){var w=g.target;if(!!w){var b=g.target.dom7EventData||[];if(b.indexOf(g)<0&&b.unshift(g),h(w).is(a))n.apply(w,b);else for(var C=h(w).parents(),y=0;y<C.length;y+=1)h(C[y]).is(a)&&n.apply(C[y],b)}}function o(g){var w=g&&g.target?g.target.dom7EventData||[]:[];w.indexOf(g)<0&&w.unshift(g),n.apply(this,w)}for(var d=t.split(" "),f,u=0;u<this.length;u+=1){var p=this[u];if(a)for(f=0;f<d.length;f+=1){var v=d[f];p.dom7LiveListeners||(p.dom7LiveListeners={}),p.dom7LiveListeners[v]||(p.dom7LiveListeners[v]=[]),p.dom7LiveListeners[v].push({listener:n,proxyListener:l}),p.addEventListener(v,l,s)}else for(f=0;f<d.length;f+=1){var c=d[f];p.dom7Listeners||(p.dom7Listeners={}),p.dom7Listeners[c]||(p.dom7Listeners[c]=[]),p.dom7Listeners[c].push({listener:n,proxyListener:o}),p.addEventListener(c,o,s)}}return this}function Et(){for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];var t=e[0],a=e[1],n=e[2],s=e[3];typeof e[1]=="function"&&(t=e[0],n=e[1],s=e[2],a=void 0),s||(s=!1);for(var l=t.split(" "),o=0;o<l.length;o+=1)for(var d=l[o],f=0;f<this.length;f+=1){var u=this[f],p=void 0;if(!a&&u.dom7Listeners?p=u.dom7Listeners[d]:a&&u.dom7LiveListeners&&(p=u.dom7LiveListeners[d]),p&&p.length)for(var c=p.length-1;c>=0;c-=1){var v=p[c];n&&v.listener===n||n&&v.listener&&v.listener.dom7proxy&&v.listener.dom7proxy===n?(u.removeEventListener(d,v.proxyListener,s),p.splice(c,1)):n||(u.removeEventListener(d,v.proxyListener,s),p.splice(c,1))}}return this}function xt(){for(var i=A(),e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];for(var a=r[0].split(" "),n=r[1],s=0;s<a.length;s+=1)for(var l=a[s],o=0;o<this.length;o+=1){var d=this[o];if(i.CustomEvent){var f=new i.CustomEvent(l,{detail:n,bubbles:!0,cancelable:!0});d.dom7EventData=r.filter(function(u,p){return p>0}),d.dispatchEvent(f),d.dom7EventData=[],delete d.dom7EventData}}return this}function Mt(i){var e=this;function r(t){t.target===this&&(i.call(this,t),e.off("transitionend",r))}return i&&e.on("transitionend",r),this}function Pt(i){if(this.length>0){if(i){var e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null}function Lt(i){if(this.length>0){if(i){var e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null}function Ot(){if(this.length>0){var i=A(),e=G(),r=this[0],t=r.getBoundingClientRect(),a=e.body,n=r.clientTop||a.clientTop||0,s=r.clientLeft||a.clientLeft||0,l=r===i?i.scrollY:r.scrollTop,o=r===i?i.scrollX:r.scrollLeft;return{top:t.top+l-n,left:t.left+o-s}}return null}function zt(){var i=A();return this[0]?i.getComputedStyle(this[0],null):{}}function It(i,e){var r=A(),t;if(arguments.length===1)if(typeof i=="string"){if(this[0])return r.getComputedStyle(this[0],null).getPropertyValue(i)}else{for(t=0;t<this.length;t+=1)for(var a in i)this[t].style[a]=i[a];return this}if(arguments.length===2&&typeof i=="string"){for(t=0;t<this.length;t+=1)this[t].style[i]=e;return this}return this}function kt(i){return i?(this.forEach(function(e,r){i.apply(e,[e,r])}),this):this}function At(i){var e=Ve(this,i);return h(e)}function $t(i){if(typeof i=="undefined")return this[0]?this[0].innerHTML:null;for(var e=0;e<this.length;e+=1)this[e].innerHTML=i;return this}function Bt(i){if(typeof i=="undefined")return this[0]?this[0].textContent.trim():null;for(var e=0;e<this.length;e+=1)this[e].textContent=i;return this}function Gt(i){var e=A(),r=G(),t=this[0],a,n;if(!t||typeof i=="undefined")return!1;if(typeof i=="string"){if(t.matches)return t.matches(i);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(i);if(t.msMatchesSelector)return t.msMatchesSelector(i);for(a=h(i),n=0;n<a.length;n+=1)if(a[n]===t)return!0;return!1}if(i===r)return t===r;if(i===e)return t===e;if(i.nodeType||i instanceof N){for(a=i.nodeType?[i]:i,n=0;n<a.length;n+=1)if(a[n]===t)return!0;return!1}return!1}function Dt(){var i=this[0],e;if(i){for(e=0;(i=i.previousSibling)!==null;)i.nodeType===1&&(e+=1);return e}}function Nt(i){if(typeof i=="undefined")return this;var e=this.length;if(i>e-1)return h([]);if(i<0){var r=e+i;return r<0?h([]):h([this[r]])}return h([this[i]])}function Ht(){for(var i,e=G(),r=0;r<arguments.length;r+=1){i=r<0||arguments.length<=r?void 0:arguments[r];for(var t=0;t<this.length;t+=1)if(typeof i=="string"){var a=e.createElement("div");for(a.innerHTML=i;a.firstChild;)this[t].appendChild(a.firstChild)}else if(i instanceof N)for(var n=0;n<i.length;n+=1)this[t].appendChild(i[n]);else this[t].appendChild(i)}return this}function Ft(i){var e=G(),r,t;for(r=0;r<this.length;r+=1)if(typeof i=="string"){var a=e.createElement("div");for(a.innerHTML=i,t=a.childNodes.length-1;t>=0;t-=1)this[r].insertBefore(a.childNodes[t],this[r].childNodes[0])}else if(i instanceof N)for(t=0;t<i.length;t+=1)this[r].insertBefore(i[t],this[r].childNodes[0]);else this[r].insertBefore(i,this[r].childNodes[0]);return this}function Rt(i){return this.length>0?i?this[0].nextElementSibling&&h(this[0].nextElementSibling).is(i)?h([this[0].nextElementSibling]):h([]):this[0].nextElementSibling?h([this[0].nextElementSibling]):h([]):h([])}function Vt(i){var e=[],r=this[0];if(!r)return h([]);for(;r.nextElementSibling;){var t=r.nextElementSibling;i?h(t).is(i)&&e.push(t):e.push(t),r=t}return h(e)}function Wt(i){if(this.length>0){var e=this[0];return i?e.previousElementSibling&&h(e.previousElementSibling).is(i)?h([e.previousElementSibling]):h([]):e.previousElementSibling?h([e.previousElementSibling]):h([])}return h([])}function _t(i){var e=[],r=this[0];if(!r)return h([]);for(;r.previousElementSibling;){var t=r.previousElementSibling;i?h(t).is(i)&&e.push(t):e.push(t),r=t}return h(e)}function jt(i){for(var e=[],r=0;r<this.length;r+=1)this[r].parentNode!==null&&(i?h(this[r].parentNode).is(i)&&e.push(this[r].parentNode):e.push(this[r].parentNode));return h(e)}function qt(i){for(var e=[],r=0;r<this.length;r+=1)for(var t=this[r].parentNode;t;)i?h(t).is(i)&&e.push(t):e.push(t),t=t.parentNode;return h(e)}function Xt(i){var e=this;return typeof i=="undefined"?h([]):(e.is(i)||(e=e.parents(i).eq(0)),e)}function Yt(i){for(var e=[],r=0;r<this.length;r+=1)for(var t=this[r].querySelectorAll(i),a=0;a<t.length;a+=1)e.push(t[a]);return h(e)}function Ut(i){for(var e=[],r=0;r<this.length;r+=1)for(var t=this[r].children,a=0;a<t.length;a+=1)(!i||h(t[a]).is(i))&&e.push(t[a]);return h(e)}function Jt(){for(var i=0;i<this.length;i+=1)this[i].parentNode&&this[i].parentNode.removeChild(this[i]);return this}var De={addClass:ht,removeClass:mt,hasClass:wt,toggleClass:gt,attr:bt,removeAttr:yt,transform:Tt,transition:Ct,on:St,off:Et,trigger:xt,transitionEnd:Mt,outerWidth:Pt,outerHeight:Lt,styles:zt,offset:Ot,css:It,each:kt,html:$t,text:Bt,is:Gt,index:Dt,eq:Nt,append:Ht,prepend:Ft,next:Rt,nextAll:Vt,prev:Wt,prevAll:_t,parent:jt,parents:qt,closest:Xt,find:Yt,children:Ut,filter:At,remove:Jt};Object.keys(De).forEach(function(i){Object.defineProperty(h.fn,i,{value:De[i],writable:!0})});function Qt(i){var e=i;Object.keys(e).forEach(function(r){try{e[r]=null}catch{}try{delete e[r]}catch{}})}function te(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function H(){return Date.now()}function Zt(i){var e=A(),r;return e.getComputedStyle&&(r=e.getComputedStyle(i,null)),!r&&i.currentStyle&&(r=i.currentStyle),r||(r=i.style),r}function Kt(i,e){e===void 0&&(e="x");var r=A(),t,a,n,s=Zt(i);return r.WebKitCSSMatrix?(a=s.transform||s.webkitTransform,a.split(",").length>6&&(a=a.split(", ").map(function(l){return l.replace(",",".")}).join(", ")),n=new r.WebKitCSSMatrix(a==="none"?"":a)):(n=s.MozTransform||s.OTransform||s.MsTransform||s.msTransform||s.transform||s.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),t=n.toString().split(",")),e==="x"&&(r.WebKitCSSMatrix?a=n.m41:t.length===16?a=parseFloat(t[12]):a=parseFloat(t[4])),e==="y"&&(r.WebKitCSSMatrix?a=n.m42:t.length===16?a=parseFloat(t[13]):a=parseFloat(t[5])),a||0}function X(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function ei(i){return typeof window!="undefined"&&typeof window.HTMLElement!="undefined"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function O(){for(var i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"],r=1;r<arguments.length;r+=1){var t=r<0||arguments.length<=r?void 0:arguments[r];if(t!=null&&!ei(t))for(var a=Object.keys(Object(t)).filter(function(d){return e.indexOf(d)<0}),n=0,s=a.length;n<s;n+=1){var l=a[n],o=Object.getOwnPropertyDescriptor(t,l);o!==void 0&&o.enumerable&&(X(i[l])&&X(t[l])?t[l].__swiper__?i[l]=t[l]:O(i[l],t[l]):!X(i[l])&&X(t[l])?(i[l]={},t[l].__swiper__?i[l]=t[l]:O(i[l],t[l])):i[l]=t[l])}}return i}function q(i,e){Object.keys(e).forEach(function(r){X(e[r])&&Object.keys(e[r]).forEach(function(t){typeof e[r][t]=="function"&&(e[r][t]=e[r][t].bind(i))}),i[r]=e[r]})}function j(i){return i===void 0&&(i=""),"."+i.trim().replace(/([\.:\/])/g,"\\$1").replace(/ /g,".")}function We(i,e,r,t){var a=G();return r&&Object.keys(t).forEach(function(n){if(!e[n]&&e.auto===!0){var s=a.createElement("div");s.className=t[n],i.append(s),e[n]=s}}),e}var pe;function ti(){var i=A(),e=G();return{touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch),pointerEvents:!!i.PointerEvent&&"maxTouchPoints"in i.navigator&&i.navigator.maxTouchPoints>=0,observer:function(){return"MutationObserver"in i||"WebkitMutationObserver"in i}(),passiveListener:function(){var t=!1;try{var a=Object.defineProperty({},"passive",{get:function(){t=!0}});i.addEventListener("testPassiveListener",null,a)}catch{}return t}(),gestures:function(){return"ongesturestart"in i}()}}function _e(){return pe||(pe=ti()),pe}var ce;function ii(i){var e=i===void 0?{}:i,r=e.userAgent,t=_e(),a=A(),n=a.navigator.platform,s=r||a.navigator.userAgent,l={ios:!1,android:!1},o=a.screen.width,d=a.screen.height,f=s.match(/(Android);?[\s\/]+([\d.]+)?/),u=s.match(/(iPad).*OS\s([\d_]+)/),p=s.match(/(iPod)(.*OS\s([\d_]+))?/),c=!u&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),v=n==="Win32",g=n==="MacIntel",w=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&g&&t.touch&&w.indexOf(o+"x"+d)>=0&&(u=s.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),g=!1),f&&!v&&(l.os="android",l.android=!0),(u||c||p)&&(l.os="ios",l.ios=!0),l}function ri(i){return i===void 0&&(i={}),ce||(ce=ii(i)),ce}var ve;function ai(){var i=A();function e(){var r=i.navigator.userAgent.toLowerCase();return r.indexOf("safari")>=0&&r.indexOf("chrome")<0&&r.indexOf("android")<0}return{isEdge:!!i.navigator.userAgent.match(/Edge/g),isSafari:e(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent)}}function ni(){return ve||(ve=ai()),ve}var si=function(){var e=A();return typeof e.ResizeObserver!="undefined"},oi={name:"resize",create:function(){var e=this;O(e,{resize:{observer:null,createObserver:function(){!e||e.destroyed||!e.initialized||(e.resize.observer=new ResizeObserver(function(t){var a=e.width,n=e.height,s=a,l=n;t.forEach(function(o){var d=o.contentBoxSize,f=o.contentRect,u=o.target;u&&u!==e.el||(s=f?f.width:(d[0]||d).inlineSize,l=f?f.height:(d[0]||d).blockSize)}),(s!==a||l!==n)&&e.resize.resizeHandler()}),e.resize.observer.observe(e.el))},removeObserver:function(){e.resize.observer&&e.resize.observer.unobserve&&e.el&&(e.resize.observer.unobserve(e.el),e.resize.observer=null)},resizeHandler:function(){!e||e.destroyed||!e.initialized||(e.emit("beforeResize"),e.emit("resize"))},orientationChangeHandler:function(){!e||e.destroyed||!e.initialized||e.emit("orientationchange")}}})},on:{init:function(e){var r=A();if(e.params.resizeObserver&&si()){e.resize.createObserver();return}r.addEventListener("resize",e.resize.resizeHandler),r.addEventListener("orientationchange",e.resize.orientationChangeHandler)},destroy:function(e){var r=A();e.resize.removeObserver(),r.removeEventListener("resize",e.resize.resizeHandler),r.removeEventListener("orientationchange",e.resize.orientationChangeHandler)}}};function ye(){return ye=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},ye.apply(this,arguments)}var li={attach:function(e,r){r===void 0&&(r={});var t=A(),a=this,n=t.MutationObserver||t.WebkitMutationObserver,s=new n(function(l){if(l.length===1){a.emit("observerUpdate",l[0]);return}var o=function(){a.emit("observerUpdate",l[0])};t.requestAnimationFrame?t.requestAnimationFrame(o):t.setTimeout(o,0)});s.observe(e,{attributes:typeof r.attributes=="undefined"?!0:r.attributes,childList:typeof r.childList=="undefined"?!0:r.childList,characterData:typeof r.characterData=="undefined"?!0:r.characterData}),a.observer.observers.push(s)},init:function(){var e=this;if(!(!e.support.observer||!e.params.observer)){if(e.params.observeParents)for(var r=e.$el.parents(),t=0;t<r.length;t+=1)e.observer.attach(r[t]);e.observer.attach(e.$el[0],{childList:e.params.observeSlideChildren}),e.observer.attach(e.$wrapperEl[0],{attributes:!1})}},destroy:function(){var e=this;e.observer.observers.forEach(function(r){r.disconnect()}),e.observer.observers=[]}},di={name:"observer",params:{observer:!1,observeParents:!1,observeSlideChildren:!1},create:function(){var e=this;q(e,{observer:ye({},li,{observers:[]})})},on:{init:function(e){e.observer.init()},destroy:function(e){e.observer.destroy()}}},fi={useParams:function(e){var r=this;!r.modules||Object.keys(r.modules).forEach(function(t){var a=r.modules[t];a.params&&O(e,a.params)})},useModules:function(e){e===void 0&&(e={});var r=this;!r.modules||Object.keys(r.modules).forEach(function(t){var a=r.modules[t],n=e[t]||{};a.on&&r.on&&Object.keys(a.on).forEach(function(s){r.on(s,a.on[s])}),a.create&&a.create.bind(r)(n)})}},ui={on:function(e,r,t){var a=this;if(typeof r!="function")return a;var n=t?"unshift":"push";return e.split(" ").forEach(function(s){a.eventsListeners[s]||(a.eventsListeners[s]=[]),a.eventsListeners[s][n](r)}),a},once:function(e,r,t){var a=this;if(typeof r!="function")return a;function n(){a.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var s=arguments.length,l=new Array(s),o=0;o<s;o++)l[o]=arguments[o];r.apply(a,l)}return n.__emitterProxy=r,a.on(e,n,t)},onAny:function(e,r){var t=this;if(typeof e!="function")return t;var a=r?"unshift":"push";return t.eventsAnyListeners.indexOf(e)<0&&t.eventsAnyListeners[a](e),t},offAny:function(e){var r=this;if(!r.eventsAnyListeners)return r;var t=r.eventsAnyListeners.indexOf(e);return t>=0&&r.eventsAnyListeners.splice(t,1),r},off:function(e,r){var t=this;return t.eventsListeners&&e.split(" ").forEach(function(a){typeof r=="undefined"?t.eventsListeners[a]=[]:t.eventsListeners[a]&&t.eventsListeners[a].forEach(function(n,s){(n===r||n.__emitterProxy&&n.__emitterProxy===r)&&t.eventsListeners[a].splice(s,1)})}),t},emit:function(){var e=this;if(!e.eventsListeners)return e;for(var r,t,a,n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];typeof s[0]=="string"||Array.isArray(s[0])?(r=s[0],t=s.slice(1,s.length),a=e):(r=s[0].events,t=s[0].data,a=s[0].context||e),t.unshift(a);var o=Array.isArray(r)?r:r.split(" ");return o.forEach(function(d){e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(function(f){f.apply(a,[d].concat(t))}),e.eventsListeners&&e.eventsListeners[d]&&e.eventsListeners[d].forEach(function(f){f.apply(a,t)})}),e}};function pi(){var i=this,e,r,t=i.$el;typeof i.params.width!="undefined"&&i.params.width!==null?e=i.params.width:e=t[0].clientWidth,typeof i.params.height!="undefined"&&i.params.height!==null?r=i.params.height:r=t[0].clientHeight,!(e===0&&i.isHorizontal()||r===0&&i.isVertical())&&(e=e-parseInt(t.css("padding-left")||0,10)-parseInt(t.css("padding-right")||0,10),r=r-parseInt(t.css("padding-top")||0,10)-parseInt(t.css("padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(r)&&(r=0),O(i,{width:e,height:r,size:i.isHorizontal()?e:r}))}function ci(){var i=this;function e(B){return i.isHorizontal()?B:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[B]}function r(B,V){return parseFloat(B.getPropertyValue(e(V))||0)}var t=i.params,a=i.$wrapperEl,n=i.size,s=i.rtlTranslate,l=i.wrongRTL,o=i.virtual&&t.virtual.enabled,d=o?i.virtual.slides.length:i.slides.length,f=a.children("."+i.params.slideClass),u=o?i.virtual.slides.length:f.length,p=[],c=[],v=[],g=t.slidesOffsetBefore;typeof g=="function"&&(g=t.slidesOffsetBefore.call(i));var w=t.slidesOffsetAfter;typeof w=="function"&&(w=t.slidesOffsetAfter.call(i));var b=i.snapGrid.length,C=i.slidesGrid.length,y=t.spaceBetween,m=-g,S=0,z=0;if(typeof n!="undefined"){typeof y=="string"&&y.indexOf("%")>=0&&(y=parseFloat(y.replace("%",""))/100*n),i.virtualSize=-y,s?f.css({marginLeft:"",marginBottom:"",marginTop:""}):f.css({marginRight:"",marginBottom:"",marginTop:""});var M;t.slidesPerColumn>1&&(Math.floor(u/t.slidesPerColumn)===u/i.params.slidesPerColumn?M=u:M=Math.ceil(u/t.slidesPerColumn)*t.slidesPerColumn,t.slidesPerView!=="auto"&&t.slidesPerColumnFill==="row"&&(M=Math.max(M,t.slidesPerView*t.slidesPerColumn)));for(var T,P=t.slidesPerColumn,E=M/P,L=Math.floor(u/t.slidesPerColumn),k=0;k<u;k+=1){T=0;var x=f.eq(k);if(t.slidesPerColumn>1){var D=void 0,I=void 0,$=void 0;if(t.slidesPerColumnFill==="row"&&t.slidesPerGroup>1){var F=Math.floor(k/(t.slidesPerGroup*t.slidesPerColumn)),R=k-t.slidesPerColumn*t.slidesPerGroup*F,U=F===0?t.slidesPerGroup:Math.min(Math.ceil((u-F*P*t.slidesPerGroup)/P),t.slidesPerGroup);$=Math.floor(R/U),I=R-$*U+F*t.slidesPerGroup,D=I+$*M/P,x.css({"-webkit-box-ordinal-group":D,"-moz-box-ordinal-group":D,"-ms-flex-order":D,"-webkit-order":D,order:D})}else t.slidesPerColumnFill==="column"?(I=Math.floor(k/P),$=k-I*P,(I>L||I===L&&$===P-1)&&($+=1,$>=P&&($=0,I+=1))):($=Math.floor(k/E),I=k-$*E);x.css(e("margin-top"),$!==0?t.spaceBetween&&t.spaceBetween+"px":"")}if(x.css("display")!=="none"){if(t.slidesPerView==="auto"){var W=getComputedStyle(x[0]),ie=x[0].style.transform,re=x[0].style.webkitTransform;if(ie&&(x[0].style.transform="none"),re&&(x[0].style.webkitTransform="none"),t.roundLengths)T=i.isHorizontal()?x.outerWidth(!0):x.outerHeight(!0);else{var Oe=r(W,"width"),qe=r(W,"padding-left"),Xe=r(W,"padding-right"),ze=r(W,"margin-left"),Ie=r(W,"margin-right"),ke=W.getPropertyValue("box-sizing");if(ke&&ke==="border-box")T=Oe+ze+Ie;else{var Ae=x[0],Ye=Ae.clientWidth,Ue=Ae.offsetWidth;T=Oe+qe+Xe+ze+Ie+(Ue-Ye)}}ie&&(x[0].style.transform=ie),re&&(x[0].style.webkitTransform=re),t.roundLengths&&(T=Math.floor(T))}else T=(n-(t.slidesPerView-1)*y)/t.slidesPerView,t.roundLengths&&(T=Math.floor(T)),f[k]&&(f[k].style[e("width")]=T+"px");f[k]&&(f[k].swiperSlideSize=T),v.push(T),t.centeredSlides?(m=m+T/2+S/2+y,S===0&&k!==0&&(m=m-n/2-y),k===0&&(m=m-n/2-y),Math.abs(m)<1/1e3&&(m=0),t.roundLengths&&(m=Math.floor(m)),z%t.slidesPerGroup===0&&p.push(m),c.push(m)):(t.roundLengths&&(m=Math.floor(m)),(z-Math.min(i.params.slidesPerGroupSkip,z))%i.params.slidesPerGroup===0&&p.push(m),c.push(m),m=m+T+y),i.virtualSize+=T+y,S=T,z+=1}}i.virtualSize=Math.max(i.virtualSize,n)+w;var _;if(s&&l&&(t.effect==="slide"||t.effect==="coverflow")&&a.css({width:i.virtualSize+t.spaceBetween+"px"}),t.setWrapperSize){var ae;a.css((ae={},ae[e("width")]=i.virtualSize+t.spaceBetween+"px",ae))}if(t.slidesPerColumn>1){var ne;if(i.virtualSize=(T+t.spaceBetween)*M,i.virtualSize=Math.ceil(i.virtualSize/t.slidesPerColumn)-t.spaceBetween,a.css((ne={},ne[e("width")]=i.virtualSize+t.spaceBetween+"px",ne)),t.centeredSlides){_=[];for(var J=0;J<p.length;J+=1){var se=p[J];t.roundLengths&&(se=Math.floor(se)),p[J]<i.virtualSize+p[0]&&_.push(se)}p=_}}if(!t.centeredSlides){_=[];for(var Q=0;Q<p.length;Q+=1){var oe=p[Q];t.roundLengths&&(oe=Math.floor(oe)),p[Q]<=i.virtualSize-n&&_.push(oe)}p=_,Math.floor(i.virtualSize-n)-Math.floor(p[p.length-1])>1&&p.push(i.virtualSize-n)}if(p.length===0&&(p=[0]),t.spaceBetween!==0){var le,Je=i.isHorizontal()&&s?"marginLeft":e("marginRight");f.filter(function(B,V){return t.cssMode?V!==f.length-1:!0}).css((le={},le[Je]=y+"px",le))}if(t.centeredSlides&&t.centeredSlidesBounds){var de=0;v.forEach(function(B){de+=B+(t.spaceBetween?t.spaceBetween:0)}),de-=t.spaceBetween;var $e=de-n;p=p.map(function(B){return B<0?-g:B>$e?$e+w:B})}if(t.centerInsufficientSlides){var Z=0;if(v.forEach(function(B){Z+=B+(t.spaceBetween?t.spaceBetween:0)}),Z-=t.spaceBetween,Z<n){var Be=(n-Z)/2;p.forEach(function(B,V){p[V]=B-Be}),c.forEach(function(B,V){c[V]=B+Be})}}O(i,{slides:f,snapGrid:p,slidesGrid:c,slidesSizesGrid:v}),u!==d&&i.emit("slidesLengthChange"),p.length!==b&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),c.length!==C&&i.emit("slidesGridLengthChange"),(t.watchSlidesProgress||t.watchSlidesVisibility)&&i.updateSlidesOffset()}}function vi(i){var e=this,r=[],t=e.virtual&&e.params.virtual.enabled,a=0,n;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);var s=function(f){return t?e.slides.filter(function(u){return parseInt(u.getAttribute("data-swiper-slide-index"),10)===f})[0]:e.slides.eq(f)[0]};if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)e.visibleSlides.each(function(d){r.push(d)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){var l=e.activeIndex+n;if(l>e.slides.length&&!t)break;r.push(s(l))}else r.push(s(e.activeIndex));for(n=0;n<r.length;n+=1)if(typeof r[n]!="undefined"){var o=r[n].offsetHeight;a=o>a?o:a}a&&e.$wrapperEl.css("height",a+"px")}function hi(){for(var i=this,e=i.slides,r=0;r<e.length;r+=1)e[r].swiperSlideOffset=i.isHorizontal()?e[r].offsetLeft:e[r].offsetTop}function mi(i){i===void 0&&(i=this&&this.translate||0);var e=this,r=e.params,t=e.slides,a=e.rtlTranslate;if(t.length!==0){typeof t[0].swiperSlideOffset=="undefined"&&e.updateSlidesOffset();var n=-i;a&&(n=i),t.removeClass(r.slideVisibleClass),e.visibleSlidesIndexes=[],e.visibleSlides=[];for(var s=0;s<t.length;s+=1){var l=t[s],o=(n+(r.centeredSlides?e.minTranslate():0)-l.swiperSlideOffset)/(l.swiperSlideSize+r.spaceBetween);if(r.watchSlidesVisibility||r.centeredSlides&&r.autoHeight){var d=-(n-l.swiperSlideOffset),f=d+e.slidesSizesGrid[s],u=d>=0&&d<e.size-1||f>1&&f<=e.size||d<=0&&f>=e.size;u&&(e.visibleSlides.push(l),e.visibleSlidesIndexes.push(s),t.eq(s).addClass(r.slideVisibleClass))}l.progress=a?-o:o}e.visibleSlides=h(e.visibleSlides)}}function gi(i){var e=this;if(typeof i=="undefined"){var r=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*r||0}var t=e.params,a=e.maxTranslate()-e.minTranslate(),n=e.progress,s=e.isBeginning,l=e.isEnd,o=s,d=l;a===0?(n=0,s=!0,l=!0):(n=(i-e.minTranslate())/a,s=n<=0,l=n>=1),O(e,{progress:n,isBeginning:s,isEnd:l}),(t.watchSlidesProgress||t.watchSlidesVisibility||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),s&&!o&&e.emit("reachBeginning toEdge"),l&&!d&&e.emit("reachEnd toEdge"),(o&&!s||d&&!l)&&e.emit("fromEdge"),e.emit("progress",n)}function wi(){var i=this,e=i.slides,r=i.params,t=i.$wrapperEl,a=i.activeIndex,n=i.realIndex,s=i.virtual&&r.virtual.enabled;e.removeClass(r.slideActiveClass+" "+r.slideNextClass+" "+r.slidePrevClass+" "+r.slideDuplicateActiveClass+" "+r.slideDuplicateNextClass+" "+r.slideDuplicatePrevClass);var l;s?l=i.$wrapperEl.find("."+r.slideClass+'[data-swiper-slide-index="'+a+'"]'):l=e.eq(a),l.addClass(r.slideActiveClass),r.loop&&(l.hasClass(r.slideDuplicateClass)?t.children("."+r.slideClass+":not(."+r.slideDuplicateClass+')[data-swiper-slide-index="'+n+'"]').addClass(r.slideDuplicateActiveClass):t.children("."+r.slideClass+"."+r.slideDuplicateClass+'[data-swiper-slide-index="'+n+'"]').addClass(r.slideDuplicateActiveClass));var o=l.nextAll("."+r.slideClass).eq(0).addClass(r.slideNextClass);r.loop&&o.length===0&&(o=e.eq(0),o.addClass(r.slideNextClass));var d=l.prevAll("."+r.slideClass).eq(0).addClass(r.slidePrevClass);r.loop&&d.length===0&&(d=e.eq(-1),d.addClass(r.slidePrevClass)),r.loop&&(o.hasClass(r.slideDuplicateClass)?t.children("."+r.slideClass+":not(."+r.slideDuplicateClass+')[data-swiper-slide-index="'+o.attr("data-swiper-slide-index")+'"]').addClass(r.slideDuplicateNextClass):t.children("."+r.slideClass+"."+r.slideDuplicateClass+'[data-swiper-slide-index="'+o.attr("data-swiper-slide-index")+'"]').addClass(r.slideDuplicateNextClass),d.hasClass(r.slideDuplicateClass)?t.children("."+r.slideClass+":not(."+r.slideDuplicateClass+')[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(r.slideDuplicatePrevClass):t.children("."+r.slideClass+"."+r.slideDuplicateClass+'[data-swiper-slide-index="'+d.attr("data-swiper-slide-index")+'"]').addClass(r.slideDuplicatePrevClass)),i.emitSlidesClasses()}function bi(i){var e=this,r=e.rtlTranslate?e.translate:-e.translate,t=e.slidesGrid,a=e.snapGrid,n=e.params,s=e.activeIndex,l=e.realIndex,o=e.snapIndex,d=i,f;if(typeof d=="undefined"){for(var u=0;u<t.length;u+=1)typeof t[u+1]!="undefined"?r>=t[u]&&r<t[u+1]-(t[u+1]-t[u])/2?d=u:r>=t[u]&&r<t[u+1]&&(d=u+1):r>=t[u]&&(d=u);n.normalizeSlideIndex&&(d<0||typeof d=="undefined")&&(d=0)}if(a.indexOf(r)>=0)f=a.indexOf(r);else{var p=Math.min(n.slidesPerGroupSkip,d);f=p+Math.floor((d-p)/n.slidesPerGroup)}if(f>=a.length&&(f=a.length-1),d===s){f!==o&&(e.snapIndex=f,e.emit("snapIndexChange"));return}var c=parseInt(e.slides.eq(d).attr("data-swiper-slide-index")||d,10);O(e,{snapIndex:f,realIndex:c,previousIndex:s,activeIndex:d}),e.emit("activeIndexChange"),e.emit("snapIndexChange"),l!==c&&e.emit("realIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&e.emit("slideChange")}function yi(i){var e=this,r=e.params,t=h(i.target).closest("."+r.slideClass)[0],a=!1,n;if(t){for(var s=0;s<e.slides.length;s+=1)if(e.slides[s]===t){a=!0,n=s;break}}if(t&&a)e.clickedSlide=t,e.virtual&&e.params.virtual.enabled?e.clickedIndex=parseInt(h(t).attr("data-swiper-slide-index"),10):e.clickedIndex=n;else{e.clickedSlide=void 0,e.clickedIndex=void 0;return}r.slideToClickedSlide&&e.clickedIndex!==void 0&&e.clickedIndex!==e.activeIndex&&e.slideToClickedSlide()}var Ti={updateSize:pi,updateSlides:ci,updateAutoHeight:vi,updateSlidesOffset:hi,updateSlidesProgress:mi,updateProgress:gi,updateSlidesClasses:wi,updateActiveIndex:bi,updateClickedSlide:yi};function Ci(i){i===void 0&&(i=this.isHorizontal()?"x":"y");var e=this,r=e.params,t=e.rtlTranslate,a=e.translate,n=e.$wrapperEl;if(r.virtualTranslate)return t?-a:a;if(r.cssMode)return a;var s=Kt(n[0],i);return t&&(s=-s),s||0}function Si(i,e){var r=this,t=r.rtlTranslate,a=r.params,n=r.$wrapperEl,s=r.wrapperEl,l=r.progress,o=0,d=0,f=0;r.isHorizontal()?o=t?-i:i:d=i,a.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),a.cssMode?s[r.isHorizontal()?"scrollLeft":"scrollTop"]=r.isHorizontal()?-o:-d:a.virtualTranslate||n.transform("translate3d("+o+"px, "+d+"px, "+f+"px)"),r.previousTranslate=r.translate,r.translate=r.isHorizontal()?o:d;var u,p=r.maxTranslate()-r.minTranslate();p===0?u=0:u=(i-r.minTranslate())/p,u!==l&&r.updateProgress(i),r.emit("setTranslate",r.translate,e)}function Ei(){return-this.snapGrid[0]}function xi(){return-this.snapGrid[this.snapGrid.length-1]}function Mi(i,e,r,t,a){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),r===void 0&&(r=!0),t===void 0&&(t=!0);var n=this,s=n.params,l=n.wrapperEl;if(n.animating&&s.preventInteractionOnTransition)return!1;var o=n.minTranslate(),d=n.maxTranslate(),f;if(t&&i>o?f=o:t&&i<d?f=d:f=i,n.updateProgress(f),s.cssMode){var u=n.isHorizontal();if(e===0)l[u?"scrollLeft":"scrollTop"]=-f;else if(l.scrollTo){var p;l.scrollTo((p={},p[u?"left":"top"]=-f,p.behavior="smooth",p))}else l[u?"scrollLeft":"scrollTop"]=-f;return!0}return e===0?(n.setTransition(0),n.setTranslate(f),r&&(n.emit("beforeTransitionStart",e,a),n.emit("transitionEnd"))):(n.setTransition(e),n.setTranslate(f),r&&(n.emit("beforeTransitionStart",e,a),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(v){!n||n.destroyed||v.target===this&&(n.$wrapperEl[0].removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.$wrapperEl[0].removeEventListener("webkitTransitionEnd",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,r&&n.emit("transitionEnd"))}),n.$wrapperEl[0].addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.$wrapperEl[0].addEventListener("webkitTransitionEnd",n.onTranslateToWrapperTransitionEnd))),!0}var Pi={getTranslate:Ci,setTranslate:Si,minTranslate:Ei,maxTranslate:xi,translateTo:Mi};function Li(i,e){var r=this;r.params.cssMode||r.$wrapperEl.transition(i),r.emit("setTransition",i,e)}function Oi(i,e){i===void 0&&(i=!0);var r=this,t=r.activeIndex,a=r.params,n=r.previousIndex;if(!a.cssMode){a.autoHeight&&r.updateAutoHeight();var s=e;if(s||(t>n?s="next":t<n?s="prev":s="reset"),r.emit("transitionStart"),i&&t!==n){if(s==="reset"){r.emit("slideResetTransitionStart");return}r.emit("slideChangeTransitionStart"),s==="next"?r.emit("slideNextTransitionStart"):r.emit("slidePrevTransitionStart")}}}function zi(i,e){i===void 0&&(i=!0);var r=this,t=r.activeIndex,a=r.previousIndex,n=r.params;if(r.animating=!1,!n.cssMode){r.setTransition(0);var s=e;if(s||(t>a?s="next":t<a?s="prev":s="reset"),r.emit("transitionEnd"),i&&t!==a){if(s==="reset"){r.emit("slideResetTransitionEnd");return}r.emit("slideChangeTransitionEnd"),s==="next"?r.emit("slideNextTransitionEnd"):r.emit("slidePrevTransitionEnd")}}}var Ii={setTransition:Li,transitionStart:Oi,transitionEnd:zi};function ki(i,e,r,t,a){if(i===void 0&&(i=0),e===void 0&&(e=this.params.speed),r===void 0&&(r=!0),typeof i!="number"&&typeof i!="string")throw new Error("The 'index' argument cannot have type other than 'number' or 'string'. ["+typeof i+"] given.");if(typeof i=="string"){var n=parseInt(i,10),s=isFinite(n);if(!s)throw new Error("The passed-in 'index' (string) couldn't be converted to 'number'. ["+i+"] given.");i=n}var l=this,o=i;o<0&&(o=0);var d=l.params,f=l.snapGrid,u=l.slidesGrid,p=l.previousIndex,c=l.activeIndex,v=l.rtlTranslate,g=l.wrapperEl,w=l.enabled;if(l.animating&&d.preventInteractionOnTransition||!w&&!t&&!a)return!1;var b=Math.min(l.params.slidesPerGroupSkip,o),C=b+Math.floor((o-b)/l.params.slidesPerGroup);C>=f.length&&(C=f.length-1),(c||d.initialSlide||0)===(p||0)&&r&&l.emit("beforeSlideChangeStart");var y=-f[C];if(l.updateProgress(y),d.normalizeSlideIndex)for(var m=0;m<u.length;m+=1){var S=-Math.floor(y*100),z=Math.floor(u[m]*100),M=Math.floor(u[m+1]*100);typeof u[m+1]!="undefined"?S>=z&&S<M-(M-z)/2?o=m:S>=z&&S<M&&(o=m+1):S>=z&&(o=m)}if(l.initialized&&o!==c&&(!l.allowSlideNext&&y<l.translate&&y<l.minTranslate()||!l.allowSlidePrev&&y>l.translate&&y>l.maxTranslate()&&(c||0)!==o))return!1;var T;if(o>c?T="next":o<c?T="prev":T="reset",v&&-y===l.translate||!v&&y===l.translate)return l.updateActiveIndex(o),d.autoHeight&&l.updateAutoHeight(),l.updateSlidesClasses(),d.effect!=="slide"&&l.setTranslate(y),T!=="reset"&&(l.transitionStart(r,T),l.transitionEnd(r,T)),!1;if(d.cssMode){var P=l.isHorizontal(),E=-y;if(v&&(E=g.scrollWidth-g.offsetWidth-E),e===0)g[P?"scrollLeft":"scrollTop"]=E;else if(g.scrollTo){var L;g.scrollTo((L={},L[P?"left":"top"]=E,L.behavior="smooth",L))}else g[P?"scrollLeft":"scrollTop"]=E;return!0}return e===0?(l.setTransition(0),l.setTranslate(y),l.updateActiveIndex(o),l.updateSlidesClasses(),l.emit("beforeTransitionStart",e,t),l.transitionStart(r,T),l.transitionEnd(r,T)):(l.setTransition(e),l.setTranslate(y),l.updateActiveIndex(o),l.updateSlidesClasses(),l.emit("beforeTransitionStart",e,t),l.transitionStart(r,T),l.animating||(l.animating=!0,l.onSlideToWrapperTransitionEnd||(l.onSlideToWrapperTransitionEnd=function(x){!l||l.destroyed||x.target===this&&(l.$wrapperEl[0].removeEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.$wrapperEl[0].removeEventListener("webkitTransitionEnd",l.onSlideToWrapperTransitionEnd),l.onSlideToWrapperTransitionEnd=null,delete l.onSlideToWrapperTransitionEnd,l.transitionEnd(r,T))}),l.$wrapperEl[0].addEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.$wrapperEl[0].addEventListener("webkitTransitionEnd",l.onSlideToWrapperTransitionEnd))),!0}function Ai(i,e,r,t){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),r===void 0&&(r=!0);var a=this,n=i;return a.params.loop&&(n+=a.loopedSlides),a.slideTo(n,e,r,t)}function $i(i,e,r){i===void 0&&(i=this.params.speed),e===void 0&&(e=!0);var t=this,a=t.params,n=t.animating,s=t.enabled;if(!s)return t;var l=t.activeIndex<a.slidesPerGroupSkip?1:a.slidesPerGroup;if(a.loop){if(n&&a.loopPreventsSlide)return!1;t.loopFix(),t._clientLeft=t.$wrapperEl[0].clientLeft}return t.slideTo(t.activeIndex+l,i,e,r)}function Bi(i,e,r){i===void 0&&(i=this.params.speed),e===void 0&&(e=!0);var t=this,a=t.params,n=t.animating,s=t.snapGrid,l=t.slidesGrid,o=t.rtlTranslate,d=t.enabled;if(!d)return t;if(a.loop){if(n&&a.loopPreventsSlide)return!1;t.loopFix(),t._clientLeft=t.$wrapperEl[0].clientLeft}var f=o?t.translate:-t.translate;function u(w){return w<0?-Math.floor(Math.abs(w)):Math.floor(w)}var p=u(f),c=s.map(function(w){return u(w)}),v=s[c.indexOf(p)-1];typeof v=="undefined"&&a.cssMode&&s.forEach(function(w){!v&&p>=w&&(v=w)});var g;return typeof v!="undefined"&&(g=l.indexOf(v),g<0&&(g=t.activeIndex-1)),t.slideTo(g,i,e,r)}function Gi(i,e,r){i===void 0&&(i=this.params.speed),e===void 0&&(e=!0);var t=this;return t.slideTo(t.activeIndex,i,e,r)}function Di(i,e,r,t){i===void 0&&(i=this.params.speed),e===void 0&&(e=!0),t===void 0&&(t=.5);var a=this,n=a.activeIndex,s=Math.min(a.params.slidesPerGroupSkip,n),l=s+Math.floor((n-s)/a.params.slidesPerGroup),o=a.rtlTranslate?a.translate:-a.translate;if(o>=a.snapGrid[l]){var d=a.snapGrid[l],f=a.snapGrid[l+1];o-d>(f-d)*t&&(n+=a.params.slidesPerGroup)}else{var u=a.snapGrid[l-1],p=a.snapGrid[l];o-u<=(p-u)*t&&(n-=a.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,a.slidesGrid.length-1),a.slideTo(n,i,e,r)}function Ni(){var i=this,e=i.params,r=i.$wrapperEl,t=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView,a=i.clickedIndex,n;if(e.loop){if(i.animating)return;n=parseInt(h(i.clickedSlide).attr("data-swiper-slide-index"),10),e.centeredSlides?a<i.loopedSlides-t/2||a>i.slides.length-i.loopedSlides+t/2?(i.loopFix(),a=r.children("."+e.slideClass+'[data-swiper-slide-index="'+n+'"]:not(.'+e.slideDuplicateClass+")").eq(0).index(),te(function(){i.slideTo(a)})):i.slideTo(a):a>i.slides.length-t?(i.loopFix(),a=r.children("."+e.slideClass+'[data-swiper-slide-index="'+n+'"]:not(.'+e.slideDuplicateClass+")").eq(0).index(),te(function(){i.slideTo(a)})):i.slideTo(a)}else i.slideTo(a)}var Hi={slideTo:ki,slideToLoop:Ai,slideNext:$i,slidePrev:Bi,slideReset:Gi,slideToClosest:Di,slideToClickedSlide:Ni};function Fi(){var i=this,e=G(),r=i.params,t=i.$wrapperEl;t.children("."+r.slideClass+"."+r.slideDuplicateClass).remove();var a=t.children("."+r.slideClass);if(r.loopFillGroupWithBlank){var n=r.slidesPerGroup-a.length%r.slidesPerGroup;if(n!==r.slidesPerGroup){for(var s=0;s<n;s+=1){var l=h(e.createElement("div")).addClass(r.slideClass+" "+r.slideBlankClass);t.append(l)}a=t.children("."+r.slideClass)}}r.slidesPerView==="auto"&&!r.loopedSlides&&(r.loopedSlides=a.length),i.loopedSlides=Math.ceil(parseFloat(r.loopedSlides||r.slidesPerView,10)),i.loopedSlides+=r.loopAdditionalSlides,i.loopedSlides>a.length&&(i.loopedSlides=a.length);var o=[],d=[];a.each(function(p,c){var v=h(p);c<i.loopedSlides&&d.push(p),c<a.length&&c>=a.length-i.loopedSlides&&o.push(p),v.attr("data-swiper-slide-index",c)});for(var f=0;f<d.length;f+=1)t.append(h(d[f].cloneNode(!0)).addClass(r.slideDuplicateClass));for(var u=o.length-1;u>=0;u-=1)t.prepend(h(o[u].cloneNode(!0)).addClass(r.slideDuplicateClass))}function Ri(){var i=this;i.emit("beforeLoopFix");var e=i.activeIndex,r=i.slides,t=i.loopedSlides,a=i.allowSlidePrev,n=i.allowSlideNext,s=i.snapGrid,l=i.rtlTranslate,o;i.allowSlidePrev=!0,i.allowSlideNext=!0;var d=-s[e],f=d-i.getTranslate();if(e<t){o=r.length-t*3+e,o+=t;var u=i.slideTo(o,0,!1,!0);u&&f!==0&&i.setTranslate((l?-i.translate:i.translate)-f)}else if(e>=r.length-t){o=-r.length+e+t,o+=t;var p=i.slideTo(o,0,!1,!0);p&&f!==0&&i.setTranslate((l?-i.translate:i.translate)-f)}i.allowSlidePrev=a,i.allowSlideNext=n,i.emit("loopFix")}function Vi(){var i=this,e=i.$wrapperEl,r=i.params,t=i.slides;e.children("."+r.slideClass+"."+r.slideDuplicateClass+",."+r.slideClass+"."+r.slideBlankClass).remove(),t.removeAttr("data-swiper-slide-index")}var Wi={loopCreate:Fi,loopFix:Ri,loopDestroy:Vi};function _i(i){var e=this;if(!(e.support.touch||!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)){var r=e.el;r.style.cursor="move",r.style.cursor=i?"-webkit-grabbing":"-webkit-grab",r.style.cursor=i?"-moz-grabbin":"-moz-grab",r.style.cursor=i?"grabbing":"grab"}}function ji(){var i=this;i.support.touch||i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.el.style.cursor="")}var qi={setGrabCursor:_i,unsetGrabCursor:ji};function Xi(i){var e=this,r=e.$wrapperEl,t=e.params;if(t.loop&&e.loopDestroy(),typeof i=="object"&&"length"in i)for(var a=0;a<i.length;a+=1)i[a]&&r.append(i[a]);else r.append(i);t.loop&&e.loopCreate(),t.observer&&e.support.observer||e.update()}function Yi(i){var e=this,r=e.params,t=e.$wrapperEl,a=e.activeIndex;r.loop&&e.loopDestroy();var n=a+1;if(typeof i=="object"&&"length"in i){for(var s=0;s<i.length;s+=1)i[s]&&t.prepend(i[s]);n=a+i.length}else t.prepend(i);r.loop&&e.loopCreate(),r.observer&&e.support.observer||e.update(),e.slideTo(n,0,!1)}function Ui(i,e){var r=this,t=r.$wrapperEl,a=r.params,n=r.activeIndex,s=n;a.loop&&(s-=r.loopedSlides,r.loopDestroy(),r.slides=t.children("."+a.slideClass));var l=r.slides.length;if(i<=0){r.prependSlide(e);return}if(i>=l){r.appendSlide(e);return}for(var o=s>i?s+1:s,d=[],f=l-1;f>=i;f-=1){var u=r.slides.eq(f);u.remove(),d.unshift(u)}if(typeof e=="object"&&"length"in e){for(var p=0;p<e.length;p+=1)e[p]&&t.append(e[p]);o=s>i?s+e.length:s}else t.append(e);for(var c=0;c<d.length;c+=1)t.append(d[c]);a.loop&&r.loopCreate(),a.observer&&r.support.observer||r.update(),a.loop?r.slideTo(o+r.loopedSlides,0,!1):r.slideTo(o,0,!1)}function Ji(i){var e=this,r=e.params,t=e.$wrapperEl,a=e.activeIndex,n=a;r.loop&&(n-=e.loopedSlides,e.loopDestroy(),e.slides=t.children("."+r.slideClass));var s=n,l;if(typeof i=="object"&&"length"in i){for(var o=0;o<i.length;o+=1)l=i[o],e.slides[l]&&e.slides.eq(l).remove(),l<s&&(s-=1);s=Math.max(s,0)}else l=i,e.slides[l]&&e.slides.eq(l).remove(),l<s&&(s-=1),s=Math.max(s,0);r.loop&&e.loopCreate(),r.observer&&e.support.observer||e.update(),r.loop?e.slideTo(s+e.loopedSlides,0,!1):e.slideTo(s,0,!1)}function Qi(){for(var i=this,e=[],r=0;r<i.slides.length;r+=1)e.push(r);i.removeSlide(e)}var Zi={appendSlide:Xi,prependSlide:Yi,addSlide:Ui,removeSlide:Ji,removeAllSlides:Qi};function Ki(i,e){e===void 0&&(e=this);function r(t){if(!t||t===G()||t===A())return null;t.assignedSlot&&(t=t.assignedSlot);var a=t.closest(i);return a||r(t.getRootNode().host)}return r(e)}function er(i){var e=this,r=G(),t=A(),a=e.touchEventsData,n=e.params,s=e.touches,l=e.enabled;if(!!l&&!(e.animating&&n.preventInteractionOnTransition)){var o=i;o.originalEvent&&(o=o.originalEvent);var d=h(o.target);if(!(n.touchEventsTarget==="wrapper"&&!d.closest(e.wrapperEl).length)&&(a.isTouchEvent=o.type==="touchstart",!(!a.isTouchEvent&&"which"in o&&o.which===3)&&!(!a.isTouchEvent&&"button"in o&&o.button>0)&&!(a.isTouched&&a.isMoved))){var f=!!n.noSwipingClass&&n.noSwipingClass!=="";f&&o.target&&o.target.shadowRoot&&i.path&&i.path[0]&&(d=h(i.path[0]));var u=n.noSwipingSelector?n.noSwipingSelector:"."+n.noSwipingClass,p=!!(o.target&&o.target.shadowRoot);if(n.noSwiping&&(p?Ki(u,o.target):d.closest(u)[0])){e.allowClick=!0;return}if(!(n.swipeHandler&&!d.closest(n.swipeHandler)[0])){s.currentX=o.type==="touchstart"?o.targetTouches[0].pageX:o.pageX,s.currentY=o.type==="touchstart"?o.targetTouches[0].pageY:o.pageY;var c=s.currentX,v=s.currentY,g=n.edgeSwipeDetection||n.iOSEdgeSwipeDetection,w=n.edgeSwipeThreshold||n.iOSEdgeSwipeThreshold;if(g&&(c<=w||c>=t.innerWidth-w))if(g==="prevent")i.preventDefault();else return;if(O(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=c,s.startY=v,a.touchStartTime=H(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,n.threshold>0&&(a.allowThresholdMove=!1),o.type!=="touchstart"){var b=!0;d.is(a.focusableElements)&&(b=!1),r.activeElement&&h(r.activeElement).is(a.focusableElements)&&r.activeElement!==d[0]&&r.activeElement.blur();var C=b&&e.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||C)&&!d[0].isContentEditable&&o.preventDefault()}e.emit("touchStart",o)}}}}function tr(i){var e=G(),r=this,t=r.touchEventsData,a=r.params,n=r.touches,s=r.rtlTranslate,l=r.enabled;if(!!l){var o=i;if(o.originalEvent&&(o=o.originalEvent),!t.isTouched){t.startMoving&&t.isScrolling&&r.emit("touchMoveOpposite",o);return}if(!(t.isTouchEvent&&o.type!=="touchmove")){var d=o.type==="touchmove"&&o.targetTouches&&(o.targetTouches[0]||o.changedTouches[0]),f=o.type==="touchmove"?d.pageX:o.pageX,u=o.type==="touchmove"?d.pageY:o.pageY;if(o.preventedByNestedSwiper){n.startX=f,n.startY=u;return}if(!r.allowTouchMove){r.allowClick=!1,t.isTouched&&(O(n,{startX:f,startY:u,currentX:f,currentY:u}),t.touchStartTime=H());return}if(t.isTouchEvent&&a.touchReleaseOnEdges&&!a.loop){if(r.isVertical()){if(u<n.startY&&r.translate<=r.maxTranslate()||u>n.startY&&r.translate>=r.minTranslate()){t.isTouched=!1,t.isMoved=!1;return}}else if(f<n.startX&&r.translate<=r.maxTranslate()||f>n.startX&&r.translate>=r.minTranslate())return}if(t.isTouchEvent&&e.activeElement&&o.target===e.activeElement&&h(o.target).is(t.focusableElements)){t.isMoved=!0,r.allowClick=!1;return}if(t.allowTouchCallbacks&&r.emit("touchMove",o),!(o.targetTouches&&o.targetTouches.length>1)){n.currentX=f,n.currentY=u;var p=n.currentX-n.startX,c=n.currentY-n.startY;if(!(r.params.threshold&&Math.sqrt(Math.pow(p,2)+Math.pow(c,2))<r.params.threshold)){if(typeof t.isScrolling=="undefined"){var v;r.isHorizontal()&&n.currentY===n.startY||r.isVertical()&&n.currentX===n.startX?t.isScrolling=!1:p*p+c*c>=25&&(v=Math.atan2(Math.abs(c),Math.abs(p))*180/Math.PI,t.isScrolling=r.isHorizontal()?v>a.touchAngle:90-v>a.touchAngle)}if(t.isScrolling&&r.emit("touchMoveOpposite",o),typeof t.startMoving=="undefined"&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(t.startMoving=!0),t.isScrolling){t.isTouched=!1;return}if(!!t.startMoving){r.allowClick=!1,!a.cssMode&&o.cancelable&&o.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&o.stopPropagation(),t.isMoved||(a.loop&&r.loopFix(),t.startTranslate=r.getTranslate(),r.setTransition(0),r.animating&&r.$wrapperEl.trigger("webkitTransitionEnd transitionend"),t.allowMomentumBounce=!1,a.grabCursor&&(r.allowSlideNext===!0||r.allowSlidePrev===!0)&&r.setGrabCursor(!0),r.emit("sliderFirstMove",o)),r.emit("sliderMove",o),t.isMoved=!0;var g=r.isHorizontal()?p:c;n.diff=g,g*=a.touchRatio,s&&(g=-g),r.swipeDirection=g>0?"prev":"next",t.currentTranslate=g+t.startTranslate;var w=!0,b=a.resistanceRatio;if(a.touchReleaseOnEdges&&(b=0),g>0&&t.currentTranslate>r.minTranslate()?(w=!1,a.resistance&&(t.currentTranslate=r.minTranslate()-1+Math.pow(-r.minTranslate()+t.startTranslate+g,b))):g<0&&t.currentTranslate<r.maxTranslate()&&(w=!1,a.resistance&&(t.currentTranslate=r.maxTranslate()+1-Math.pow(r.maxTranslate()-t.startTranslate-g,b))),w&&(o.preventedByNestedSwiper=!0),!r.allowSlideNext&&r.swipeDirection==="next"&&t.currentTranslate<t.startTranslate&&(t.currentTranslate=t.startTranslate),!r.allowSlidePrev&&r.swipeDirection==="prev"&&t.currentTranslate>t.startTranslate&&(t.currentTranslate=t.startTranslate),!r.allowSlidePrev&&!r.allowSlideNext&&(t.currentTranslate=t.startTranslate),a.threshold>0)if(Math.abs(g)>a.threshold||t.allowThresholdMove){if(!t.allowThresholdMove){t.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,t.currentTranslate=t.startTranslate,n.diff=r.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{t.currentTranslate=t.startTranslate;return}!a.followFinger||a.cssMode||((a.freeMode||a.watchSlidesProgress||a.watchSlidesVisibility)&&(r.updateActiveIndex(),r.updateSlidesClasses()),a.freeMode&&(t.velocities.length===0&&t.velocities.push({position:n[r.isHorizontal()?"startX":"startY"],time:t.touchStartTime}),t.velocities.push({position:n[r.isHorizontal()?"currentX":"currentY"],time:H()})),r.updateProgress(t.currentTranslate),r.setTranslate(t.currentTranslate))}}}}}}function ir(i){var e=this,r=e.touchEventsData,t=e.params,a=e.touches,n=e.rtlTranslate,s=e.$wrapperEl,l=e.slidesGrid,o=e.snapGrid,d=e.enabled;if(!!d){var f=i;if(f.originalEvent&&(f=f.originalEvent),r.allowTouchCallbacks&&e.emit("touchEnd",f),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&t.grabCursor&&e.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}t.grabCursor&&r.isMoved&&r.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);var u=H(),p=u-r.touchStartTime;if(e.allowClick&&(e.updateClickedSlide(f),e.emit("tap click",f),p<300&&u-r.lastClickTime<300&&e.emit("doubleTap doubleClick",f)),r.lastClickTime=H(),te(function(){e.destroyed||(e.allowClick=!0)}),!r.isTouched||!r.isMoved||!e.swipeDirection||a.diff===0||r.currentTranslate===r.startTranslate){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;var c;if(t.followFinger?c=n?e.translate:-e.translate:c=-r.currentTranslate,!t.cssMode){if(t.freeMode){if(c<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(c>-e.maxTranslate()){e.slides.length<o.length?e.slideTo(o.length-1):e.slideTo(e.slides.length-1);return}if(t.freeModeMomentum){if(r.velocities.length>1){var v=r.velocities.pop(),g=r.velocities.pop(),w=v.position-g.position,b=v.time-g.time;e.velocity=w/b,e.velocity/=2,Math.abs(e.velocity)<t.freeModeMinimumVelocity&&(e.velocity=0),(b>150||H()-v.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=t.freeModeMomentumVelocityRatio,r.velocities.length=0;var C=1e3*t.freeModeMomentumRatio,y=e.velocity*C,m=e.translate+y;n&&(m=-m);var S=!1,z,M=Math.abs(e.velocity)*20*t.freeModeMomentumBounceRatio,T;if(m<e.maxTranslate())t.freeModeMomentumBounce?(m+e.maxTranslate()<-M&&(m=e.maxTranslate()-M),z=e.maxTranslate(),S=!0,r.allowMomentumBounce=!0):m=e.maxTranslate(),t.loop&&t.centeredSlides&&(T=!0);else if(m>e.minTranslate())t.freeModeMomentumBounce?(m-e.minTranslate()>M&&(m=e.minTranslate()+M),z=e.minTranslate(),S=!0,r.allowMomentumBounce=!0):m=e.minTranslate(),t.loop&&t.centeredSlides&&(T=!0);else if(t.freeModeSticky){for(var P,E=0;E<o.length;E+=1)if(o[E]>-m){P=E;break}Math.abs(o[P]-m)<Math.abs(o[P-1]-m)||e.swipeDirection==="next"?m=o[P]:m=o[P-1],m=-m}if(T&&e.once("transitionEnd",function(){e.loopFix()}),e.velocity!==0){if(n?C=Math.abs((-m-e.translate)/e.velocity):C=Math.abs((m-e.translate)/e.velocity),t.freeModeSticky){var L=Math.abs((n?-m:m)-e.translate),k=e.slidesSizesGrid[e.activeIndex];L<k?C=t.speed:L<2*k?C=t.speed*1.5:C=t.speed*2.5}}else if(t.freeModeSticky){e.slideToClosest();return}t.freeModeMomentumBounce&&S?(e.updateProgress(z),e.setTransition(C),e.setTranslate(m),e.transitionStart(!0,e.swipeDirection),e.animating=!0,s.transitionEnd(function(){!e||e.destroyed||!r.allowMomentumBounce||(e.emit("momentumBounce"),e.setTransition(t.speed),setTimeout(function(){e.setTranslate(z),s.transitionEnd(function(){!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(e.updateProgress(m),e.setTransition(C),e.setTranslate(m),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,s.transitionEnd(function(){!e||e.destroyed||e.transitionEnd()}))):(e.emit("_freeModeNoMomentumRelease"),e.updateProgress(m)),e.updateActiveIndex(),e.updateSlidesClasses()}else if(t.freeModeSticky){e.slideToClosest();return}else t.freeMode&&e.emit("_freeModeNoMomentumRelease");(!t.freeModeMomentum||p>=t.longSwipesMs)&&(e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses());return}for(var x=0,D=e.slidesSizesGrid[0],I=0;I<l.length;I+=I<t.slidesPerGroupSkip?1:t.slidesPerGroup){var $=I<t.slidesPerGroupSkip-1?1:t.slidesPerGroup;typeof l[I+$]!="undefined"?c>=l[I]&&c<l[I+$]&&(x=I,D=l[I+$]-l[I]):c>=l[I]&&(x=I,D=l[l.length-1]-l[l.length-2])}var F=(c-l[x])/D,R=x<t.slidesPerGroupSkip-1?1:t.slidesPerGroup;if(p>t.longSwipesMs){if(!t.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(F>=t.longSwipesRatio?e.slideTo(x+R):e.slideTo(x)),e.swipeDirection==="prev"&&(F>1-t.longSwipesRatio?e.slideTo(x+R):e.slideTo(x))}else{if(!t.shortSwipes){e.slideTo(e.activeIndex);return}var U=e.navigation&&(f.target===e.navigation.nextEl||f.target===e.navigation.prevEl);U?f.target===e.navigation.nextEl?e.slideTo(x+R):e.slideTo(x):(e.swipeDirection==="next"&&e.slideTo(x+R),e.swipeDirection==="prev"&&e.slideTo(x))}}}}function Te(){var i=this,e=i.params,r=i.el;if(!(r&&r.offsetWidth===0)){e.breakpoints&&i.setBreakpoint();var t=i.allowSlideNext,a=i.allowSlidePrev,n=i.snapGrid;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses(),(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides?i.slideTo(i.slides.length-1,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.run(),i.allowSlidePrev=a,i.allowSlideNext=t,i.params.watchOverflow&&n!==i.snapGrid&&i.checkOverflow()}}function rr(i){var e=this;!e.enabled||e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation()))}function ar(){var i=this,e=i.wrapperEl,r=i.rtlTranslate,t=i.enabled;if(!!t){i.previousTranslate=i.translate,i.isHorizontal()?r?i.translate=e.scrollWidth-e.offsetWidth-e.scrollLeft:i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===-0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();var a,n=i.maxTranslate()-i.minTranslate();n===0?a=0:a=(i.translate-i.minTranslate())/n,a!==i.progress&&i.updateProgress(r?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}}var Ne=!1;function nr(){}function sr(){var i=this,e=G(),r=i.params,t=i.touchEvents,a=i.el,n=i.wrapperEl,s=i.device,l=i.support;i.onTouchStart=er.bind(i),i.onTouchMove=tr.bind(i),i.onTouchEnd=ir.bind(i),r.cssMode&&(i.onScroll=ar.bind(i)),i.onClick=rr.bind(i);var o=!!r.nested;if(!l.touch&&l.pointerEvents)a.addEventListener(t.start,i.onTouchStart,!1),e.addEventListener(t.move,i.onTouchMove,o),e.addEventListener(t.end,i.onTouchEnd,!1);else{if(l.touch){var d=t.start==="touchstart"&&l.passiveListener&&r.passiveListeners?{passive:!0,capture:!1}:!1;a.addEventListener(t.start,i.onTouchStart,d),a.addEventListener(t.move,i.onTouchMove,l.passiveListener?{passive:!1,capture:o}:o),a.addEventListener(t.end,i.onTouchEnd,d),t.cancel&&a.addEventListener(t.cancel,i.onTouchEnd,d),Ne||(e.addEventListener("touchstart",nr),Ne=!0)}(r.simulateTouch&&!s.ios&&!s.android||r.simulateTouch&&!l.touch&&s.ios)&&(a.addEventListener("mousedown",i.onTouchStart,!1),e.addEventListener("mousemove",i.onTouchMove,o),e.addEventListener("mouseup",i.onTouchEnd,!1))}(r.preventClicks||r.preventClicksPropagation)&&a.addEventListener("click",i.onClick,!0),r.cssMode&&n.addEventListener("scroll",i.onScroll),r.updateOnWindowResize?i.on(s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",Te,!0):i.on("observerUpdate",Te,!0)}function or(){var i=this,e=G(),r=i.params,t=i.touchEvents,a=i.el,n=i.wrapperEl,s=i.device,l=i.support,o=!!r.nested;if(!l.touch&&l.pointerEvents)a.removeEventListener(t.start,i.onTouchStart,!1),e.removeEventListener(t.move,i.onTouchMove,o),e.removeEventListener(t.end,i.onTouchEnd,!1);else{if(l.touch){var d=t.start==="onTouchStart"&&l.passiveListener&&r.passiveListeners?{passive:!0,capture:!1}:!1;a.removeEventListener(t.start,i.onTouchStart,d),a.removeEventListener(t.move,i.onTouchMove,o),a.removeEventListener(t.end,i.onTouchEnd,d),t.cancel&&a.removeEventListener(t.cancel,i.onTouchEnd,d)}(r.simulateTouch&&!s.ios&&!s.android||r.simulateTouch&&!l.touch&&s.ios)&&(a.removeEventListener("mousedown",i.onTouchStart,!1),e.removeEventListener("mousemove",i.onTouchMove,o),e.removeEventListener("mouseup",i.onTouchEnd,!1))}(r.preventClicks||r.preventClicksPropagation)&&a.removeEventListener("click",i.onClick,!0),r.cssMode&&n.removeEventListener("scroll",i.onScroll),i.off(s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",Te)}var lr={attachEvents:sr,detachEvents:or};function dr(){var i=this,e=i.activeIndex,r=i.initialized,t=i.loopedSlides,a=t===void 0?0:t,n=i.params,s=i.$el,l=n.breakpoints;if(!(!l||l&&Object.keys(l).length===0)){var o=i.getBreakpoint(l,i.params.breakpointsBase,i.el);if(!(!o||i.currentBreakpoint===o)){var d=o in l?l[o]:void 0;d&&["slidesPerView","spaceBetween","slidesPerGroup","slidesPerGroupSkip","slidesPerColumn"].forEach(function(b){var C=d[b];typeof C!="undefined"&&(b==="slidesPerView"&&(C==="AUTO"||C==="auto")?d[b]="auto":b==="slidesPerView"?d[b]=parseFloat(C):d[b]=parseInt(C,10))});var f=d||i.originalParams,u=n.slidesPerColumn>1,p=f.slidesPerColumn>1,c=n.enabled;u&&!p?(s.removeClass(n.containerModifierClass+"multirow "+n.containerModifierClass+"multirow-column"),i.emitContainerClasses()):!u&&p&&(s.addClass(n.containerModifierClass+"multirow"),(f.slidesPerColumnFill&&f.slidesPerColumnFill==="column"||!f.slidesPerColumnFill&&n.slidesPerColumnFill==="column")&&s.addClass(n.containerModifierClass+"multirow-column"),i.emitContainerClasses());var v=f.direction&&f.direction!==n.direction,g=n.loop&&(f.slidesPerView!==n.slidesPerView||v);v&&r&&i.changeDirection(),O(i.params,f);var w=i.params.enabled;O(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),c&&!w?i.disable():!c&&w&&i.enable(),i.currentBreakpoint=o,i.emit("_beforeBreakpoint",f),g&&r&&(i.loopDestroy(),i.loopCreate(),i.updateSlides(),i.slideTo(e-a+i.loopedSlides,0,!1)),i.emit("breakpoint",f)}}}function fr(i,e,r){if(e===void 0&&(e="window"),!(!i||e==="container"&&!r)){var t=!1,a=A(),n=e==="window"?a.innerHeight:r.clientHeight,s=Object.keys(i).map(function(u){if(typeof u=="string"&&u.indexOf("@")===0){var p=parseFloat(u.substr(1)),c=n*p;return{value:c,point:u}}return{value:u,point:u}});s.sort(function(u,p){return parseInt(u.value,10)-parseInt(p.value,10)});for(var l=0;l<s.length;l+=1){var o=s[l],d=o.point,f=o.value;e==="window"?a.matchMedia("(min-width: "+f+"px)").matches&&(t=d):f<=r.clientWidth&&(t=d)}return t||"max"}}var ur={setBreakpoint:dr,getBreakpoint:fr};function pr(i,e){var r=[];return i.forEach(function(t){typeof t=="object"?Object.keys(t).forEach(function(a){t[a]&&r.push(e+a)}):typeof t=="string"&&r.push(e+t)}),r}function cr(){var i=this,e=i.classNames,r=i.params,t=i.rtl,a=i.$el,n=i.device,s=i.support,l=pr(["initialized",r.direction,{"pointer-events":s.pointerEvents&&!s.touch},{"free-mode":r.freeMode},{autoheight:r.autoHeight},{rtl:t},{multirow:r.slidesPerColumn>1},{"multirow-column":r.slidesPerColumn>1&&r.slidesPerColumnFill==="column"},{android:n.android},{ios:n.ios},{"css-mode":r.cssMode}],r.containerModifierClass);e.push.apply(e,l),a.addClass([].concat(e).join(" ")),i.emitContainerClasses()}function vr(){var i=this,e=i.$el,r=i.classNames;e.removeClass(r.join(" ")),i.emitContainerClasses()}var hr={addClasses:cr,removeClasses:vr};function mr(i,e,r,t,a,n){var s=A(),l;function o(){n&&n()}var d=h(i).parent("picture")[0];!d&&(!i.complete||!a)&&e?(l=new s.Image,l.onload=o,l.onerror=o,t&&(l.sizes=t),r&&(l.srcset=r),e&&(l.src=e)):o()}function gr(){var i=this;i.imagesToLoad=i.$el.find("img");function e(){typeof i=="undefined"||i===null||!i||i.destroyed||(i.imagesLoaded!==void 0&&(i.imagesLoaded+=1),i.imagesLoaded===i.imagesToLoad.length&&(i.params.updateOnImagesReady&&i.update(),i.emit("imagesReady")))}for(var r=0;r<i.imagesToLoad.length;r+=1){var t=i.imagesToLoad[r];i.loadImage(t,t.currentSrc||t.getAttribute("src"),t.srcset||t.getAttribute("srcset"),t.sizes||t.getAttribute("sizes"),!0,e)}}var wr={loadImage:mr,preloadImages:gr};function br(){var i=this,e=i.params,r=i.isLocked,t=i.slides.length>0&&e.slidesOffsetBefore+e.spaceBetween*(i.slides.length-1)+i.slides[0].offsetWidth*i.slides.length;e.slidesOffsetBefore&&e.slidesOffsetAfter&&t?i.isLocked=t<=i.size:i.isLocked=i.snapGrid.length===1,i.allowSlideNext=!i.isLocked,i.allowSlidePrev=!i.isLocked,r!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock"),r&&r!==i.isLocked&&(i.isEnd=!1,i.navigation&&i.navigation.update())}var yr={checkOverflow:br},He={init:!0,direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!1,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,slidesPerGroupSkip:0,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!1,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-container-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function Fe(i,e){for(var r=0;r<e.length;r++){var t=e[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(i,t.key,t)}}function Tr(i,e,r){return e&&Fe(i.prototype,e),r&&Fe(i,r),i}var he={modular:fi,eventsEmitter:ui,update:Ti,translate:Pi,transition:Ii,slide:Hi,loop:Wi,grabCursor:qi,manipulation:Zi,events:lr,breakpoints:ur,checkOverflow:yr,classes:hr,images:wr},me={},Le=function(){function i(){for(var r,t,a=arguments.length,n=new Array(a),s=0;s<a;s++)n[s]=arguments[s];if(n.length===1&&n[0].constructor&&Object.prototype.toString.call(n[0]).slice(8,-1)==="Object"?t=n[0]:(r=n[0],t=n[1]),t||(t={}),t=O({},t),r&&!t.el&&(t.el=r),t.el&&h(t.el).length>1){var l=[];return h(t.el).each(function(f){var u=O({},t,{el:f});l.push(new i(u))}),l}var o=this;o.__swiper__=!0,o.support=_e(),o.device=ri({userAgent:t.userAgent}),o.browser=ni(),o.eventsListeners={},o.eventsAnyListeners=[],typeof o.modules=="undefined"&&(o.modules={}),Object.keys(o.modules).forEach(function(f){var u=o.modules[f];if(u.params){var p=Object.keys(u.params)[0],c=u.params[p];if(typeof c!="object"||c===null||(["navigation","pagination","scrollbar"].indexOf(p)>=0&&t[p]===!0&&(t[p]={auto:!0}),!(p in t&&"enabled"in c)))return;t[p]===!0&&(t[p]={enabled:!0}),typeof t[p]=="object"&&!("enabled"in t[p])&&(t[p].enabled=!0),t[p]||(t[p]={enabled:!1})}});var d=O({},He);return o.useParams(d),o.params=O({},d,me,t),o.originalParams=O({},o.params),o.passedParams=O({},t),o.params&&o.params.on&&Object.keys(o.params.on).forEach(function(f){o.on(f,o.params.on[f])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),o.$=h,O(o,{enabled:o.params.enabled,el:r,classNames:[],slides:h(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return o.params.direction==="horizontal"},isVertical:function(){return o.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEvents:function(){var u=["touchstart","touchmove","touchend","touchcancel"],p=["mousedown","mousemove","mouseup"];return o.support.pointerEvents&&(p=["pointerdown","pointermove","pointerup"]),o.touchEventsTouch={start:u[0],move:u[1],end:u[2],cancel:u[3]},o.touchEventsDesktop={start:p[0],move:p[1],end:p[2]},o.support.touch||!o.params.simulateTouch?o.touchEventsTouch:o.touchEventsDesktop}(),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:H(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.useModules(),o.emit("_swiper"),o.params.init&&o.init(),o}var e=i.prototype;return e.enable=function(){var t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))},e.disable=function(){var t=this;!t.enabled||(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))},e.setProgress=function(t,a){var n=this;t=Math.min(Math.max(t,0),1);var s=n.minTranslate(),l=n.maxTranslate(),o=(l-s)*t+s;n.translateTo(o,typeof a=="undefined"?0:a),n.updateActiveIndex(),n.updateSlidesClasses()},e.emitContainerClasses=function(){var t=this;if(!(!t.params._emitClasses||!t.el)){var a=t.el.className.split(" ").filter(function(n){return n.indexOf("swiper-container")===0||n.indexOf(t.params.containerModifierClass)===0});t.emit("_containerClasses",a.join(" "))}},e.getSlideClasses=function(t){var a=this;return t.className.split(" ").filter(function(n){return n.indexOf("swiper-slide")===0||n.indexOf(a.params.slideClass)===0}).join(" ")},e.emitSlidesClasses=function(){var t=this;if(!(!t.params._emitClasses||!t.el)){var a=[];t.slides.each(function(n){var s=t.getSlideClasses(n);a.push({slideEl:n,classNames:s}),t.emit("_slideClass",n,s)}),t.emit("_slideClasses",a)}},e.slidesPerViewDynamic=function(){var t=this,a=t.params,n=t.slides,s=t.slidesGrid,l=t.size,o=t.activeIndex,d=1;if(a.centeredSlides){for(var f=n[o].swiperSlideSize,u,p=o+1;p<n.length;p+=1)n[p]&&!u&&(f+=n[p].swiperSlideSize,d+=1,f>l&&(u=!0));for(var c=o-1;c>=0;c-=1)n[c]&&!u&&(f+=n[c].swiperSlideSize,d+=1,f>l&&(u=!0))}else for(var v=o+1;v<n.length;v+=1)s[v]-s[o]<l&&(d+=1);return d},e.update=function(){var t=this;if(!t||t.destroyed)return;var a=t.snapGrid,n=t.params;n.breakpoints&&t.setBreakpoint(),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function s(){var o=t.rtlTranslate?t.translate*-1:t.translate,d=Math.min(Math.max(o,t.maxTranslate()),t.minTranslate());t.setTranslate(d),t.updateActiveIndex(),t.updateSlidesClasses()}var l;t.params.freeMode?(s(),t.params.autoHeight&&t.updateAutoHeight()):((t.params.slidesPerView==="auto"||t.params.slidesPerView>1)&&t.isEnd&&!t.params.centeredSlides?l=t.slideTo(t.slides.length-1,0,!1,!0):l=t.slideTo(t.activeIndex,0,!1,!0),l||s()),n.watchOverflow&&a!==t.snapGrid&&t.checkOverflow(),t.emit("update")},e.changeDirection=function(t,a){a===void 0&&(a=!0);var n=this,s=n.params.direction;return t||(t=s==="horizontal"?"vertical":"horizontal"),t===s||t!=="horizontal"&&t!=="vertical"||(n.$el.removeClass(""+n.params.containerModifierClass+s).addClass(""+n.params.containerModifierClass+t),n.emitContainerClasses(),n.params.direction=t,n.slides.each(function(l){t==="vertical"?l.style.width="":l.style.height=""}),n.emit("changeDirection"),a&&n.update()),n},e.mount=function(t){var a=this;if(a.mounted)return!0;var n=h(t||a.params.el);if(t=n[0],!t)return!1;t.swiper=a;var s=function(){return"."+(a.params.wrapperClass||"").trim().split(" ").join(".")},l=function(){if(t&&t.shadowRoot&&t.shadowRoot.querySelector){var p=h(t.shadowRoot.querySelector(s()));return p.children=function(c){return n.children(c)},p}return n.children(s())},o=l();if(o.length===0&&a.params.createElements){var d=G(),f=d.createElement("div");o=h(f),f.className=a.params.wrapperClass,n.append(f),n.children("."+a.params.slideClass).each(function(u){o.append(u)})}return O(a,{$el:n,el:t,$wrapperEl:o,wrapperEl:o[0],mounted:!0,rtl:t.dir.toLowerCase()==="rtl"||n.css("direction")==="rtl",rtlTranslate:a.params.direction==="horizontal"&&(t.dir.toLowerCase()==="rtl"||n.css("direction")==="rtl"),wrongRTL:o.css("display")==="-webkit-box"}),!0},e.init=function(t){var a=this;if(a.initialized)return a;var n=a.mount(t);return n===!1||(a.emit("beforeInit"),a.params.breakpoints&&a.setBreakpoint(),a.addClasses(),a.params.loop&&a.loopCreate(),a.updateSize(),a.updateSlides(),a.params.watchOverflow&&a.checkOverflow(),a.params.grabCursor&&a.enabled&&a.setGrabCursor(),a.params.preloadImages&&a.preloadImages(),a.params.loop?a.slideTo(a.params.initialSlide+a.loopedSlides,0,a.params.runCallbacksOnInit,!1,!0):a.slideTo(a.params.initialSlide,0,a.params.runCallbacksOnInit,!1,!0),a.attachEvents(),a.initialized=!0,a.emit("init"),a.emit("afterInit")),a},e.destroy=function(t,a){t===void 0&&(t=!0),a===void 0&&(a=!0);var n=this,s=n.params,l=n.$el,o=n.$wrapperEl,d=n.slides;return typeof n.params=="undefined"||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),s.loop&&n.loopDestroy(),a&&(n.removeClasses(),l.removeAttr("style"),o.removeAttr("style"),d&&d.length&&d.removeClass([s.slideVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(function(f){n.off(f)}),t!==!1&&(n.$el[0].swiper=null,Qt(n)),n.destroyed=!0),null},i.extendDefaults=function(t){O(me,t)},i.installModule=function(t){i.prototype.modules||(i.prototype.modules={});var a=t.name||Object.keys(i.prototype.modules).length+"_"+H();i.prototype.modules[a]=t},i.use=function(t){return Array.isArray(t)?(t.forEach(function(a){return i.installModule(a)}),i):(i.installModule(t),i)},Tr(i,null,[{key:"extendedDefaults",get:function(){return me}},{key:"defaults",get:function(){return He}}]),i}();Object.keys(he).forEach(function(i){Object.keys(he[i]).forEach(function(e){Le.prototype[e]=he[i][e]})});Le.use([oi,di]);var je=Le;function Ce(){return Ce=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},Ce.apply(this,arguments)}var Cr={toggleEl:function(e,r){e[r?"addClass":"removeClass"](this.params.navigation.disabledClass),e[0]&&e[0].tagName==="BUTTON"&&(e[0].disabled=r)},update:function(){var e=this,r=e.params.navigation,t=e.navigation.toggleEl;if(!e.params.loop){var a=e.navigation,n=a.$nextEl,s=a.$prevEl;s&&s.length>0&&(e.isBeginning?t(s,!0):t(s,!1),e.params.watchOverflow&&e.enabled&&s[e.isLocked?"addClass":"removeClass"](r.lockClass)),n&&n.length>0&&(e.isEnd?t(n,!0):t(n,!1),e.params.watchOverflow&&e.enabled&&n[e.isLocked?"addClass":"removeClass"](r.lockClass))}},onPrevClick:function(e){var r=this;e.preventDefault(),!(r.isBeginning&&!r.params.loop)&&r.slidePrev()},onNextClick:function(e){var r=this;e.preventDefault(),!(r.isEnd&&!r.params.loop)&&r.slideNext()},init:function(){var e=this,r=e.params.navigation;if(e.params.navigation=We(e.$el,e.params.navigation,e.params.createElements,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!!(r.nextEl||r.prevEl)){var t,a;r.nextEl&&(t=h(r.nextEl),e.params.uniqueNavElements&&typeof r.nextEl=="string"&&t.length>1&&e.$el.find(r.nextEl).length===1&&(t=e.$el.find(r.nextEl))),r.prevEl&&(a=h(r.prevEl),e.params.uniqueNavElements&&typeof r.prevEl=="string"&&a.length>1&&e.$el.find(r.prevEl).length===1&&(a=e.$el.find(r.prevEl))),t&&t.length>0&&t.on("click",e.navigation.onNextClick),a&&a.length>0&&a.on("click",e.navigation.onPrevClick),O(e.navigation,{$nextEl:t,nextEl:t&&t[0],$prevEl:a,prevEl:a&&a[0]}),e.enabled||(t&&t.addClass(r.lockClass),a&&a.addClass(r.lockClass))}},destroy:function(){var e=this,r=e.navigation,t=r.$nextEl,a=r.$prevEl;t&&t.length&&(t.off("click",e.navigation.onNextClick),t.removeClass(e.params.navigation.disabledClass)),a&&a.length&&(a.off("click",e.navigation.onPrevClick),a.removeClass(e.params.navigation.disabledClass))}},Sr={name:"navigation",params:{navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}},create:function(){var e=this;q(e,{navigation:Ce({},Cr)})},on:{init:function(e){e.navigation.init(),e.navigation.update()},toEdge:function(e){e.navigation.update()},fromEdge:function(e){e.navigation.update()},destroy:function(e){e.navigation.destroy()},"enable disable":function(e){var r=e.navigation,t=r.$nextEl,a=r.$prevEl;t&&t[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass),a&&a[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass)},click:function(e,r){var t=e.navigation,a=t.$nextEl,n=t.$prevEl,s=r.target;if(e.params.navigation.hideOnClick&&!h(s).is(n)&&!h(s).is(a)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===s||e.pagination.el.contains(s)))return;var l;a?l=a.hasClass(e.params.navigation.hiddenClass):n&&(l=n.hasClass(e.params.navigation.hiddenClass)),l===!0?e.emit("navigationShow"):e.emit("navigationHide"),a&&a.toggleClass(e.params.navigation.hiddenClass),n&&n.toggleClass(e.params.navigation.hiddenClass)}}}};function Se(){return Se=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},Se.apply(this,arguments)}var Er={update:function(){var e=this,r=e.rtl,t=e.params.pagination;if(!(!t.el||!e.pagination.el||!e.pagination.$el||e.pagination.$el.length===0)){var a=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,n=e.pagination.$el,s,l=e.params.loop?Math.ceil((a-e.loopedSlides*2)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(s=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup),s>a-1-e.loopedSlides*2&&(s-=a-e.loopedSlides*2),s>l-1&&(s-=l),s<0&&e.params.paginationType!=="bullets"&&(s=l+s)):typeof e.snapIndex!="undefined"?s=e.snapIndex:s=e.activeIndex||0,t.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){var o=e.pagination.bullets,d,f,u;if(t.dynamicBullets&&(e.pagination.bulletSize=o.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),n.css(e.isHorizontal()?"width":"height",e.pagination.bulletSize*(t.dynamicMainBullets+4)+"px"),t.dynamicMainBullets>1&&e.previousIndex!==void 0&&(e.pagination.dynamicBulletIndex+=s-e.previousIndex,e.pagination.dynamicBulletIndex>t.dynamicMainBullets-1?e.pagination.dynamicBulletIndex=t.dynamicMainBullets-1:e.pagination.dynamicBulletIndex<0&&(e.pagination.dynamicBulletIndex=0)),d=s-e.pagination.dynamicBulletIndex,f=d+(Math.min(o.length,t.dynamicMainBullets)-1),u=(f+d)/2),o.removeClass(t.bulletActiveClass+" "+t.bulletActiveClass+"-next "+t.bulletActiveClass+"-next-next "+t.bulletActiveClass+"-prev "+t.bulletActiveClass+"-prev-prev "+t.bulletActiveClass+"-main"),n.length>1)o.each(function(P){var E=h(P),L=E.index();L===s&&E.addClass(t.bulletActiveClass),t.dynamicBullets&&(L>=d&&L<=f&&E.addClass(t.bulletActiveClass+"-main"),L===d&&E.prev().addClass(t.bulletActiveClass+"-prev").prev().addClass(t.bulletActiveClass+"-prev-prev"),L===f&&E.next().addClass(t.bulletActiveClass+"-next").next().addClass(t.bulletActiveClass+"-next-next"))});else{var p=o.eq(s),c=p.index();if(p.addClass(t.bulletActiveClass),t.dynamicBullets){for(var v=o.eq(d),g=o.eq(f),w=d;w<=f;w+=1)o.eq(w).addClass(t.bulletActiveClass+"-main");if(e.params.loop)if(c>=o.length-t.dynamicMainBullets){for(var b=t.dynamicMainBullets;b>=0;b-=1)o.eq(o.length-b).addClass(t.bulletActiveClass+"-main");o.eq(o.length-t.dynamicMainBullets-1).addClass(t.bulletActiveClass+"-prev")}else v.prev().addClass(t.bulletActiveClass+"-prev").prev().addClass(t.bulletActiveClass+"-prev-prev"),g.next().addClass(t.bulletActiveClass+"-next").next().addClass(t.bulletActiveClass+"-next-next");else v.prev().addClass(t.bulletActiveClass+"-prev").prev().addClass(t.bulletActiveClass+"-prev-prev"),g.next().addClass(t.bulletActiveClass+"-next").next().addClass(t.bulletActiveClass+"-next-next")}}if(t.dynamicBullets){var C=Math.min(o.length,t.dynamicMainBullets+4),y=(e.pagination.bulletSize*C-e.pagination.bulletSize)/2-u*e.pagination.bulletSize,m=r?"right":"left";o.css(e.isHorizontal()?m:"top",y+"px")}}if(t.type==="fraction"&&(n.find(j(t.currentClass)).text(t.formatFractionCurrent(s+1)),n.find(j(t.totalClass)).text(t.formatFractionTotal(l))),t.type==="progressbar"){var S;t.progressbarOpposite?S=e.isHorizontal()?"vertical":"horizontal":S=e.isHorizontal()?"horizontal":"vertical";var z=(s+1)/l,M=1,T=1;S==="horizontal"?M=z:T=z,n.find(j(t.progressbarFillClass)).transform("translate3d(0,0,0) scaleX("+M+") scaleY("+T+")").transition(e.params.speed)}t.type==="custom"&&t.renderCustom?(n.html(t.renderCustom(e,s+1,l)),e.emit("paginationRender",n[0])):e.emit("paginationUpdate",n[0]),e.params.watchOverflow&&e.enabled&&n[e.isLocked?"addClass":"removeClass"](t.lockClass)}},render:function(){var e=this,r=e.params.pagination;if(!(!r.el||!e.pagination.el||!e.pagination.$el||e.pagination.$el.length===0)){var t=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,a=e.pagination.$el,n="";if(r.type==="bullets"){var s=e.params.loop?Math.ceil((t-e.loopedSlides*2)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&!e.params.loop&&s>t&&(s=t);for(var l=0;l<s;l+=1)r.renderBullet?n+=r.renderBullet.call(e,l,r.bulletClass):n+="<"+r.bulletElement+' class="'+r.bulletClass+'"></'+r.bulletElement+">";a.html(n),e.pagination.bullets=a.find(j(r.bulletClass))}r.type==="fraction"&&(r.renderFraction?n=r.renderFraction.call(e,r.currentClass,r.totalClass):n='<span class="'+r.currentClass+'"></span> / '+('<span class="'+r.totalClass+'"></span>'),a.html(n)),r.type==="progressbar"&&(r.renderProgressbar?n=r.renderProgressbar.call(e,r.progressbarFillClass):n='<span class="'+r.progressbarFillClass+'"></span>',a.html(n)),r.type!=="custom"&&e.emit("paginationRender",e.pagination.$el[0])}},init:function(){var e=this;e.params.pagination=We(e.$el,e.params.pagination,e.params.createElements,{el:"swiper-pagination"});var r=e.params.pagination;if(!!r.el){var t=h(r.el);t.length!==0&&(e.params.uniqueNavElements&&typeof r.el=="string"&&t.length>1&&(t=e.$el.find(r.el)),r.type==="bullets"&&r.clickable&&t.addClass(r.clickableClass),t.addClass(r.modifierClass+r.type),r.type==="bullets"&&r.dynamicBullets&&(t.addClass(""+r.modifierClass+r.type+"-dynamic"),e.pagination.dynamicBulletIndex=0,r.dynamicMainBullets<1&&(r.dynamicMainBullets=1)),r.type==="progressbar"&&r.progressbarOpposite&&t.addClass(r.progressbarOppositeClass),r.clickable&&t.on("click",j(r.bulletClass),function(n){n.preventDefault();var s=h(this).index()*e.params.slidesPerGroup;e.params.loop&&(s+=e.loopedSlides),e.slideTo(s)}),O(e.pagination,{$el:t,el:t[0]}),e.enabled||t.addClass(r.lockClass))}},destroy:function(){var e=this,r=e.params.pagination;if(!(!r.el||!e.pagination.el||!e.pagination.$el||e.pagination.$el.length===0)){var t=e.pagination.$el;t.removeClass(r.hiddenClass),t.removeClass(r.modifierClass+r.type),e.pagination.bullets&&e.pagination.bullets.removeClass(r.bulletActiveClass),r.clickable&&t.off("click",j(r.bulletClass))}}},xr={name:"pagination",params:{pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",modifierClass:"swiper-pagination-",currentClass:"swiper-pagination-current",totalClass:"swiper-pagination-total",hiddenClass:"swiper-pagination-hidden",progressbarFillClass:"swiper-pagination-progressbar-fill",progressbarOppositeClass:"swiper-pagination-progressbar-opposite",clickableClass:"swiper-pagination-clickable",lockClass:"swiper-pagination-lock"}},create:function(){var e=this;q(e,{pagination:Se({dynamicBulletIndex:0},Er)})},on:{init:function(e){e.pagination.init(),e.pagination.render(),e.pagination.update()},activeIndexChange:function(e){(e.params.loop||typeof e.snapIndex=="undefined")&&e.pagination.update()},snapIndexChange:function(e){e.params.loop||e.pagination.update()},slidesLengthChange:function(e){e.params.loop&&(e.pagination.render(),e.pagination.update())},snapGridLengthChange:function(e){e.params.loop||(e.pagination.render(),e.pagination.update())},destroy:function(e){e.pagination.destroy()},"enable disable":function(e){var r=e.pagination.$el;r&&r[e.enabled?"removeClass":"addClass"](e.params.pagination.lockClass)},click:function(e,r){var t=r.target;if(e.params.pagination.el&&e.params.pagination.hideOnClick&&e.pagination.$el.length>0&&!h(t).hasClass(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&t===e.navigation.nextEl||e.navigation.prevEl&&t===e.navigation.prevEl))return;var a=e.pagination.$el.hasClass(e.params.pagination.hiddenClass);a===!0?e.emit("paginationShow"):e.emit("paginationHide"),e.pagination.$el.toggleClass(e.params.pagination.hiddenClass)}}}};function Ee(){return Ee=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},Ee.apply(this,arguments)}var Mr={run:function(){var e=this,r=e.slides.eq(e.activeIndex),t=e.params.autoplay.delay;r.attr("data-swiper-autoplay")&&(t=r.attr("data-swiper-autoplay")||e.params.autoplay.delay),clearTimeout(e.autoplay.timeout),e.autoplay.timeout=te(function(){var a;e.params.autoplay.reverseDirection?e.params.loop?(e.loopFix(),a=e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.isBeginning?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(a=e.slideTo(e.slides.length-1,e.params.speed,!0,!0),e.emit("autoplay")):(a=e.slidePrev(e.params.speed,!0,!0),e.emit("autoplay")):e.params.loop?(e.loopFix(),a=e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")):e.isEnd?e.params.autoplay.stopOnLastSlide?e.autoplay.stop():(a=e.slideTo(0,e.params.speed,!0,!0),e.emit("autoplay")):(a=e.slideNext(e.params.speed,!0,!0),e.emit("autoplay")),(e.params.cssMode&&e.autoplay.running||a===!1)&&e.autoplay.run()},t)},start:function(){var e=this;return typeof e.autoplay.timeout!="undefined"||e.autoplay.running?!1:(e.autoplay.running=!0,e.emit("autoplayStart"),e.autoplay.run(),!0)},stop:function(){var e=this;return!e.autoplay.running||typeof e.autoplay.timeout=="undefined"?!1:(e.autoplay.timeout&&(clearTimeout(e.autoplay.timeout),e.autoplay.timeout=void 0),e.autoplay.running=!1,e.emit("autoplayStop"),!0)},pause:function(e){var r=this;!r.autoplay.running||r.autoplay.paused||(r.autoplay.timeout&&clearTimeout(r.autoplay.timeout),r.autoplay.paused=!0,e===0||!r.params.autoplay.waitForTransition?(r.autoplay.paused=!1,r.autoplay.run()):["transitionend","webkitTransitionEnd"].forEach(function(t){r.$wrapperEl[0].addEventListener(t,r.autoplay.onTransitionEnd)}))},onVisibilityChange:function(){var e=this,r=G();r.visibilityState==="hidden"&&e.autoplay.running&&e.autoplay.pause(),r.visibilityState==="visible"&&e.autoplay.paused&&(e.autoplay.run(),e.autoplay.paused=!1)},onTransitionEnd:function(e){var r=this;!r||r.destroyed||!r.$wrapperEl||e.target===r.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach(function(t){r.$wrapperEl[0].removeEventListener(t,r.autoplay.onTransitionEnd)}),r.autoplay.paused=!1,r.autoplay.running?r.autoplay.run():r.autoplay.stop())},onMouseEnter:function(){var e=this;e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause(),["transitionend","webkitTransitionEnd"].forEach(function(r){e.$wrapperEl[0].removeEventListener(r,e.autoplay.onTransitionEnd)})},onMouseLeave:function(){var e=this;e.params.autoplay.disableOnInteraction||(e.autoplay.paused=!1,e.autoplay.run())},attachMouseEvents:function(){var e=this;e.params.autoplay.pauseOnMouseEnter&&(e.$el.on("mouseenter",e.autoplay.onMouseEnter),e.$el.on("mouseleave",e.autoplay.onMouseLeave))},detachMouseEvents:function(){var e=this;e.$el.off("mouseenter",e.autoplay.onMouseEnter),e.$el.off("mouseleave",e.autoplay.onMouseLeave)}},Pr={name:"autoplay",params:{autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}},create:function(){var e=this;q(e,{autoplay:Ee({},Mr,{running:!1,paused:!1})})},on:{init:function(e){if(e.params.autoplay.enabled){e.autoplay.start();var r=G();r.addEventListener("visibilitychange",e.autoplay.onVisibilityChange),e.autoplay.attachMouseEvents()}},beforeTransitionStart:function(e,r,t){e.autoplay.running&&(t||!e.params.autoplay.disableOnInteraction?e.autoplay.pause(r):e.autoplay.stop())},sliderFirstMove:function(e){e.autoplay.running&&(e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause())},touchEnd:function(e){e.params.cssMode&&e.autoplay.paused&&!e.params.autoplay.disableOnInteraction&&e.autoplay.run()},destroy:function(e){e.autoplay.detachMouseEvents(),e.autoplay.running&&e.autoplay.stop();var r=G();r.removeEventListener("visibilitychange",e.autoplay.onVisibilityChange)}}};function xe(){return xe=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},xe.apply(this,arguments)}var Lr={setTranslate:function(){var e=this,r=e.$el,t=e.$wrapperEl,a=e.slides,n=e.width,s=e.height,l=e.rtlTranslate,o=e.size,d=e.browser,f=e.params.cubeEffect,u=e.isHorizontal(),p=e.virtual&&e.params.virtual.enabled,c=0,v;f.shadow&&(u?(v=t.find(".swiper-cube-shadow"),v.length===0&&(v=h('<div class="swiper-cube-shadow"></div>'),t.append(v)),v.css({height:n+"px"})):(v=r.find(".swiper-cube-shadow"),v.length===0&&(v=h('<div class="swiper-cube-shadow"></div>'),r.append(v))));for(var g=0;g<a.length;g+=1){var w=a.eq(g),b=g;p&&(b=parseInt(w.attr("data-swiper-slide-index"),10));var C=b*90,y=Math.floor(C/360);l&&(C=-C,y=Math.floor(-C/360));var m=Math.max(Math.min(w[0].progress,1),-1),S=0,z=0,M=0;b%4===0?(S=-y*4*o,M=0):(b-1)%4===0?(S=0,M=-y*4*o):(b-2)%4===0?(S=o+y*4*o,M=o):(b-3)%4===0&&(S=-o,M=3*o+o*4*y),l&&(S=-S),u||(z=S,S=0);var T="rotateX("+(u?0:-C)+"deg) rotateY("+(u?C:0)+"deg) translate3d("+S+"px, "+z+"px, "+M+"px)";if(m<=1&&m>-1&&(c=b*90+m*90,l&&(c=-b*90-m*90)),w.transform(T),f.slideShadows){var P=u?w.find(".swiper-slide-shadow-left"):w.find(".swiper-slide-shadow-top"),E=u?w.find(".swiper-slide-shadow-right"):w.find(".swiper-slide-shadow-bottom");P.length===0&&(P=h('<div class="swiper-slide-shadow-'+(u?"left":"top")+'"></div>'),w.append(P)),E.length===0&&(E=h('<div class="swiper-slide-shadow-'+(u?"right":"bottom")+'"></div>'),w.append(E)),P.length&&(P[0].style.opacity=Math.max(-m,0)),E.length&&(E[0].style.opacity=Math.max(m,0))}}if(t.css({"-webkit-transform-origin":"50% 50% -"+o/2+"px","-moz-transform-origin":"50% 50% -"+o/2+"px","-ms-transform-origin":"50% 50% -"+o/2+"px","transform-origin":"50% 50% -"+o/2+"px"}),f.shadow)if(u)v.transform("translate3d(0px, "+(n/2+f.shadowOffset)+"px, "+-n/2+"px) rotateX(90deg) rotateZ(0deg) scale("+f.shadowScale+")");else{var L=Math.abs(c)-Math.floor(Math.abs(c)/90)*90,k=1.5-(Math.sin(L*2*Math.PI/360)/2+Math.cos(L*2*Math.PI/360)/2),x=f.shadowScale,D=f.shadowScale/k,I=f.shadowOffset;v.transform("scale3d("+x+", 1, "+D+") translate3d(0px, "+(s/2+I)+"px, "+-s/2/D+"px) rotateX(-90deg)")}var $=d.isSafari||d.isWebView?-o/2:0;t.transform("translate3d(0px,0,"+$+"px) rotateX("+(e.isHorizontal()?0:c)+"deg) rotateY("+(e.isHorizontal()?-c:0)+"deg)")},setTransition:function(e){var r=this,t=r.$el,a=r.slides;a.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),r.params.cubeEffect.shadow&&!r.isHorizontal()&&t.find(".swiper-cube-shadow").transition(e)}},Or={name:"effect-cube",params:{cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}},create:function(){var e=this;q(e,{cubeEffect:xe({},Lr)})},on:{beforeInit:function(e){if(e.params.effect==="cube"){e.classNames.push(e.params.containerModifierClass+"cube"),e.classNames.push(e.params.containerModifierClass+"3d");var r={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0};O(e.params,r),O(e.originalParams,r)}},setTranslate:function(e){e.params.effect==="cube"&&e.cubeEffect.setTranslate()},setTransition:function(e,r){e.params.effect==="cube"&&e.cubeEffect.setTransition(r)}}};function Me(){return Me=Object.assign||function(i){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}return i},Me.apply(this,arguments)}var zr={setTranslate:function(){for(var e=this,r=e.width,t=e.height,a=e.slides,n=e.slidesSizesGrid,s=e.params.coverflowEffect,l=e.isHorizontal(),o=e.translate,d=l?-o+r/2:-o+t/2,f=l?s.rotate:-s.rotate,u=s.depth,p=0,c=a.length;p<c;p+=1){var v=a.eq(p),g=n[p],w=v[0].swiperSlideOffset,b=(d-w-g/2)/g*s.modifier,C=l?f*b:0,y=l?0:f*b,m=-u*Math.abs(b),S=s.stretch;typeof S=="string"&&S.indexOf("%")!==-1&&(S=parseFloat(s.stretch)/100*g);var z=l?0:S*b,M=l?S*b:0,T=1-(1-s.scale)*Math.abs(b);Math.abs(M)<.001&&(M=0),Math.abs(z)<.001&&(z=0),Math.abs(m)<.001&&(m=0),Math.abs(C)<.001&&(C=0),Math.abs(y)<.001&&(y=0),Math.abs(T)<.001&&(T=0);var P="translate3d("+M+"px,"+z+"px,"+m+"px)  rotateX("+y+"deg) rotateY("+C+"deg) scale("+T+")";if(v.transform(P),v[0].style.zIndex=-Math.abs(Math.round(b))+1,s.slideShadows){var E=l?v.find(".swiper-slide-shadow-left"):v.find(".swiper-slide-shadow-top"),L=l?v.find(".swiper-slide-shadow-right"):v.find(".swiper-slide-shadow-bottom");E.length===0&&(E=h('<div class="swiper-slide-shadow-'+(l?"left":"top")+'"></div>'),v.append(E)),L.length===0&&(L=h('<div class="swiper-slide-shadow-'+(l?"right":"bottom")+'"></div>'),v.append(L)),E.length&&(E[0].style.opacity=b>0?b:0),L.length&&(L[0].style.opacity=-b>0?-b:0)}}},setTransition:function(e){var r=this;r.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)}},Ir={name:"effect-coverflow",params:{coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}},create:function(){var e=this;q(e,{coverflowEffect:Me({},zr)})},on:{beforeInit:function(e){e.params.effect==="coverflow"&&(e.classNames.push(e.params.containerModifierClass+"coverflow"),e.classNames.push(e.params.containerModifierClass+"3d"),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},setTranslate:function(e){e.params.effect==="coverflow"&&e.coverflowEffect.setTranslate()},setTransition:function(e,r){e.params.effect==="coverflow"&&e.coverflowEffect.setTransition(r)}}};je.use([Pr,Ir,Or,xr,Sr]);const kr={name:"msSwiper",components:{},setup(i){const{getters:e}=Ze(),r=Ke({list:[{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u5B9A\u91D1\u98CE.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u9AD8\u6C38\u5229.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u9EC4\u4E3D\u7EA2.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u5415\u660E.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u9676\u535A\u58EB.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u59DA\u5A1C.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u5F20\u535A\u58EB.png",red_url:""},{img_url:"https://static.medsci.cn/public-image/ms-image/7c8eed70-3609-11ec-8e2f-1389d01aad85_\u5468\u4E1C\u4E1C.png",red_url:""}]});et(async()=>{t()});const t=()=>{new je(".swiper1",{loop:!0,slidesPerView:1.2,slidesOffsetBefore:30,autoplay:{delay:3e3,stopOnLastSlide:!1,disableOnInteraction:!1}})},a=n=>{!n.red_url||(window.location.href=n.red_url)};return{...tt(r),getters:e,link:a,initSwiper:t}}},Ar=i=>(nt("data-v-3b5de9fc"),i=i(),st(),i),$r={class:"swiper-container swiper1"},Br={class:"swiper-wrapper"},Gr=["src","onClick"],Dr=Ar(()=>ge("div",{class:"swiper-pagination"},null,-1));function Nr(i,e,r,t,a,n){return fe(),ue("div",$r,[ge("div",Br,[(fe(!0),ue(it,null,rt(i.list,s=>(fe(),ue("div",{class:at(["swiper-slide",{"pc-swiper-slide":t.getters.model=="pc"}]),key:s.id},[ge("img",{src:s.img_url,alt:"",onClick:l=>t.link(s)},null,8,Gr)],2))),128))]),Dr])}var Fr=Qe(kr,[["render",Nr],["__scopeId","data-v-3b5de9fc"]]);export{Fr as m};
