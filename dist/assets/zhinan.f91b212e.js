import{_ as G,u as K,a as Y,r as n,b as B,o as Z,T as v,t as ee,c as C,d as se,e as te,f as c,g as U,B as ae,w as ne,F as oe,h as ie,p as ce,i as re}from"./index.8f4d7f86.js";import{s as m,C as le}from"./js.cookie.ad72bcd1.js";import{f as de}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b as _}from"./config.3aca39f6.js";const me={name:"Home",components:{falseData:de,msSwiper:pe},setup:()=>{K();const t=Y(),r=n(!1),q=n(!1),b=n(!1),y=n(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),M=n(!1),l=n(""),f=n(""),h=n(""),u=n(""),S=n(!1),L=n(!1),O=n(""),A=n("college"),k=n(!1),R=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],W=e=>{S.value=!1,z(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},g=B({info:{}}),o=B({msg:{}}),w=e=>{for(var s=window.location.search.substring(1),a=s.split("&"),i=0;i<a.length;i++){var p=a[i].split("=");if(p[0]==e)return p[1]}return!1};Z(async()=>{document.title="\u6885\u65AF\u533B\u5B66\u6307\u5357\u4F1A\u5458\u5168\u7F51\u9996\u53D1",P({title:"\u6885\u65AF\u533B\u5B66\u6307\u5357\u4F1A\u5458\u5168\u7F51\u9996\u53D1",summary:"\u9996\u6708\u7279\u60E0\u4EC519\u5143\uFF0C\u901F\u67AA\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/21bd5620-3b12-11ed-b66b-937b834e3ef9_zhinan-fenxiang.png"});const e=await m.post("/activity/memberCardDetail",{id:"****************"});o.msg=e.data,console.log(o.msg,"11"),o.msg&&o.msg.activityEndTime&&(o.msg.activityEndTime=o.msg.activityEndTime.replace(/-/g,"/"),y.value=new Date(o.msg.activityEndTime).getTime()-new Date().getTime()),y.value<0&&(k.value=!0);let s=navigator.userAgent;s!=null&&s.indexOf("MicroMessenger")>-1&&(t.query.openId||w("openId"))&&D()});const P=async e=>{let s=window.location.href.split("#")[0];const a=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(a.data),wx.error(function(i){console.log(i)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/zhinan",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/zhinan",imgUrl:e.thumb,success:function(){}})})},$=()=>{const e=navigator.userAgent,s=t.query.openId||w("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){l.value="WX";const a="wx9096048917ec59ab";if(s)u.value=s;else{const i=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${i}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?l.value="ALI":l.value="ALL"},D=async()=>{const e=le.get("userInfo");if(e&&($(),v.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"})),e)g.info=JSON.parse(e),g.info.mobile||addLoginDom(),x();else{const s=t.query.sso_sessionid||w("sso_sessionid");if(s){const a=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});g.info=a.data,x()}else E()}},E=()=>{addLoginDom()},j=e=>{e.link&&(window.location.href=e.link)},x=async()=>{const{userId:e,userName:s,realName:a,mobile:i,email:p,plaintextUserId:T}=g.info,d=await m.post("/activity/createOrder",{itemId:o.msg.id,itemNum:1,itemPicPath:o.msg.cardImage,itemTitle:o.msg.cardName,itemPrice:o.msg.firstPrice,projectId:o.msg.projectId,orderType:1,mobile:i,payment:0,userId:e,nikeName:s,buyerMessage:"\u6307\u5357\u4F1A\u5458\u5168\u7F51\u91CD\u78C5\u9996\u53D1\u6D3B\u52A8"});f.value=d.data,v.clear(),X()},H=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},J=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},V=()=>{b.value=!0},F=()=>{k.value=!0},X=async()=>{if(window.innerWidth<750)l.value=="ALL"?S.value=!0:z(l.value);else{const e=await m.post(_+"/payment/pay/merge_qrcode",{accessAppId:A.value,appOrderId:f.value}),{qrCodeUrl:s}=e.data;s&&(L.value=!0,O.value=s);const a=setInterval(()=>{Q(),h.value=="PAID"&&clearInterval(a)},3e3)}M.value=!1},Q=async()=>{const e=await m.get(_+"/payment/pay/query",{params:{appOrderId:f.value}}),{paymentStatus:s}=e.data;h.value=s,s=="PAID"&&v("\u652F\u4ED8\u6210\u529F")},z=async e=>{const s=await m.post(_+"/payment/pay/build",{accessAppId:A.value,appOrderId:f.value,payChannel:e,paySource:"MEDSCI_WEB",payType:l.value=="ALL"?"MWEB":l.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){v(s.msg);return}const a=await m.post(_+"/payment/pay/order",{accessAppId:A.value,payOrderId:s.data.payOrderId,openId:u.value}),{aliH5:i,aliQR:p,wechatH5:T,wechatJsapi:d}=a.data;if(i){const I=document.createElement("div");I.innerHTML=i.html,document.body.appendChild(I),document.forms[0].submit()}p&&(window.location.href=p.payUrl),T&&(window.location.href=T.h5Url),d&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:d.appId,timeStamp:d.timeStamp,nonceStr:d.nonceStr,package:d.packageStr,signType:d.signType,paySign:d.paySign},function(I){I.err_msg=="get_brand_wcpay_request:ok"&&v.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...ee(o),loading:r,userInfo:g,active:q,guize:b,time:y,actions:R,show:S,showImg:L,qrCodeUrlImg:O,isEnd:k,Login:E,buy:D,Pay:x,testPlay:j,getQueryVariable:w,wxShare:P,link:H,showGuize:V,formatTime:J,onFinish:F,onSelect:W}}},N=t=>(ce("data-v-7184f42e"),t=t(),re(),t),ue={class:"box"},fe=N(()=>c("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/ce78bcc0-3aee-11ed-b66b-937b834e3ef9_zhinan.jpg?v=1",alt:""},null,-1)),ge={class:"last"},ve={class:"bottom"},ye={class:"bottom-left"},he={class:"Num"},we=N(()=>c("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Ie=["src"];function _e(t,r,q,b,y,M){const l=C("false-data"),f=C("van-action-sheet"),h=C("van-overlay");return se(),te(oe,null,[c("div",ue,[U(l),fe,c("div",ge,[c("div",ve,[c("div",ye,[c("div",he," \u6307\u5357\u4F1A\u5458\u9996\u6708\u7279\u60E0\uFF1A"+ae(t.msg.firstPrice)+"\u5143/\u6708 ",1)]),c("div",{class:"bottom-right",onClick:r[0]||(r[0]=(...u)=>t.buy&&t.buy(...u))}," \u70B9\u51FB\u52A0\u5165 ")])])]),U(f,{show:t.show,"onUpdate:show":r[1]||(r[1]=u=>t.show=u),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),U(h,{show:t.showImg,onClick:r[3]||(r[3]=u=>t.showImg=!1)},{default:ne(()=>[c("div",{class:"wrapper",onClick:r[2]||(r[2]=ie(()=>{},["stop"]))},[c("div",null,[we,c("img",{src:t.qrCodeUrlImg,alt:""},null,8,Ie)])])]),_:1},8,["show"])],64)}var Te=G(me,[["render",_e],["__scopeId","data-v-7184f42e"]]);export{Te as default};
