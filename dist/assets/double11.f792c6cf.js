import{_ as te,u as se,a as ne,r as s,b as P,o as ae,t as oe,c as A,d as ie,e as ce,f as l,g as U,w as re,F as le,T as m,h as de,p as pe,i as ue}from"./index.8f4d7f86.js";import{C as N,s as d}from"./js.cookie.ad72bcd1.js";import{f as me}from"./falseData.c0306b2a.js";import{m as fe}from"./msSwiper.7d61aaec.js";import{b as v}from"./config.3aca39f6.js";import{b as V}from"./configFreeVip.d32e6c36.js";import"./sanofiConfig.a80970f2.js";const ge={name:"double11",components:{falseData:me,msSwiper:fe},setup:()=>{se();const n=ne(),c=s(!1),C=s("1"),q=s(""),T=s(!1),L=s(!1),f=s(!1),h=s(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),y=s(!1),o=s(""),g=s(""),B=s(""),M=s(""),_=s(!1),I=s(!1),D=s(""),S=s("college"),O=s(!1),z=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],F=e=>{_.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},i=P({info:{}}),J=P({msg:{}}),b=e=>{for(var t=window.location.search.substring(1),a=t.split("&"),r=0;r<a.length;r++){var p=a[r].split("=");if(p[0]==e)return p[1]}return!1};ae(async()=>{document.title="\u516C\u5F00\u8BFE\u4F1A\u5458&\u53CC11",R({title:"\u516C\u5F00\u8BFE\u4F1A\u5458&\u53CC11",summary:"4\u6298\u5F00\u901A\uFF0C\u89E3\u9501\u4E0A\u5343\u8282\u4E34\u5E8A+\u79D1\u7814+\u533B\u9662\u7BA1\u7406\u8BFE\u7A0B",thumb:"https://static.medsci.cn/public-image/ms-image/a06e2900-7e0e-11ee-8419-490633d98253_\u5206\u4EAB\u56FE.png"}),N.get("userInfo"),d.get(V+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"new_user_register"}})});const R=async e=>{let t=window.location.href.split("#")[0];const a=await d.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(a.data),wx.error(function(r){console.log(r)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/double11",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/double11",imgUrl:e.thumb,success:function(){}})})},H=()=>{const e=navigator.userAgent,t=n.query.openId||b("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){o.value="WX";const a="wx9096048917ec59ab";if(t)M.value=t;else{const r=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${r}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?o.value="ALI":o.value="ALL"},X=async()=>{H(),m.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=N.get("userInfo");if(e)i.info=JSON.parse(e),i.info.mobile?x():addLoginDom();else{const t=n.query.sso_sessionid||b("sso_sessionid");if(t){const a=await d.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});i.info=a.data,x()}else W()}},W=()=>{addLoginDom()},j=e=>{e.link&&(window.location.href=e.link)},x=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const e=await d.post(V+"/medsci-activity/pay/member-card/",{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,type:"double_eleven_card"});e.code!==200&&e.code!==205&&(m.clear(),m(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),g.value=e.data.data,m.clear(),Z()},Q=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},G=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},K=()=>{f.value=!0},Y=()=>{O.value=!0},Z=async()=>{if(window.innerWidth<750)o.value=="ALL"?_.value=!0:$(o.value);else{const e=await d.post(v+"/payment/pay/merge_qrcode",{accessAppId:S.value,appOrderId:g.value}),{qrCodeUrl:t}=e.data;t&&(I.value=!0,D.value=t);const a=setInterval(()=>{ee(),B.value=="PAID"&&(I.value=!1,clearInterval(a))},3e3)}y.value=!1},ee=async()=>{const e=await d.get(v+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:t}=e.data;B.value=t,t=="PAID"&&(m("\u652F\u4ED8\u6210\u529F"),window.location.href="https://open.medsci.cn/")},$=async e=>{const t=await d.post(v+"/payment/pay/build",{accessAppId:S.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:o.value=="ALL"?"MWEB":o.value=="WX"?"JSAPI":"NATIVE"});if(t.code!="SUCCESS"){m(t.msg);return}const a=await d.post(v+"/payment/pay/order",{accessAppId:S.value,payOrderId:t.data.payOrderId,openId:M.value}),{aliH5:r,aliQR:p,wechatH5:E,wechatJsapi:u}=a.data;if(r){const w=document.createElement("div");w.innerHTML=r.html,document.body.appendChild(w),document.forms[0].submit()}p&&(window.location.href=p.payUrl),E&&(window.location.href=E.h5Url),u&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:u.appId,timeStamp:u.timeStamp,nonceStr:u.nonceStr,package:u.packageStr,signType:u.signType,paySign:u.paySign},function(w){w.err_msg=="get_brand_wcpay_request:ok"&&(m.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://open.medsci.cn/")})};return{curVal:C,...oe(J),loading:T,userInfo:i,active:L,guize:f,time:h,vipType:q,actions:z,show:_,showImg:I,qrCodeUrlImg:D,isEnd:O,timing:c,getBtn:X,Login:W,Pay:x,testPlay:j,getQueryVariable:b,wxShare:R,link:Q,showGuize:K,formatTime:G,onFinish:Y,onSelect:F}}},k=n=>(pe("data-v-915ddbe4"),n=n(),ue(),n),we={class:"box"},ve=k(()=>l("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/3623c370-7dff-11ee-8419-490633d98253_bcg.png",alt:""},null,-1)),he={class:"last"},ye=k(()=>l("img",{src:"https://static.medsci.cn/public-image/ms-image/43325a90-7dff-11ee-8419-490633d98253_btn.png",alt:""},null,-1)),_e=[ye],Ie=k(()=>l("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Se=["src"];function be(n,c,C,q,T,L){const f=A("false-data"),h=A("van-action-sheet"),y=A("van-overlay");return ie(),ce(le,null,[l("div",we,[U(f),ve,l("div",he,[l("div",{class:"bottom",onClick:c[0]||(c[0]=(...o)=>n.getBtn&&n.getBtn(...o))},_e)])]),U(h,{show:n.show,"onUpdate:show":c[1]||(c[1]=o=>n.show=o),actions:n.actions,onSelect:n.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),U(y,{show:n.showImg,onClick:c[3]||(c[3]=o=>n.showImg=!1)},{default:re(()=>[l("div",{class:"wrapper",onClick:c[2]||(c[2]=de(()=>{},["stop"]))},[l("div",null,[Ie,l("img",{src:n.qrCodeUrlImg,alt:""},null,8,Se)])])]),_:1},8,["show"])],64)}var Le=te(ge,[["render",be],["__scopeId","data-v-915ddbe4"]]);export{Le as default};
