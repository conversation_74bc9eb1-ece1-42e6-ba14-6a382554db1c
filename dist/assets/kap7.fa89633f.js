import{_ as K,u as X,a as j,r as o,b as N,o as z,T as f,t as H,c as T,d as F,e as Q,f as c,g as B,w as G,F as Y,h as Z,p as ee,i as se}from"./index.8f4d7f86.js";import{s as m,C as te}from"./js.cookie.ad72bcd1.js";import{m as ae}from"./msSwiper.7d61aaec.js";import{b as ne,a as oe}from"./configkap.935700b6.js";import{b as S}from"./config.3aca39f6.js";const ie={name:"huxXi",components:{msSwiper:ae},setup:()=>{X();const t=j(),r=o(!1),x=o(!1),U=o(!1),d=o(""),g=o(""),y=o(""),w=o(""),p=o(!1),C=o(!1),q=o(""),b=o("college"),E=o(!1),R=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],W=e=>{p.value=!1,P(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},v=N({info:{}}),u=N({msg:{}}),h=e=>{for(var s=window.location.search.substring(1),n=s.split("&"),a=0;a<n.length;a++){var l=n[a].split("=");if(l[0]==e)return l[1]}return!1};z(async()=>{document.title="\u3010KAP\u4EA7\u54C1\u72EC\u5BB6\u9996\u53D1\u3011\u5B9A\u91D11\u5143\u62B5\u62632\u4E07\u5143\uFF01",L({title:"\u3010KAP\u4EA7\u54C1\u72EC\u5BB6\u9996\u53D1\u3011\u5B9A\u91D11\u5143\u62B5\u62632\u4E07\u5143\uFF01",summary:"\u77ED\u671F\u5B9E\u73B0\u5F2F\u9053\u8D85\u8F66\u6700\u65B0\u65B9\u6848\uFF0C\u70B9\u51FB\u6765\u4E86\u89E3\u5427\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/79a3d0d0-03e0-11ed-a1b8-6123b3ff61ea_share.png"});const e=await m.get(ne+"/activityNew/d9ca10a");u.msg=e.data;let s=navigator.userAgent;s!=null&&s.indexOf("MicroMessenger")>-1&&(t.query.openId||h("openId"))&&M()});const L=async e=>{let s=window.location.href.split("#")[0];const n=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(n.data),wx.error(function(a){console.log(a)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kap7",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kap7",imgUrl:e.thumb,success:function(){}})})},M=async()=>{const e=navigator.userAgent,s=t.query.openId||h("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){d.value="WX";const a="wx9096048917ec59ab";if(s)w.value=s;else{const l=encodeURIComponent(window.location.href),I=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${l}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${I}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;return}}else e!=null&&e.indexOf("AlipayClient")>-1?d.value="ALI":d.value="ALL";const n=te.get("userInfo");if(f.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),n)v.info=JSON.parse(n),v.info.mobile||addLoginDom(),A();else{const a=t.query.sso_sessionid||h("sso_sessionid");if(a){const l=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:a});v.info=l.data,A()}else O()}},O=()=>{addLoginDom()},$=e=>{e.link&&(window.location.href=e.link)},A=async()=>{const{userId:e,userName:s,realName:n,mobile:a,email:l,plaintextUserId:I}=v.info,i=await m.post(oe+"/openOrder/addActivityOrder",{activityId:u.msg.id,itemNum:1,itemPicPath:"",activityName:u.msg.name,itemPrice:u.msg.money,projectId:1,orderType:u.msg.type,mobile:a,payment:u.msg.money,userId:e,nikeName:s,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});i.status==200?(g.value=i.data,f.clear(),V()):f(i.message)},D=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},V=async()=>{if(window.innerWidth<750)d.value=="ALL"?p.value=!0:P(d.value);else{const e=await m.post(S+"/payment/pay/merge_qrcode",{accessAppId:b.value,appOrderId:g.value}),{qrCodeUrl:s}=e.data;s&&(C.value=!0,q.value=s);const n=setInterval(()=>{J(),y.value=="PAID"&&clearInterval(n)},3e3)}U.value=!1},J=async()=>{const e=await m.get(S+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:s}=e.data;y.value=s,s=="PAID"&&f("\u652F\u4ED8\u6210\u529F")},P=async e=>{const s=await m.post(S+"/payment/pay/build",{accessAppId:b.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:d.value=="ALL"?"MWEB":d.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){f(s.msg);return}const n=await m.post(S+"/payment/pay/order",{accessAppId:b.value,payOrderId:s.data.payOrderId,openId:w.value}),{aliH5:a,aliQR:l,wechatH5:I,wechatJsapi:i}=n.data;if(a){const _=document.createElement("div");_.innerHTML=a.html,document.body.appendChild(_),document.forms[0].submit()}l&&(window.location.href=l.payUrl),I&&f.fail("\u7528\u5FAE\u4FE1\u7AEF\u6253\u5F00\u8FDB\u884C\u652F\u4ED8"),i&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:i.appId,timeStamp:i.timeStamp,nonceStr:i.nonceStr,package:i.packageStr,signType:i.signType,paySign:i.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&f.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...H(u),loading:r,userInfo:v,active:x,actions:R,show:p,showImg:C,qrCodeUrlImg:q,isEnd:E,Login:O,buy:M,Pay:A,testPlay:$,getQueryVariable:h,wxShare:L,link:D,onSelect:W}}},k=t=>(ee("data-v-3cc0944e"),t=t(),se(),t),ce={class:"box"},re=k(()=>c("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/9d01ac10-0803-11ed-a1b8-6123b3ff61ea_kap\u7EC8\u7248.png",alt:""},null,-1)),le={class:"last"},de={class:"bottom"},pe=k(()=>c("div",{class:"bottom-left"},[c("div",{class:"Num"}," \u83B7\u53D61V1\u987E\u95EE\u8BB2\u89E3 ")],-1)),me={class:"wrapper"},ue=k(()=>c("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),fe=["src"];function ge(t,r,x,U,d,g){const y=T("van-action-sheet"),w=T("van-overlay");return F(),Q(Y,null,[c("div",ce,[re,c("div",le,[c("div",de,[pe,c("div",{class:"bottom-right",onClick:r[0]||(r[0]=(...p)=>t.buy&&t.buy(...p))}," \u652F\u4ED81\u5143\u5B9A\u91D1 ")])])]),B(y,{show:t.show,"onUpdate:show":r[1]||(r[1]=p=>t.show=p),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),B(w,{show:t.showImg,onClick:r[3]||(r[3]=p=>t.showImg=!1)},{default:G(()=>[c("div",me,[c("div",{onClick:r[2]||(r[2]=Z(()=>{},["stop"]))},[ue,c("img",{src:t.qrCodeUrlImg,alt:""},null,8,fe)])])]),_:1},8,["show"])],64)}var _e=K(ie,[["render",ge],["__scopeId","data-v-3cc0944e"]]);export{_e as default};
