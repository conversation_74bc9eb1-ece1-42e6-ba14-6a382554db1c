import{_ as se,u as te,a as ae,r as t,b as P,o as ne,t as oe,c as A,d as ie,e as ce,f as l,g as U,w as re,F as le,T as u,h as de,p as pe,i as me}from"./index.8f4d7f86.js";import{C as N,s as d}from"./js.cookie.ad72bcd1.js";import{f as ue}from"./falseData.c0306b2a.js";import{m as fe}from"./msSwiper.7d61aaec.js";import{b as v}from"./config.3aca39f6.js";import{b as V}from"./configFreeVip.d32e6c36.js";import"./sanofiConfig.a80970f2.js";const ge={name:"scale",components:{falseData:ue,msSwiper:fe},setup:()=>{te();const a=ae(),c=t(!1),C=t("1"),q=t(""),T=t(!1),L=t(!1),f=t(!1),h=t(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),y=t(!1),o=t(""),g=t(""),B=t(""),M=t(""),_=t(!1),I=t(!1),D=t(""),S=t("college"),O=t(!1),z=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],F=e=>{_.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},i=P({info:{}}),J=P({msg:{}}),b=e=>{for(var s=window.location.search.substring(1),n=s.split("&"),r=0;r<n.length;r++){var p=n[r].split("=");if(p[0]==e)return p[1]}return!1};ne(async()=>{document.title="\u6885\u65AF\u533B\u5B66\u8BA1\u7B97\u516C\u5F0F\u4F1A\u5458\u6708\u5361",R({title:"\u6885\u65AF\u533B\u5B66\u8BA1\u7B97\u516C\u5F0F\u4F1A\u5458\u6708\u5361",summary:"\u70B9\u51FB\u7ACB\u5373\u5F00\u901A",thumb:"https://static.medsci.cn/public-image/ms-image/202406261030_\u533B\u5B66\u516C\u5F0F\u5206\u4EAB.png"}),N.get("userInfo"),d.get(V+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"new_user_register"}})});const R=async e=>{let s=window.location.href.split("#")[0];const n=await d.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(n.data),wx.error(function(r){console.log(r)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/scale",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/scale",imgUrl:e.thumb,success:function(){}})})},H=()=>{const e=navigator.userAgent,s=a.query.openId||b("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){o.value="WX";const n="wx9096048917ec59ab";if(s)M.value=s;else{const r=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${r}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?o.value="ALI":o.value="ALL"},X=async()=>{H(),u.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=N.get("userInfo");if(e)i.info=JSON.parse(e),i.info.mobile?x():addLoginDom();else{const s=a.query.sso_sessionid||b("sso_sessionid");if(s){const n=await d.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});i.info=n.data,x()}else W()}},W=()=>{addLoginDom()},j=e=>{e.link&&(window.location.href=e.link)},x=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const e=await d.post(V+"/medsci-activity/pay/member-card/",{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,type:"scale_card"});e.code!==200&&e.code!==205&&(u.clear(),u(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),g.value=e.data.data,u.clear(),Z()},Q=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},G=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},K=()=>{f.value=!0},Y=()=>{O.value=!0},Z=async()=>{if(window.innerWidth<750)o.value=="ALL"?_.value=!0:$(o.value);else{const e=await d.post(v+"/payment/pay/merge_qrcode",{accessAppId:S.value,appOrderId:g.value}),{qrCodeUrl:s}=e.data;s&&(I.value=!0,D.value=s);const n=setInterval(()=>{ee(),B.value=="PAID"&&(I.value=!1,clearInterval(n))},3e3)}y.value=!1},ee=async()=>{const e=await d.get(v+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:s}=e.data;B.value=s,s=="PAID"&&(u("\u652F\u4ED8\u6210\u529F"),window.location.href="https://m.medsci.cn/scale/index.do")},$=async e=>{const s=await d.post(v+"/payment/pay/build",{accessAppId:S.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:o.value=="ALL"?"MWEB":o.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){u(s.msg);return}const n=await d.post(v+"/payment/pay/order",{accessAppId:S.value,payOrderId:s.data.payOrderId,openId:M.value}),{aliH5:r,aliQR:p,wechatH5:E,wechatJsapi:m}=n.data;if(r){const w=document.createElement("div");w.innerHTML=r.html,document.body.appendChild(w),document.forms[0].submit()}p&&(window.location.href=p.payUrl),E&&(window.location.href=E.h5Url),m&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:m.appId,timeStamp:m.timeStamp,nonceStr:m.nonceStr,package:m.packageStr,signType:m.signType,paySign:m.paySign},function(w){w.err_msg=="get_brand_wcpay_request:ok"&&(u.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://m.medsci.cn/scale/index.do")})};return{curVal:C,...oe(J),loading:T,userInfo:i,active:L,guize:f,time:h,vipType:q,actions:z,show:_,showImg:I,qrCodeUrlImg:D,isEnd:O,timing:c,getBtn:X,Login:W,Pay:x,testPlay:j,getQueryVariable:b,wxShare:R,link:Q,showGuize:K,formatTime:G,onFinish:Y,onSelect:F}}},k=a=>(pe("data-v-3f33d7b7"),a=a(),me(),a),we={class:"box"},ve=k(()=>l("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/202406261030_\u533B\u5B66\u516C\u5F0F-\u65E0\u6559\u7A0B.png",alt:""},null,-1)),he={class:"last"},ye=k(()=>l("img",{src:"https://static.medsci.cn/public-image/ms-image/202406261030_\u533B\u5B66\u516C\u5F0Fbtn.png",alt:""},null,-1)),_e=[ye],Ie=k(()=>l("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Se=["src"];function be(a,c,C,q,T,L){const f=A("false-data"),h=A("van-action-sheet"),y=A("van-overlay");return ie(),ce(le,null,[l("div",we,[U(f),ve,l("div",he,[l("div",{class:"bottom",onClick:c[0]||(c[0]=(...o)=>a.getBtn&&a.getBtn(...o))},_e)])]),U(h,{show:a.show,"onUpdate:show":c[1]||(c[1]=o=>a.show=o),actions:a.actions,onSelect:a.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),U(y,{show:a.showImg,onClick:c[3]||(c[3]=o=>a.showImg=!1)},{default:re(()=>[l("div",{class:"wrapper",onClick:c[2]||(c[2]=de(()=>{},["stop"]))},[l("div",null,[Ie,l("img",{src:a.qrCodeUrlImg,alt:""},null,8,Se)])])]),_:1},8,["show"])],64)}var Le=se(ge,[["render",be],["__scopeId","data-v-3f33d7b7"]]);export{Le as default};
