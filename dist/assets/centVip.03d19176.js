import{_ as ce,u as le,a as re,r as a,b as z,o as de,t as pe,c as q,d as L,e as O,f as c,g as B,F,E as me,w as ue,T as p,h as fe,p as ge,i as he}from"./index.8f4d7f86.js";import{C as J,s as m}from"./js.cookie.ad72bcd1.js";import{f as ve}from"./falseData.c0306b2a.js";import{m as we}from"./msSwiper.7d61aaec.js";import{b as y}from"./config.3aca39f6.js";import{b}from"./configFreeVip.d32e6c36.js";const _e={name:"centVip",components:{falseData:ve,msSwiper:we},setup:()=>{le();const t=re(),l=a(!1),I=a([{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_lessons.png",id:"1",isSelect:!0},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_guide.png",id:"2",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_journal.png",id:"3",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_fund.png",id:"4",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_breath.png",id:"5",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_neurology.png",id:"6",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_cardio.png",id:"7",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/a36ee5e0-3741-11ee-aed8-05e366306843_pifuke.png",id:"8",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_eye.png",id:"9",isSelect:!1}]),r=a("1"),M=a(""),V=a(!1),S=a(!1),w=a(!1),k=a(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),d=a(!1),u=a(""),g=a(""),E=a(""),$=a(""),x=a(!1),A=a(!1),R=a(""),C=a("college"),W=a(!1),H=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],X=e=>{x.value=!1,j(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},o=z({info:{}}),Q=z({msg:{}}),U=e=>{for(var s=window.location.search.substring(1),i=s.split("&"),n=0;n<i.length;n++){var f=i[n].split("=");if(f[0]==e)return f[1]}return!1};de(async()=>{document.title="819\u533B\u5E08\u8282\uFF0C0.01\u5143\u4EFB\u9009\u6885\u65AF\u4F1A\u5458\uFF01",P({title:"819\u533B\u5E08\u8282\uFF0C0.01\u5143\u4EFB\u9009\u6885\u65AF\u4F1A\u5458\uFF01",summary:"\u70B9\u51FB\u83B7\u53D6\uFF01\u4EC5\u9650\u533B\u62A4\u4EBA\u5458\uFF0C\u4EC5\u9650\u4ECA\u5929\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/e0f90730-3659-11ee-aed8-05e366306843_819.png"}),J.get("userInfo"),m.get(b+"/medsci-activity/visit",{params:{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,event_type:"view",type:"physicians_day"}})});const P=async e=>{let s=window.location.href.split("#")[0];const i=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(i.data),wx.error(function(n){console.log(n)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVip",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVip",imgUrl:e.thumb,success:function(){}})})},G=()=>{const e=navigator.userAgent,s=t.query.openId||U("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){u.value="WX";const i="wx9096048917ec59ab";if(s)$.value=s;else{const n=encodeURIComponent(window.location.href),f=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${i}?returnUrl=${n}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${i}&redirect_uri=${f}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?u.value="ALI":u.value="ALL"},K=async e=>{r.value=e,I.value.forEach(s=>{r.value==s.id?s.isSelect=!0:s.isSelect=!1})},Y=async()=>{G(),p.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=J.get("userInfo");if(e)o.info=JSON.parse(e),o.info.mobile?T():addLoginDom();else{const s=t.query.sso_sessionid||U("sso_sessionid");if(s){const i=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});o.info=i.data,T()}else N()}},N=()=>{addLoginDom()},Z=e=>{e.link&&(window.location.href=e.link)},T=async()=>{let e=new Date().getTime(),s=new Date("2023/08/19 00:00:00").getTime(),i=new Date("2023/08/19 23:59:59").getTime();if(e<s)return p("\u6D3B\u52A8\u8FD8\u6CA1\u5F00\u59CB\uFF0C\u518D\u7B49\u7B49\u54E6"),!1;if(!(e>s&&e<i)){if(e>i)return p("\u6D3B\u52A8\u5DF2\u7ED3\u675F\uFF0C\u4E0B\u6B21\u518D\u6765\u5427"),!1}const n=await m.post(b+"/medsci-activity/attend-status",{order_id:g.value,type:"physicians_day",mobile:o.info.mobile});if(n.data.status==0){let f={1:"scientific_research",2:"guider",3:"journal",4:"nsfc",5:"breathe",6:"nerve",7:"cardiovascular",8:"skin",9:"ophthalmology"};const h=await m.post(b+"/medsci-activity/pay/member-card/"+f[r.value],{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,mobile:o.info.mobile,user_name:o.info.userName,real_name:o.info.realName,type:"physicians_day"});h.code!==200&&h.code!==205&&(p.clear(),p(h.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),g.value=h.data.data,p.clear(),ie()}else(n.data.status==1||res.code==205)&&p("\u60A8\u5DF2\u7ECF\u9886\u8FC7\u4E86\u54E6\uFF01")},ee=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},se=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},te=()=>{w.value=!0},ae=()=>{W.value=!0},ie=async()=>{if(window.innerWidth<750)u.value=="ALL"?x.value=!0:j(u.value);else{const e=await m.post(y+"/payment/pay/merge_qrcode",{accessAppId:C.value,appOrderId:g.value}),{qrCodeUrl:s}=e.data;s&&(A.value=!0,R.value=s);const i=setInterval(()=>{oe(),E.value=="PAID"&&(A.value=!1,ne(),clearInterval(i))},3e3)}d.value=!1},ne=async()=>{const e=await m.post(b+"/medsci-activity/pay/member-card-status",{order_id:g.value,type:"physicians_day"});console.log(e,"\u4E0B\u5355\u6210\u529F")},oe=async()=>{const e=await m.get(y+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:s}=e.data;E.value=s,s=="PAID"&&(p("\u652F\u4ED8\u6210\u529F"),console.log(r),r.value=="3"?window.location.href="https://www.medsci.cn/sci/index.do":r.value=="2"?window.location.href="https://www.medsci.cn/guideline/index.do":r.value=="4"?window.location.href="https://www.medsci.cn/sci/nsfc.do?utm_campaign":["1","5","6","7","8","9"].includes(r.value)&&(window.location.href="https://class.medsci.cn/"))},j=async e=>{const s=await m.post(y+"/payment/pay/build",{accessAppId:C.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:u.value=="ALL"?"MWEB":u.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){p(s.msg);return}const i=await m.post(y+"/payment/pay/order",{accessAppId:C.value,payOrderId:s.data.payOrderId,openId:$.value}),{aliH5:n,aliQR:f,wechatH5:h,wechatJsapi:v}=i.data;if(n){const _=document.createElement("div");_.innerHTML=n.html,document.body.appendChild(_),document.forms[0].submit()}f&&(window.location.href=f.payUrl),h&&(window.location.href=h.h5Url),v&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:v.appId,timeStamp:v.timeStamp,nonceStr:v.nonceStr,package:v.packageStr,signType:v.signType,paySign:v.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&p.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{curVal:r,urls:I,...pe(Q),loading:V,userInfo:o,active:S,guize:w,time:k,vipType:M,actions:H,show:x,showImg:A,qrCodeUrlImg:R,isEnd:W,timing:l,getBtn:Y,Login:N,Pay:T,testPlay:Z,getQueryVariable:U,wxShare:P,link:ee,showGuize:te,formatTime:se,onFinish:ae,onSelect:X,selectOne:K}}},D=t=>(ge("data-v-7f640630"),t=t(),he(),t),ye={class:"box"},be=D(()=>c("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/153529d0-3749-11ee-aed8-05e366306843_bg2.png",alt:""},null,-1)),Ie={class:"content"},Se=["onClick"],ke=["src"],xe=["src"],Ae={class:"last"},Ce=D(()=>c("img",{src:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_btn.png",alt:""},null,-1)),Ue=[Ce],Te=D(()=>c("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),qe=["src"];function Le(t,l,I,r,M,V){const S=q("false-data"),w=q("van-action-sheet"),k=q("van-overlay");return L(),O(F,null,[c("div",ye,[B(S),be,c("div",Ie,[(L(!0),O(F,null,me(t.urls,(d,u)=>(L(),O("div",{class:"content_box",key:u,onClick:g=>t.selectOne(d.id)},[c("img",{class:"content1",src:d.url,alt:""},null,8,ke),c("img",{class:"btn",src:d.isSelect?"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_selected.png":"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_no-btn.png"},null,8,xe)],8,Se))),128))]),c("div",Ae,[c("div",{class:"bottom",onClick:l[0]||(l[0]=(...d)=>t.getBtn&&t.getBtn(...d))},Ue)])]),B(w,{show:t.show,"onUpdate:show":l[1]||(l[1]=d=>t.show=d),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),B(k,{show:t.showImg,onClick:l[3]||(l[3]=d=>t.showImg=!1)},{default:ue(()=>[c("div",{class:"wrapper",onClick:l[2]||(l[2]=fe(()=>{},["stop"]))},[c("div",null,[Te,c("img",{src:t.qrCodeUrlImg,alt:""},null,8,qe)])])]),_:1},8,["show"])],64)}var $e=ce(_e,[["render",Le],["__scopeId","data-v-7f640630"]]);export{$e as default};
