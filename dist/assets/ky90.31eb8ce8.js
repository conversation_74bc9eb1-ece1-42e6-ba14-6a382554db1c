import{C as p,s as o}from"./js.cookie.ad72bcd1.js";import{b as l}from"./config618.d924d98a.js";import{_ as v,r as f,b as y,o as h,d as w,e as x,f as _,T as c,p as b,i as I}from"./index.8f4d7f86.js";const S={name:"ms12",components:{},setup:()=>{const t=f(!1),i=f(!1),s=y({info:{}});h(async()=>{document.title="90 \u5929\u79D1\u7814\u966A\u8DD1\u8425\uFF0C\u91CD\u78C5\u767B\u573A\uFF01",r({title:"90 \u5929\u79D1\u7814\u966A\u8DD1\u8425\uFF0C\u91CD\u78C5\u767B\u573A\uFF01",summary:"\u672C\u671F\u4EC5\u965020\u540D\uFF0C\u70B9\u51FB\u62A5\u540D\u5F00\u542F\u79D1\u7814\u98DE\u8DC3\u4E4B\u65C5",thumb:"https://static.medsci.cn/public-image/ms-image/17376047896144_90day_share.png"});const e=new Date("2025/12/31").getTime();new Date().getTime()-e>0&&(i.value=!0);const n=p.get("userInfo");n?(s.info=JSON.parse(n),m()):a(),o.get(l+"/medsci-activity/visit",{params:{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,event_type:"view",type:"research_carnival_90"}})});const r=async e=>{let d=window.location.href.split("#")[0];const n=await o.get("https://ypxcx.medsci.cn/ean/share",{params:{url:d}});wx.config(n.data),wx.error(function(g){console.log(g)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},m=async()=>{const e=await o.post(l+"/medsci-activity/attend-status",{mobile:s.info.mobile,type:"research_carnival_90"});e.data&&e.data.status==1?t.value=!0:t.value=!1},u=async()=>{if(!p.get("userInfo"))return a(),!1;if(i.value)return c("\u672C\u6B21\u6D3B\u52A8\u5DF2\u7ED3\u675F\uFF0C\u5982\u6709\u76F8\u5173\u670D\u52A1\u9700\u6C42\uFF0C\u8BF7\u62E8\u6253\u7535\u8BDD\u54A8\u8BE2\uFF1A400-0583-188\uFF0C\u4E5F\u53EF\u4EE5\u8054\u7CFB\u60A8\u7684\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u54A8\u8BE2~"),!1;if(t.value)c("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF0C\u8BF7\u7559\u610F\u7535\u8BDD\u4FE1\u606F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB");else{const e=await o.post(l+"/medsci-activity/research_carnival_90",{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,mobile:s.info.mobile,user_name:s.info.userName,real_name:s.info.realName,email:s.info.email});e.code==200&&(c.success("\u606D\u559C\u60A8\uFF0C\u62A5\u540D\u6210\u529F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB"),t.value=!0),e.code!=200&&e.code!=205&&c(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},a=()=>{addLoginDom()};return{userInfo:s,timing:i,wxShare:r,Login:a,joinStatus:t,joinActivity:u}}},k=t=>(b("data-v-587e9b08"),t=t(),I(),t),U={class:"box"},T=k(()=>_("img",{class:"img",src:"https://static.medsci.cn/public-image/ms-image/\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_17376036138809.png",alt:""},null,-1));function j(t,i,s,r,m,u){return w(),x("div",U,[T,_("img",{class:"btn",onClick:i[0]||(i[0]=(...a)=>t.joinActivity&&t.joinActivity(...a)),src:"https://static.medsci.cn/public-image/ms-image/17376047896144_90day_btn.png"})])}var $=v(S,[["render",j],["__scopeId","data-v-587e9b08"]]);export{$ as default};
