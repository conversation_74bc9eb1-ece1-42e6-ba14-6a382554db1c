import{_ as re,u as le,a as de,r as n,b as F,o as pe,t as me,c as q,d as L,e as T,f as o,g as B,F as J,E as ue,w as fe,T as u,h as ge,p as we,i as he}from"./index.8f4d7f86.js";import{C as H,s as c}from"./js.cookie.ad72bcd1.js";import{f as ve}from"./falseData.c0306b2a.js";import{m as _e}from"./msSwiper.7d61aaec.js";import{b as _}from"./config.3aca39f6.js";import{b as y}from"./configFreeVip.d32e6c36.js";import{m as ye}from"./sanofiConfig.a80970f2.js";const be={name:"centVip",components:{falseData:ve,msSwiper:_e},setup:()=>{le();const t=de(),r=n(!1),b=n([{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_lessons.png",id:"1",isSelect:!0},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_guide.png",id:"2",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_journal.png",id:"3",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_fund.png",id:"4",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_breath.png",id:"5",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_neurology.png",id:"6",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_cardio.png",id:"7",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/a36ee5e0-3741-11ee-aed8-05e366306843_pifuke.png",id:"8",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_eye.png",id:"9",isSelect:!1}]),l=n("1"),O=n(""),M=n(!1),I=n(!1),h=n(!1),S=n(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),d=n(!1),m=n(""),f=n(""),V=n(""),E=n(""),k=n(!1),x=n(!1),$=n(""),C=n("college"),P=n(!1),X=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],Q=e=>{k.value=!1,j(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},i=F({info:{}}),G=F({msg:{}}),R=async()=>{(await c.get(ye+"/perfectInfo/userInfoStatus?encryptionUserId="+i.info.userId)).data.isCompleteInfo?A():addPerfectInfoDom()},U=e=>{for(var s=window.location.search.substring(1),a=s.split("&"),p=0;p<a.length;p++){var g=a[p].split("=");if(g[0]==e)return g[1]}return!1};pe(async()=>{document.title="\u65B0\u6CE8\u518C\u7528\u6237\u597D\u793C",W({title:"\u65B0\u6CE8\u518C\u7528\u6237\u597D\u793C",summary:"\u4EC5\u533B\u62A4\u4EBA\u5458\uFF0C\u6BCF\u4E2A\u8D26\u53F7\u9650\u9886\u53D6\u4E00\u6B21",thumb:"https://static.medsci.cn/public-image/ms-image/78b03b20-4d4d-11ee-abff-45aa74f03fdd_300x300.png"}),H.get("userInfo"),c.get(y+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"new_user_register"}})});const W=async e=>{let s=window.location.href.split("#")[0];const a=await c.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(a.data),wx.error(function(p){console.log(p)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVip",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVip",imgUrl:e.thumb,success:function(){}})})},K=()=>{const e=navigator.userAgent,s=t.query.openId||U("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){m.value="WX";const a="wx9096048917ec59ab";if(s)E.value=s;else{const p=encodeURIComponent(window.location.href),g=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${p}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${g}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?m.value="ALI":m.value="ALL"},Y=async e=>{l.value=e,b.value.forEach(s=>{l.value==s.id?s.isSelect=!0:s.isSelect=!1})},Z=async()=>{K(),u.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=H.get("userInfo");if(e)i.info=JSON.parse(e),i.info.mobile?R():addLoginDom();else{const s=t.query.sso_sessionid||U("sso_sessionid");if(s){const a=await c.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});i.info=a.data,A()}else N()}},N=()=>{addLoginDom()},ee=e=>{e.link&&(window.location.href=e.link)},A=async()=>{new Date().getTime(),new Date("2023/08/19 00:00:00").getTime(),new Date("2023/08/19 23:59:59").getTime();const e=await c.post(y+"/medsci-activity/attend-status",{order_id:f.value,type:"new_user_register",mobile:i.info.mobile});if(e.data.status==0){let s={1:"scientific_research",2:"guider",3:"journal",4:"nsfc",5:"breathe",6:"nerve",7:"cardiovascular",8:"skin",9:"ophthalmology"};const a=await c.post(y+"/medsci-activity/pay/member-card/"+s[l.value],{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,type:"new_user_register"});a.code!==200&&a.code!==205&&(u.clear(),u(a.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),f.value=a.data.data,u.clear(),ie()}else(e.data.status==1||res.code==205)&&u("\u60A8\u5DF2\u7ECF\u9886\u8FC7\u4E86\u54E6\uFF01")},se=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},te=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},ae=()=>{h.value=!0},ne=()=>{P.value=!0},ie=async()=>{if(window.innerWidth<750)m.value=="ALL"?k.value=!0:j(m.value);else{const e=await c.post(_+"/payment/pay/merge_qrcode",{accessAppId:C.value,appOrderId:f.value}),{qrCodeUrl:s}=e.data;s&&(x.value=!0,$.value=s);const a=setInterval(()=>{ce(),V.value=="PAID"&&(x.value=!1,oe(),clearInterval(a))},3e3)}d.value=!1},oe=async()=>{const e=await c.post(y+"/medsci-activity/pay/member-card-status",{order_id:f.value,type:"new_user_register"});console.log(e,"\u4E0B\u5355\u6210\u529F")},ce=async()=>{const e=await c.get(_+"/payment/pay/query",{params:{appOrderId:f.value}}),{paymentStatus:s}=e.data;V.value=s,s=="PAID"&&(u("\u652F\u4ED8\u6210\u529F"),console.log(l),l.value=="3"?window.location.href="https://www.medsci.cn/sci/index.do":l.value=="2"?window.location.href="https://www.medsci.cn/guideline/index.do":l.value=="4"?window.location.href="https://www.medsci.cn/sci/nsfc.do?utm_campaign":["1","5","6","7","8","9"].includes(l.value)&&(window.location.href="https://class.medsci.cn/"))},j=async e=>{const s=await c.post(_+"/payment/pay/build",{accessAppId:C.value,appOrderId:f.value,payChannel:e,paySource:"MEDSCI_WEB",payType:m.value=="ALL"?"MWEB":m.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){u(s.msg);return}const a=await c.post(_+"/payment/pay/order",{accessAppId:C.value,payOrderId:s.data.payOrderId,openId:E.value}),{aliH5:p,aliQR:g,wechatH5:z,wechatJsapi:w}=a.data;if(p){const v=document.createElement("div");v.innerHTML=p.html,document.body.appendChild(v),document.forms[0].submit()}g&&(window.location.href=g.payUrl),z&&(window.location.href=z.h5Url),w&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:w.appId,timeStamp:w.timeStamp,nonceStr:w.nonceStr,package:w.packageStr,signType:w.signType,paySign:w.paySign},function(v){v.err_msg=="get_brand_wcpay_request:ok"&&u.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{curVal:l,urls:b,...me(G),loading:M,userInfo:i,active:I,guize:h,time:S,vipType:O,actions:X,show:k,showImg:x,qrCodeUrlImg:$,isEnd:P,timing:r,getBtn:Z,Login:N,Pay:A,testPlay:ee,getQueryVariable:U,wxShare:W,link:se,showGuize:ae,formatTime:te,onFinish:ne,onSelect:Q,selectOne:Y,isLimitComplete:R}}},D=t=>(we("data-v-96db1924"),t=t(),he(),t),Ie={class:"box"},Se=D(()=>o("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/0252a380-4d40-11ee-abff-45aa74f03fdd_\u6CE8\u518C\u7528\u6237\u597D\u793C\u5E95\u56FE.png",alt:""},null,-1)),ke={class:"content"},xe=["onClick"],Ce=["src"],Ue=["src"],Ae={class:"last"},qe=D(()=>o("img",{src:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_btn.png",alt:""},null,-1)),Le=[qe],Te=D(()=>o("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Be=["src"];function De(t,r,b,l,O,M){const I=q("false-data"),h=q("van-action-sheet"),S=q("van-overlay");return L(),T(J,null,[o("div",Ie,[B(I),Se,o("div",ke,[(L(!0),T(J,null,ue(t.urls,(d,m)=>(L(),T("div",{class:"content_box",key:m,onClick:f=>t.selectOne(d.id)},[o("img",{class:"content1",src:d.url,alt:""},null,8,Ce),o("img",{class:"btn",src:d.isSelect?"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_selected.png":"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_no-btn.png"},null,8,Ue)],8,xe))),128))]),o("div",Ae,[o("div",{class:"bottom",onClick:r[0]||(r[0]=(...d)=>t.getBtn&&t.getBtn(...d))},Le)])]),B(h,{show:t.show,"onUpdate:show":r[1]||(r[1]=d=>t.show=d),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),B(S,{show:t.showImg,onClick:r[3]||(r[3]=d=>t.showImg=!1)},{default:fe(()=>[o("div",{class:"wrapper",onClick:r[2]||(r[2]=ge(()=>{},["stop"]))},[o("div",null,[Te,o("img",{src:t.qrCodeUrlImg,alt:""},null,8,Be)])])]),_:1},8,["show"])],64)}var We=re(be,[["render",De],["__scopeId","data-v-96db1924"]]);export{We as default};
