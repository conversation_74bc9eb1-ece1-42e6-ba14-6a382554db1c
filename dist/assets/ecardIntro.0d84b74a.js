import{_ as Y,r as s,u as Z,a as ee,b as A,o as se,t as te,c as oe,d as ae,e as ne,f as i,g as ie,w as re,F as ce,T as r,h as le,p as de,i as pe}from"./index.8f4d7f86.js";import{C as me,s as m}from"./js.cookie.ad72bcd1.js";import{f as ue}from"./falseData.c0306b2a.js";import{m as fe}from"./msSwiper.7d61aaec.js";import{b as U}from"./config.3aca39f6.js";import{b as D}from"./configFreeVip.d32e6c36.js";import{m as ge}from"./sanofiConfig.a80970f2.js";import{_ as we,a as _e,b as ve}from"./close.3afbe22b.js";const ye={name:"openCourse",components:{falseData:ue,msSwiper:fe},setup:()=>{const t=s(""),o=s(""),u=s(<PERSON><PERSON>an);Z(),ee();const h=s(!1),I=s("1"),b=s(""),_=s(!1),l=s(!1),S=s(!1),q=s(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime());s(!1);const k=s(""),E=s("");s("");const L=s(""),C=s(!0),V=s(!1),M=s(""),T=s("college"),B=s(!1),v=s(!1),W=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],O=e=>{C.value=!1,K(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},n=A({info:{}}),P=A({msg:{}}),N=e=>{for(var a=window.location.search.substring(1),f=a.split("&"),d=0;d<f.length;d++){var p=f[d].split("=");if(p[0]==e)return p[1]}return!1};se(async()=>{window.innerWidth<768?u.value=!0:u.value=!1,document.title="\u79D1\u7814\u652F\u6301\u670D\u52A1\u4ECB\u7ECD",me.get("userInfo"),m.get(D+"/medsci-activity/visit",{params:{user_id:n.info.plaintextUserId,ciphertext_user_id:n.info.userId,event_type:"view",type:"new_user_register"}})});const R=async()=>{(await m.get(ge+"/perfectInfo/userInfoStatus?encryptionUserId="+n.info.userId)).data.isCompleteInfo?y():addPerfectInfoDom()},y=async()=>{v.value=!0},$=async()=>{v.value=!1},F=async()=>{y()},H=()=>{addLoginDom()},J=e=>{e.link&&(window.location.href=e.link)},j=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const e=await m.post(D+"/medsci-activity/give/member-card",{user_id:n.info.plaintextUserId,ciphertext_user_id:n.info.userId,mobile:n.info.mobile,user_name:n.info.userName,real_name:n.info.realName,type:"breathing_give"});e.code!==200&&e.code!==205&&(r.clear(),r(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),e.code==200&&(r.clear(),r("\u606D\u559C\u60A8\u5DF2\u6210\u529F\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0"),e.code==205&&(r.clear(),r("\u60A8\u5DF2\u7ECF\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458\uFF0C\u76F4\u63A5\u5B66\u4E60\u5373\u53EF"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0")},z=()=>{var e=navigator.userAgent;const a=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},Q=e=>{if(e>=10){const a=String(e);return[a[0],a[1]]}else return[0,e]},X=()=>{S.value=!0},G=()=>{B.value=!0},K=async e=>{const a=await m.post(U+"/payment/pay/build",{accessAppId:T.value,appOrderId:E.value,payChannel:e,paySource:"MEDSCI_WEB",payType:k.value=="ALL"?"MWEB":k.value=="WX"?"JSAPI":"NATIVE"});if(a.code!="SUCCESS"){r(a.msg);return}const f=await m.post(U+"/payment/pay/order",{accessAppId:T.value,payOrderId:a.data.payOrderId,openId:L.value}),{aliH5:d,aliQR:p,wechatH5:x,wechatJsapi:c}=f.data;if(d){const g=document.createElement("div");g.innerHTML=d.html,document.body.appendChild(g),document.forms[0].submit()}p&&(window.location.href=p.payUrl),x&&(window.location.href=x.h5Url),c&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:c.appId,timeStamp:c.timeStamp,nonceStr:c.nonceStr,package:c.packageStr,signType:c.signType,paySign:c.paySign},function(g){g.err_msg=="get_brand_wcpay_request:ok"&&(r.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://open.medsci.cn/")})};return{curVal:I,...te(P),loading:_,userInfo:n,active:l,guize:S,time:q,vipType:b,actions:W,show:C,showImg:V,qrCodeUrlImg:M,isEnd:B,timing:h,dialogVisible:v,isMobile:u,url:t,type:o,getBtn:F,Login:H,Pay:j,testPlay:J,getQueryVariable:N,link:z,showGuize:X,formatTime:Q,onFinish:G,onSelect:O,isLimitComplete:R,getBtn1:y,close:$}}},w=t=>(de("data-v-406e372c"),t=t(),pe(),t),he={class:"box"},Ie=w(()=>i("img",{class:"img",src:"https://static.medsci.cn/public-image/ms-image/d3466d10-97ef-11ee-926a-11c8565c20b2_introduce.png",alt:""},null,-1)),be=w(()=>i("img",{style:{width:"100%"},src:"https://static.medsci.cn/public-image/ms-image/56141d70-97f3-11ee-926a-11c8565c20b2_btn1.png",alt:""},null,-1)),Se=[be],ke={class:"block",style:{width:"8.8rem",position:"relative"}},Ce=w(()=>i("img",{style:{width:"100%",height:"100%"},src:_e,alt:""},null,-1)),Te=w(()=>i("img",{style:{width:"50%",position:"absolute",bottom:"31%",left:"24.8%"},src:ve,alt:""},null,-1));function Be(t,o,u,h,I,b){const _=oe("van-overlay");return ae(),ne(ce,null,[i("div",he,[Ie,i("div",{class:"bottom",onClick:o[0]||(o[0]=(...l)=>t.getBtn&&t.getBtn(...l))},Se)]),ie(_,{show:t.dialogVisible,onClick:o[3]||(o[3]=l=>t.show=!1)},{default:re(()=>[i("div",{class:"wrapper",onClick:o[2]||(o[2]=le(()=>{},["stop"])),style:{display:"flex","flex-direction":"column"}},[i("div",ke,[Ce,Te,i("img",{onClick:o[1]||(o[1]=(...l)=>t.close&&t.close(...l)),style:{width:"8%",position:"absolute",bottom:"-9%",left:"46%"},src:we,alt:""})])])]),_:1},8,["show"])],64)}var Me=Y(ye,[["render",Be],["__scopeId","data-v-406e372c"]]);export{Me as default};
