import{_ as ne,u as ae,a as ie,r as c,b as V,o as ce,t as le,c as j,d as l,e as r,f as t,n as re,g as P,w as D,F as I,T as f,E as A,B as U,h as de,p as pe,i as ue}from"./index.8f4d7f86.js";import{C as B,s as g}from"./js.cookie.ad72bcd1.js";import{m as me}from"./msSwiper.7d61aaec.js";import{a as fe}from"./configkap.935700b6.js";import{b as O}from"./config.3aca39f6.js";import{b as R}from"./config618.d924d98a.js";const ve={name:"doubleEleven",components:{msSwiper:me},setup:()=>{ae();const s=ie(),p=c(!1),W=c(!1),$=c(!1),h=c(""),k=c(""),C=c(""),T=c(""),S=c(!1),a=c(!1),u=c(""),d=c("college"),q=c(!1),H=c(new Date("2022/11/30 23:59:59").getTime()-new Date().getTime()),X=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],Q=e=>{S.value=!1,J(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},i=V({info:{}}),b=V({msg:{}}),w=c(!1),E=e=>{for(var o=window.location.search.substring(1),m=o.split("&"),n=0;n<m.length;n++){var y=m[n].split("=");if(y[0]==e)return y[1]}return!1};ce(async()=>{document.title="11.11\u8D85\u503C\u76DB\u5178 \u2022 \u8BED\u8A00\u670D\u52A1\u591A\u4E70\u591A\u4F18\u60E0\uFF01",F({title:"11.11\u8D85\u503C\u76DB\u5178 \u2022 \u8BED\u8A00\u670D\u52A1\u591A\u4E70\u591A\u4F18\u60E0\uFF01",summary:"\u4F18\u8D28\u670D\u52A1\uFF0C\u4E3A\u60A8\u7701\u5FC3\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/4d061f80-5c1c-11ed-b66b-937b834e3ef9_share.png"});const e=B.get("userInfo");e&&(i.info=JSON.parse(e),z()),g.get(R+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"double_eleven"}})});const F=async e=>{let o=window.location.href.split("#")[0];const m=await g.get("https://ypxcx.medsci.cn/ean/share",{params:{url:o}});wx.config(m.data),wx.error(function(n){console.log(n)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},z=async()=>{const e=await g.post(R+"/medsci-activity/attend-status",{mobile:i.info.mobile,type:"double_eleven"});e.data&&e.data.status==1?w.value=!0:w.value=!1},K=async()=>{if(console.log(w.value,"rrr"),q.value)return f("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;if(!B.get("userInfo"))return x(),!1;if(!w.value){const e=await g.post(R+"/medsci-activity/double_eleven",{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,email:i.info.email});e.code==200&&(f.success("\u62A5\u540D\u6210\u529F"),w.value=!0),e.code==205&&(f("\u60A8\u5DF2\u62A5\u540D\uFF0C\u7B49\u5F85\u56DE\u8BBF"),w.value=!0),e.code!=200&&e.code!=205&&f(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},G=async()=>{if(q.value)return f("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;const e=navigator.userAgent,o=s.query.openId||E("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){h.value="WX";const n="wx9096048917ec59ab";if(o)T.value=o;else{const y=encodeURIComponent(window.location.href),L=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${y}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${L}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;return}}else e!=null&&e.indexOf("AlipayClient")>-1?h.value="ALI":h.value="ALL";const m=B.get("userInfo");if(f.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),m)i.info=JSON.parse(m),i.info.mobile||addLoginDom(),M();else{const n=s.query.sso_sessionid||E("sso_sessionid");if(n){const y=await g.post("/medsciUser/getLoginUserInfoBySid",{sessionid:n});i.info=y.data,M()}else x()}},x=()=>{addLoginDom()},Y=e=>{e.link&&(window.location.href=e.link)},M=async()=>{const{userId:e,userName:o,realName:m,mobile:n,email:y,plaintextUserId:L}=i.info,v=await g.post(fe+"/openOrder/addActivityOrder",{activityId:b.msg.id,itemNum:1,itemPicPath:"",activityName:b.msg.name,itemPrice:b.msg.money,projectId:1,orderType:b.msg.type,mobile:n,payment:b.msg.money,userId:e,nikeName:o,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});v.status==200?(k.value=v.data,f.clear(),ee()):f(v.message)},Z=()=>{var e=navigator.userAgent;const o=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},ee=async()=>{if(window.innerWidth<750)h.value=="ALL"?S.value=!0:J(h.value);else{const e=await g.post(O+"/payment/pay/merge_qrcode",{accessAppId:d.value,appOrderId:k.value}),{qrCodeUrl:o}=e.data;o&&(a.value=!0,u.value=o);const m=setInterval(()=>{se(),C.value=="PAID"&&clearInterval(m)},3e3)}$.value=!1},se=async()=>{const e=await g.get(O+"/payment/pay/query",{params:{appOrderId:k.value}}),{paymentStatus:o}=e.data;C.value=o,o=="PAID"&&f("\u652F\u4ED8\u6210\u529F")},J=async e=>{const o=await g.post(O+"/payment/pay/build",{accessAppId:d.value,appOrderId:k.value,payChannel:e,paySource:"MEDSCI_WEB",payType:h.value=="ALL"?"MWEB":h.value=="WX"?"JSAPI":"NATIVE"});if(o.code!="SUCCESS"){f(o.msg);return}const m=await g.post(O+"/payment/pay/order",{accessAppId:d.value,payOrderId:o.data.payOrderId,openId:T.value}),{aliH5:n,aliQR:y,wechatH5:L,wechatJsapi:v}=m.data;if(n){const N=document.createElement("div");N.innerHTML=n.html,document.body.appendChild(N),document.forms[0].submit()}y&&(window.location.href=y.payUrl),L&&f.fail("\u7528\u5FAE\u4FE1\u7AEF\u6253\u5F00\u8FDB\u884C\u652F\u4ED8"),v&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:v.appId,timeStamp:v.timeStamp,nonceStr:v.nonceStr,package:v.packageStr,signType:v.signType,paySign:v.paySign},function(N){N.err_msg=="get_brand_wcpay_request:ok"&&f.success("\u652F\u4ED8\u6210\u529F\uFF01")})},te=e=>{if(e>=10){const o=String(e);return[o[0],o[1]]}else return[0,e]},oe=()=>{q.value=!0};return{...le(b),loading:p,userInfo:i,active:W,actions:X,show:S,showImg:a,qrCodeUrlImg:u,isEnd:q,Login:x,buy:G,Pay:M,testPlay:Y,getQueryVariable:E,wxShare:F,link:Z,onSelect:Q,formatTime:te,onFinish:oe,time:H,joinActivity:K,joinStatus:w}}},_=s=>(pe("data-v-1cf9a016"),s=s(),ue(),s),ye={class:"box"},ge=_(()=>t("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/6df7eb50-6014-11ed-b66b-937b834e3ef9_112.png",alt:""},null,-1)),_e={class:"last"},he={class:"top"},we=_(()=>t("img",{class:"img",src:"https://static.medsci.cn/public-image/ms-image/d3c76a80-5c1a-11ed-b66b-937b834e3ef9_count_icon.png",alt:""},null,-1)),be={class:"block"},Ie=_(()=>t("span",{class:"colon a"},"\u5929",-1)),ke={class:"block"},Se=_(()=>t("span",{class:"colon a"},"\u65F6",-1)),Ae={class:"block"},Ue=_(()=>t("span",{class:"colon a"},"\u5206",-1)),Ce={class:"block"},Te=_(()=>t("span",{class:"colon a"},"\u79D2",-1)),qe={class:"block"},Le=_(()=>t("span",{class:"colon"},[t("div",{class:"mill"},"\u6BEB"),t("div",{class:"mill"},"\u79D2")],-1)),Ne={class:"bottom"},Oe=_(()=>t("div",{class:"bottom-left"},[t("div",{class:"Num"}," \u62A5\u540D\u65F6\u95F4\uFF1A11.9-11.30 ")],-1)),Ee={key:1,class:"bottom-right disable"},xe={class:"wrapper"},Me=_(()=>t("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),je=["src"];function Pe(s,p,W,$,h,k){const C=j("van-count-down"),T=j("van-action-sheet"),S=j("van-overlay");return l(),r(I,null,[t("div",ye,[ge,s.joinStatus?re("",!0):(l(),r("div",{key:0,class:"wrap",onClick:p[0]||(p[0]=(...a)=>s.joinActivity&&s.joinActivity(...a))})),t("div",_e,[t("div",he,[we,P(C,{millisecond:"",time:s.time,onFinish:s.onFinish},{default:D(a=>[t("div",be,[(l(!0),r(I,null,A(s.formatTime(a.days),(u,d)=>(l(),r("div",{class:"block-item",key:d},U(u),1))),128))]),Ie,t("div",ke,[(l(!0),r(I,null,A(s.formatTime(a.hours),(u,d)=>(l(),r("div",{class:"block-item",key:d},U(u),1))),128))]),Se,t("div",Ae,[(l(!0),r(I,null,A(s.formatTime(a.minutes),(u,d)=>(l(),r("div",{class:"block-item",key:d},U(u),1))),128))]),Ue,t("div",Ce,[(l(!0),r(I,null,A(s.formatTime(a.seconds),(u,d)=>(l(),r("div",{class:"block-item",key:d},U(u),1))),128))]),Te,t("div",qe,[(l(!0),r(I,null,A(s.formatTime(a.milliseconds),(u,d)=>(l(),r("div",{class:"block-item",key:d},U(u),1))),128))]),Le]),_:1},8,["time","onFinish"])]),t("div",Ne,[Oe,s.joinStatus?(l(),r("div",Ee," \u60A8\u5DF2\u53C2\u4E0E ")):(l(),r("div",{key:0,class:"bottom-right",onClick:p[1]||(p[1]=(...a)=>s.joinActivity&&s.joinActivity(...a))}," \u70B9\u51FB\u53C2\u4E0E "))])])]),P(T,{show:s.show,"onUpdate:show":p[2]||(p[2]=a=>s.show=a),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),P(S,{show:s.showImg,onClick:p[4]||(p[4]=a=>s.showImg=!1)},{default:D(()=>[t("div",xe,[t("div",{onClick:p[3]||(p[3]=de(()=>{},["stop"]))},[Me,t("img",{src:s.qrCodeUrlImg,alt:""},null,8,je)])])]),_:1},8,["show"])],64)}var Ve=ne(ve,[["render",Pe],["__scopeId","data-v-1cf9a016"]]);export{Ve as default};
