import{_ as K,u as Y,a as Z,r as n,b as N,o as ee,T as v,t as te,c as q,d as se,e as ae,f as d,g as L,w as ne,F as oe,h as ie,p as ce,i as re}from"./index.8f4d7f86.js";import{s as m,C as le}from"./js.cookie.ad72bcd1.js";import{f as de}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b as I}from"./config.3aca39f6.js";const me={name:"jijin",components:{falseData:de,msSwiper:pe},setup:()=>{Y();const s=Z(),c=n(!1),M=n(!1),b=n(!1),y=n(new Date("2032/03/16 14:00:00").getTime()-new Date().getTime()),w=n(""),S=n(!1),r=n(""),g=n(""),u=n(""),E=n(""),A=n(!1),O=n(!1),P=n(""),k=n("college"),x=n(!1),W=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],$=e=>{A.value=!1,B(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},f=N({info:{}}),o=N({msg:{}}),h=e=>{for(var t=window.location.search.substring(1),a=t.split("&"),i=0;i<a.length;i++){var p=a[i].split("=");if(p[0]==e)return p[1]}return!1};ee(async()=>{let e;s.query.day==30?(document.title="\u57FA\u91D1\u4F1A\u5458\u6708\u5361",T({title:"\u57FA\u91D1\u4F1A\u5458\u6708\u5361",summary:"\u6885\u65AF\u533B\u5B66*\u57FA\u91D1\u4F1A\u5458 \u5168\u7403\u57FA\u91D1\u9879\u76EE\u6570\u636E\u5E93\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/6aad1690-f0a2-11ed-9b52-b908d10125b2_\u57FA\u91D1\u4F1A\u5458\u5C0F\u56FE.png"}),w.value="https://static.medsci.cn/public-image/ms-image/9022a070-f0a2-11ed-9b52-b908d10125b2_\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_16838793357720.png",e="****************"):(document.title="\u57FA\u91D1\u4F1A\u5458",T({title:"\u57FA\u91D1\u4F1A\u5458",summary:"\u6885\u65AF\u533B\u5B66*\u57FA\u91D1\u4F1A\u5458 \u5168\u7403\u57FA\u91D1\u9879\u76EE\u6570\u636E\u5E93\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/6aad1690-f0a2-11ed-9b52-b908d10125b2_\u57FA\u91D1\u4F1A\u5458\u5C0F\u56FE.png"}),w.value="https://static.medsci.cn/public-image/ms-image/9022a070-f0a2-11ed-9b52-b908d10125b2_\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_16838793357720.png",e="****************");const t=await m.post("/activity/memberCardDetail",{id:e});o.msg=t.data,o.msg&&o.msg.activityEndTime&&(o.msg.activityEndTime=o.msg.activityEndTime.replace(/-/g,"/"),y.value=new Date(o.msg.activityEndTime).getTime()-new Date().getTime()),y.value<0&&(x.value=!0);let a=navigator.userAgent;a!=null&&a.indexOf("MicroMessenger")>-1&&(s.query.openId||h("openId"))&&D()});const T=async e=>{let t=window.location.href.split("#")[0];const a=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(a.data),wx.error(function(i){console.log(i)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/yk",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/yk",imgUrl:e.thumb,success:function(){}})})},z=()=>{const e=navigator.userAgent,t=s.query.openId||h("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){r.value="WX";const a="wx9096048917ec59ab";if(t)E.value=t;else{const i=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${i}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?r.value="ALI":r.value="ALL"},D=async()=>{const e=le.get("userInfo");if(e&&z(),v.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)f.info=JSON.parse(e),f.info.mobile||addLoginDom(),C();else{const t=s.query.sso_sessionid||h("sso_sessionid");if(t){const a=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});f.info=a.data,C()}else j()}},j=()=>{addLoginDom()},J=e=>{e.link&&(window.location.href=e.link)},C=async()=>{const{userId:e,userName:t,realName:a,mobile:i,email:p,plaintextUserId:U}=f.info,l=await m.post("/activity/createOrder",{itemId:o.msg.id,itemNum:1,itemPicPath:o.msg.cardImage,itemTitle:o.msg.cardName,itemPrice:o.msg.activityPrice,projectId:o.msg.projectId,orderType:1,mobile:i,payment:o.msg.activityPrice,userId:e,nikeName:t,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});g.value=l.data,v.clear(),Q()},V=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},F=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},H=()=>{b.value=!0},X=()=>{x.value=!0},Q=async()=>{if(window.innerWidth<750)r.value=="ALL"?A.value=!0:B(r.value);else{const e=await m.post(I+"/payment/pay/merge_qrcode",{accessAppId:k.value,appOrderId:g.value}),{qrCodeUrl:t}=e.data;t&&(O.value=!0,P.value=t);const a=setInterval(()=>{G(),u.value=="PAID"&&clearInterval(a)},3e3)}S.value=!1},G=async()=>{const e=await m.get(I+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:t}=e.data;u.value=t,t=="PAID"&&v("\u652F\u4ED8\u6210\u529F")},B=async e=>{const t=await m.post(I+"/payment/pay/build",{accessAppId:k.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:r.value=="ALL"?"MWEB":r.value=="WX"?"JSAPI":"NATIVE"});if(t.code!="SUCCESS"){v(t.msg);return}const a=await m.post(I+"/payment/pay/order",{accessAppId:k.value,payOrderId:t.data.payOrderId,openId:E.value}),{aliH5:i,aliQR:p,wechatH5:U,wechatJsapi:l}=a.data;if(i){const _=document.createElement("div");_.innerHTML=i.html,document.body.appendChild(_),document.forms[0].submit()}p&&(window.location.href=p.payUrl),U&&(window.location.href=U.h5Url),l&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:l.appId,timeStamp:l.timeStamp,nonceStr:l.nonceStr,package:l.packageStr,signType:l.signType,paySign:l.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&v.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...te(o),loading:c,userInfo:f,active:M,guize:b,time:y,actions:W,show:A,showImg:O,qrCodeUrlImg:P,isEnd:x,url:w,Login:j,buy:D,Pay:C,testPlay:J,getQueryVariable:h,wxShare:T,link:V,showGuize:H,formatTime:F,onFinish:X,onSelect:$}}},R=s=>(ce("data-v-9fc1a62a"),s=s(),re(),s),ue={class:"box"},ge=["src"],fe={class:"last"},ve=R(()=>d("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/4910bd50-f0a4-11ed-9b52-b908d10125b2_\u6309\u94AE(2).png",alt:""},null,-1)),ye=[ve],we={class:"wrapper"},he=R(()=>d("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),_e=["src"];function Ie(s,c,M,b,y,w){const S=q("false-data"),r=q("van-action-sheet"),g=q("van-overlay");return se(),ae(oe,null,[d("div",ue,[L(S),d("img",{class:"image",src:s.url,alt:""},null,8,ge),d("div",fe,[d("div",{class:"bottom",onClick:c[0]||(c[0]=(...u)=>s.buy&&s.buy(...u))},ye)])]),L(r,{show:s.show,"onUpdate:show":c[1]||(c[1]=u=>s.show=u),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),L(g,{show:s.showImg,onClick:c[3]||(c[3]=u=>s.showImg=!1)},{default:ne(()=>[d("div",we,[d("div",{onClick:c[2]||(c[2]=ie(()=>{},["stop"]))},[he,d("img",{src:s.qrCodeUrlImg,alt:""},null,8,_e)])])]),_:1},8,["show"])],64)}var Te=K(me,[["render",Ie],["__scopeId","data-v-9fc1a62a"]]);export{Te as default};
