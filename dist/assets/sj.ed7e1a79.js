import{_ as K,u as Y,a as Z,r as n,b as N,o as ee,T as v,t as te,c as q,d as se,e as ae,f as l,g as L,w as ne,F as oe,h as ie,p as ce,i as re}from"./index.8f4d7f86.js";import{s as m,C as de}from"./js.cookie.ad72bcd1.js";import{f as le}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b as _}from"./config.3aca39f6.js";const me={name:"Home",components:{falseData:le,msSwiper:pe},setup:()=>{Y();const s=Z(),c=n(!1),M=n(!1),b=n(!1),y=n(new Date("2032/03/16 14:00:00").getTime()-new Date().getTime()),E=n(!1),r=n(""),f=n(""),w=n(""),u=n(""),S=n(!1),O=n(!1),P=n(""),A=n("college"),k=n(!1),x=n(""),W=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],$=e=>{S.value=!1,B(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},g=N({info:{}}),o=N({msg:{}}),h=e=>{for(var t=window.location.search.substring(1),a=t.split("&"),i=0;i<a.length;i++){var p=a[i].split("=");if(p[0]==e)return p[1]}return!1};ee(async()=>{let e;s.query.day==30?(document.title="\u795E\u7ECF\u79D1\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",T({title:"\u795E\u7ECF\u79D1\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",summary:"\u5927\u5496\u8BB2\u5EA7\u4E91\u96C6\uFF0C\u4E13\u6CE8\u5B9E\u6218\u4E34\u5E8A\u6280\u5DE7\uFF0C\u6D77\u91CF\u75C5\u4F8B\u5206\u6790\u7B49\u4F60\u5B66\u4E60",thumb:"https://static.medsci.cn/public-image/ms-image/a8339640-cc4b-11ed-b4b6-4d1e60dcd7df_750-750-\u795E\u7ECF(1).jpg"}),x.value="https://static.medsci.cn/public-image/ms-image/e5bf7b50-f0bd-11ed-9b52-b908d10125b2_0db37d8b-0c0c-4401-bdd3-bfedcbe7b7c5(1).jpg",e="****************"):(document.title="\u795E\u7ECF\u79D1\u7CBE\u54C1\u8BFE\u4F1A\u5458",T({title:"\u795E\u7ECF\u79D1\u7CBE\u54C1\u8BFE\u4F1A\u5458",summary:"\u5927\u5496\u8BB2\u5EA7\u4E91\u96C6\uFF0C\u4E13\u6CE8\u5B9E\u6218\u4E34\u5E8A\u6280\u5DE7\uFF0C\u6D77\u91CF\u75C5\u4F8B\u5206\u6790\u7B49\u4F60\u5B66\u4E60",thumb:"https://static.medsci.cn/public-image/ms-image/a8339640-cc4b-11ed-b4b6-4d1e60dcd7df_750-750-\u795E\u7ECF(1).jpg"}),x.value="https://static.medsci.cn/public-image/ms-image/202501101032_sj.png",e="****************");const t=await m.post("/activity/memberCardDetail",{id:e});o.msg=t.data,o.msg&&o.msg.activityEndTime&&(o.msg.activityEndTime=o.msg.activityEndTime.replace(/-/g,"/"),y.value=new Date(o.msg.activityEndTime).getTime()-new Date().getTime()),y.value<0&&(k.value=!0);let a=navigator.userAgent;a!=null&&a.indexOf("MicroMessenger")>-1&&(s.query.openId||h("openId"))&&j()});const T=async e=>{let t=window.location.href.split("#")[0];const a=await m.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(a.data),wx.error(function(i){console.log(i)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/sj",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/sj",imgUrl:e.thumb,success:function(){}})})},z=()=>{const e=navigator.userAgent,t=s.query.openId||h("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){r.value="WX";const a="wx9096048917ec59ab";if(t)u.value=t;else{const i=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${i}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?r.value="ALI":r.value="ALL"},j=async()=>{z();const e=de.get("userInfo");if(v.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)g.info=JSON.parse(e),g.info.mobile||addLoginDom(),C();else{const t=s.query.sso_sessionid||h("sso_sessionid");if(t){const a=await m.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});g.info=a.data,C()}else D()}},D=()=>{addLoginDom()},H=e=>{e.link&&(window.location.href=e.link)},C=async()=>{const{userId:e,userName:t,realName:a,mobile:i,email:p,plaintextUserId:U}=g.info,d=await m.post("/activity/createOrder",{itemId:o.msg.id,itemNum:1,itemPicPath:o.msg.cardImage,itemTitle:o.msg.cardName,itemPrice:o.msg.activityPrice,projectId:o.msg.projectId,orderType:1,mobile:i,payment:o.msg.activityPrice,userId:e,nikeName:t,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});f.value=d.data,v.clear(),Q()},J=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},V=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},F=()=>{b.value=!0},X=()=>{k.value=!0},Q=async()=>{if(window.innerWidth<750)r.value=="ALL"?S.value=!0:B(r.value);else{const e=await m.post(_+"/payment/pay/merge_qrcode",{accessAppId:A.value,appOrderId:f.value}),{qrCodeUrl:t}=e.data;t&&(O.value=!0,P.value=t);const a=setInterval(()=>{G(),w.value=="PAID"&&clearInterval(a)},3e3)}E.value=!1},G=async()=>{const e=await m.get(_+"/payment/pay/query",{params:{appOrderId:f.value}}),{paymentStatus:t}=e.data;w.value=t,t=="PAID"&&v("\u652F\u4ED8\u6210\u529F")},B=async e=>{const t=await m.post(_+"/payment/pay/build",{accessAppId:A.value,appOrderId:f.value,payChannel:e,paySource:"MEDSCI_WEB",payType:r.value=="ALL"?"MWEB":r.value=="WX"?"JSAPI":"NATIVE"});if(t.code!="SUCCESS"){v(t.msg);return}const a=await m.post(_+"/payment/pay/order",{accessAppId:A.value,payOrderId:t.data.payOrderId,openId:u.value}),{aliH5:i,aliQR:p,wechatH5:U,wechatJsapi:d}=a.data;if(i){const I=document.createElement("div");I.innerHTML=i.html,document.body.appendChild(I),document.forms[0].submit()}p&&(window.location.href=p.payUrl),U&&(window.location.href=U.h5Url),d&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:d.appId,timeStamp:d.timeStamp,nonceStr:d.nonceStr,package:d.packageStr,signType:d.signType,paySign:d.paySign},function(I){I.err_msg=="get_brand_wcpay_request:ok"&&v.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...te(o),loading:c,userInfo:g,active:M,guize:b,time:y,url:x,actions:W,show:S,showImg:O,qrCodeUrlImg:P,isEnd:k,Login:D,buy:j,Pay:C,testPlay:H,getQueryVariable:h,wxShare:T,link:J,showGuize:F,formatTime:V,onFinish:X,onSelect:$}}},R=s=>(ce("data-v-1384dfe6"),s=s(),re(),s),ue={class:"box"},fe=["src"],ge={class:"last"},ve=R(()=>l("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/6fecbd30-f071-11ed-9b52-b908d10125b2_\u795E\u7ECF\u4F1A\u5458\u6309\u94AE.png",alt:""},null,-1)),ye=[ve],we={class:"wrapper"},he=R(()=>l("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Ie=["src"];function _e(s,c,M,b,y,E){const r=q("false-data"),f=q("van-action-sheet"),w=q("van-overlay");return se(),ae(oe,null,[l("div",ue,[L(r),l("img",{class:"image",src:s.url,alt:""},null,8,fe),l("div",ge,[l("div",{class:"bottom",onClick:c[0]||(c[0]=(...u)=>s.buy&&s.buy(...u))},ye)])]),L(f,{show:s.show,"onUpdate:show":c[1]||(c[1]=u=>s.show=u),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),L(w,{show:s.showImg,onClick:c[3]||(c[3]=u=>s.showImg=!1)},{default:ne(()=>[l("div",we,[l("div",{onClick:c[2]||(c[2]=ie(()=>{},["stop"]))},[he,l("img",{src:s.qrCodeUrlImg,alt:""},null,8,Ie)])])]),_:1},8,["show"])],64)}var Te=K(me,[["render",_e],["__scopeId","data-v-1384dfe6"]]);export{Te as default};
