import{C as f,s as o}from"./js.cookie.ad72bcd1.js";import{b as l}from"./config618.d924d98a.js";import{_ as g,r as p,b as h,o as w,d as y,e as x,f as _,T as r,p as b,i as I}from"./index.8f4d7f86.js";const S={name:"618ranbao",components:{},setup:()=>{const t=p(!1),a=p(!1),s=h({info:{}});w(async()=>{document.title="\u71C3\u7206618  \u5E74\u4E2D\u72C2\u6B22\u8282\uFF01",c({title:"\u71C3\u7206618  \u5E74\u4E2D\u72C2\u6B22\u8282\uFF01",summary:"\u6765\u4E0D\u53CA\u7ED9\u7684\uFF0C618\u201C\u60E0\u201D\u7ED9\u4F60\uFF0C\u70B9\u51FB\u9501\u5B9A\u6D3B\u52A8\u540D\u989D\uFF01",thumb:"https://img.medsci.cn/202505281055-618-ranbao-share.jpg"});const e=new Date("2025/12/31").getTime();new Date().getTime()-e>0&&(a.value=!0);const n=f.get("userInfo");n?(s.info=JSON.parse(n),d()):i(),o.get(l+"/medsci-activity/visit",{params:{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,event_type:"view",type:"research_carnival_90"}})});const c=async e=>{let u=window.location.href.split("#")[0];const n=await o.get("https://ypxcx.medsci.cn/ean/share",{params:{url:u}});wx.config(n.data),wx.error(function(v){console.log(v)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},d=async()=>{const e=await o.post(l+"/medsci-activity/attend-status",{mobile:s.info.mobile,type:"research_carnival_90"});e.data&&e.data.status==1?t.value=!0:t.value=!1},m=async()=>{if(!f.get("userInfo"))return i(),!1;if(a.value)return r("\u672C\u6B21\u6D3B\u52A8\u5DF2\u7ED3\u675F\uFF0C\u5982\u6709\u76F8\u5173\u670D\u52A1\u9700\u6C42\uFF0C\u8BF7\u62E8\u6253\u7535\u8BDD\u54A8\u8BE2\uFF1A400-0583-188\uFF0C\u4E5F\u53EF\u4EE5\u8054\u7CFB\u60A8\u7684\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u54A8\u8BE2~"),!1;if(t.value)r("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF0C\u8BF7\u7559\u610F\u7535\u8BDD\u4FE1\u606F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB");else{const e=await o.post(l+"/medsci-activity/research_carnival_90",{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,mobile:s.info.mobile,user_name:s.info.userName,real_name:s.info.realName,email:s.info.email});e.code==200&&(r.success("\u606D\u559C\u60A8\uFF0C\u62A5\u540D\u6210\u529F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB"),t.value=!0),e.code!=200&&e.code!=205&&r(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},i=()=>{addLoginDom()};return{userInfo:s,timing:a,wxShare:c,Login:i,joinStatus:t,joinActivity:m}}},k=t=>(b("data-v-d6d2409a"),t=t(),I(),t),U={class:"box"},j=k(()=>_("img",{class:"img",src:"https://img.medsci.cn/7484034972932.png",alt:""},null,-1));function T(t,a,s,c,d,m){return y(),x("div",U,[j,_("img",{class:"btn",onClick:a[0]||(a[0]=(...i)=>t.joinActivity&&t.joinActivity(...i)),src:"https://img.medsci.cn/7484034972932-btn.png"})])}var $=g(S,[["render",T],["__scopeId","data-v-d6d2409a"]]);export{$ as default};
