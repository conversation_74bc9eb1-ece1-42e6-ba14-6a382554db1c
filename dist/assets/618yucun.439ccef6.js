import{C as f,s as o}from"./js.cookie.ad72bcd1.js";import{b as l}from"./config618.d924d98a.js";import{_ as g,r as p,b as y,o as h,d as w,e as x,f as _,T as c,p as I,i as S}from"./index.8f4d7f86.js";const b={name:"618yucun",components:{},setup:()=>{const t=p(!1),i=p(!1),s=y({info:{}});h(async()=>{document.title="618\u4F1A\u5458\u793C\u9047  \u5BA0\u7684\u5C31\u662F\u4F60\uFF01",r({title:"618\u4F1A\u5458\u793C\u9047  \u5BA0\u7684\u5C31\u662F\u4F60\uFF01",summary:"6.18\u5143 \u62A2 \u4EF7\u503C100\u4F1A\u5458\u798F\u5229\uFF01",thumb:"https://img.medsci.cn/03-618yucun-share.jpg"});const e=new Date("2025/12/31").getTime();new Date().getTime()-e>0&&(i.value=!0);const a=f.get("userInfo");a?(s.info=JSON.parse(a),u()):n(),o.get(l+"/medsci-activity/visit",{params:{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,event_type:"view",type:"research_carnival_618"}})});const r=async e=>{let m=window.location.href.split("#")[0];const a=await o.get("https://ypxcx.medsci.cn/ean/share",{params:{url:m}});wx.config(a.data),wx.error(function(v){console.log(v)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:window.location.href,imgUrl:e.thumb,success:function(){}})})},u=async()=>{const e=await o.post(l+"/medsci-activity/attend-status",{mobile:s.info.mobile,type:"research_carnival_618"});e.data&&e.data.status==1?t.value=!0:t.value=!1},d=async()=>{if(!f.get("userInfo"))return n(),!1;if(i.value)return c("\u672C\u6B21\u6D3B\u52A8\u5DF2\u7ED3\u675F\uFF0C\u5982\u6709\u76F8\u5173\u670D\u52A1\u9700\u6C42\uFF0C\u8BF7\u62E8\u6253\u7535\u8BDD\u54A8\u8BE2\uFF1A400-0583-188\uFF0C\u4E5F\u53EF\u4EE5\u8054\u7CFB\u60A8\u7684\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u54A8\u8BE2~"),!1;if(t.value)c("\u60A8\u5DF2\u7ECF\u62A5\u540D\u6210\u529F\uFF0C\u8BF7\u7559\u610F\u7535\u8BDD\u4FE1\u606F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB");else{const e=await o.post(l+"/medsci-activity/research_carnival_618",{user_id:s.info.plaintextUserId,ciphertext_user_id:s.info.userId,mobile:s.info.mobile,user_name:s.info.userName,real_name:s.info.realName,email:s.info.email});e.code==200&&(c.success("\u606D\u559C\u60A8\uFF0C\u62A5\u540D\u6210\u529F\uFF0C\u5C06\u6709\u4E13\u5C5E\u5B66\u672F\u987E\u95EE\u4E0E\u60A8\u8054\u7CFB"),t.value=!0),e.code!=200&&e.code!=205&&c(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},n=()=>{addLoginDom()};return{userInfo:s,timing:i,wxShare:r,Login:n,joinStatus:t,joinActivity:d}}},k=t=>(I("data-v-5ced103f"),t=t(),S(),t),U={class:"box"},j=k(()=>_("img",{class:"img",src:"https://img.medsci.cn/17484218528119.png",alt:""},null,-1));function T(t,i,s,r,u,d){return w(),x("div",U,[j,_("img",{class:"btn",onClick:i[0]||(i[0]=(...n)=>t.joinActivity&&t.joinActivity(...n)),src:"https://img.medsci.cn/03-618yucun-btn.png"})])}var $=g(b,[["render",T],["__scopeId","data-v-5ced103f"]]);export{$ as default};
