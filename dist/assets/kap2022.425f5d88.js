import{_ as ne,u as oe,a as ie,r as i,b as D,o as ce,t as re,c as P,d as l,e as d,f as a,g as B,w as V,F as I,T as u,E as A,B as U,h as le,p as de,i as pe}from"./index.8f4d7f86.js";import{C as E,s as y}from"./js.cookie.ad72bcd1.js";import{m as me}from"./msSwiper.7d61aaec.js";import{a as ue}from"./configkap.935700b6.js";import{b as O}from"./config.3aca39f6.js";import{b as R}from"./config618.d924d98a.js";const fe={name:"huxXi",components:{msSwiper:me},setup:()=>{oe();const s=ie(),v=i(!1),W=i(!1),$=i(!1),_=i(""),k=i(""),x=i(""),C=i(""),S=i(!1),c=i(!1),p=i(""),r=i("college"),T=i(!1),X=i(new Date("2022/11/01 00:00:00").getTime()-new Date().getTime()),z=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],H=e=>{S.value=!1,J(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},o=D({info:{}}),b=D({msg:{}}),w=i(!1),j=e=>{for(var t=window.location.search.substring(1),m=t.split("&"),n=0;n<m.length;n++){var g=m[n].split("=");if(g[0]==e)return g[1]}return!1};ce(async()=>{document.title="\u77E5\u4FE1\u884C\u8C03\u67E5\u7814\u7A76 KAP\u6DA8\u4EF7\u5012\u8BA1\u65F6",F({title:"\u8FD9\u6709\u4E00\u4EFD\u5FEB\u901F\u8F6C\u5316\u79D1\u7814\u6210\u679C\u7684\u65B9\u6848\uFF0C\u8BF7\u67E5\u6536\uFF01",summary:"\u77E5\u4FE1\u884C\u8C03\u67E5\u7814\u7A76\u6DA8\u4EF7\u5012\u8BA1\u65F6\uFF0C\u73B0\u5728\u62A5\u540D\u7ACB\u51CF6000\u5143~",thumb:"https://static.medsci.cn/public-image/ms-image/48e215e0-46b3-11ed-b66b-937b834e3ef9_kap_join.png"});const e=E.get("userInfo");e&&(o.info=JSON.parse(e),K()),y.get(R+"/medsci-activity/visit",{params:{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,event_type:"view",type:"kap_rise"}})});const F=async e=>{let t=window.location.href.split("#")[0];const m=await y.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(m.data),wx.error(function(n){console.log(n)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kap2022",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kap2022",imgUrl:e.thumb,success:function(){}})})},K=async()=>{const e=await y.post(R+"/medsci-activity/attend-status",{mobile:o.info.mobile,type:"kap_rise"});e.data&&e.data.status==1?w.value=!0:w.value=!1},Q=async()=>{if(console.log(w.value,"rrr"),T.value)return u("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;if(!E.get("userInfo"))return M(),!1;if(!w.value){const e=await y.post(R+"/medsci-activity/kap_rise",{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,mobile:o.info.mobile,user_name:o.info.userName,real_name:o.info.realName,email:o.info.email});e.code==200&&(u.success("\u62A5\u540D\u6210\u529F"),w.value=!0),e.code==205&&(u("\u60A8\u5DF2\u62A5\u540D\uFF0C\u7B49\u5F85\u56DE\u8BBF"),w.value=!0),e.code!=200&&e.code!=205&&u(e.msg||"\u670D\u52A1\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5")}},G=async()=>{if(T.value)return u("\u6D3B\u52A8\u5DF2\u7ED3\u675F~"),!1;const e=navigator.userAgent,t=s.query.openId||j("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){_.value="WX";const n="wx9096048917ec59ab";if(t)C.value=t;else{const g=encodeURIComponent(window.location.href),q=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${g}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${q}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`;return}}else e!=null&&e.indexOf("AlipayClient")>-1?_.value="ALI":_.value="ALL";const m=E.get("userInfo");if(u.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),m)o.info=JSON.parse(m),o.info.mobile||addLoginDom(),N();else{const n=s.query.sso_sessionid||j("sso_sessionid");if(n){const g=await y.post("/medsciUser/getLoginUserInfoBySid",{sessionid:n});o.info=g.data,N()}else M()}},M=()=>{addLoginDom()},Y=e=>{e.link&&(window.location.href=e.link)},N=async()=>{const{userId:e,userName:t,realName:m,mobile:n,email:g,plaintextUserId:q}=o.info,f=await y.post(ue+"/openOrder/addActivityOrder",{activityId:b.msg.id,itemNum:1,itemPicPath:"",activityName:b.msg.name,itemPrice:b.msg.money,projectId:1,orderType:b.msg.type,mobile:n,payment:b.msg.money,userId:e,nikeName:t,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});f.status==200?(k.value=f.data,u.clear(),ee()):u(f.message)},Z=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},ee=async()=>{if(window.innerWidth<750)_.value=="ALL"?S.value=!0:J(_.value);else{const e=await y.post(O+"/payment/pay/merge_qrcode",{accessAppId:r.value,appOrderId:k.value}),{qrCodeUrl:t}=e.data;t&&(c.value=!0,p.value=t);const m=setInterval(()=>{se(),x.value=="PAID"&&clearInterval(m)},3e3)}$.value=!1},se=async()=>{const e=await y.get(O+"/payment/pay/query",{params:{appOrderId:k.value}}),{paymentStatus:t}=e.data;x.value=t,t=="PAID"&&u("\u652F\u4ED8\u6210\u529F")},J=async e=>{const t=await y.post(O+"/payment/pay/build",{accessAppId:r.value,appOrderId:k.value,payChannel:e,paySource:"MEDSCI_WEB",payType:_.value=="ALL"?"MWEB":_.value=="WX"?"JSAPI":"NATIVE"});if(t.code!="SUCCESS"){u(t.msg);return}const m=await y.post(O+"/payment/pay/order",{accessAppId:r.value,payOrderId:t.data.payOrderId,openId:C.value}),{aliH5:n,aliQR:g,wechatH5:q,wechatJsapi:f}=m.data;if(n){const L=document.createElement("div");L.innerHTML=n.html,document.body.appendChild(L),document.forms[0].submit()}g&&(window.location.href=g.payUrl),q&&u.fail("\u7528\u5FAE\u4FE1\u7AEF\u6253\u5F00\u8FDB\u884C\u652F\u4ED8"),f&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:f.appId,timeStamp:f.timeStamp,nonceStr:f.nonceStr,package:f.packageStr,signType:f.signType,paySign:f.paySign},function(L){L.err_msg=="get_brand_wcpay_request:ok"&&u.success("\u652F\u4ED8\u6210\u529F\uFF01")})},te=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},ae=()=>{T.value=!0};return{...re(b),loading:v,userInfo:o,active:W,actions:z,show:S,showImg:c,qrCodeUrlImg:p,isEnd:T,Login:M,buy:G,Pay:N,testPlay:Y,getQueryVariable:j,wxShare:F,link:Z,onSelect:H,formatTime:te,onFinish:ae,time:X,joinActivity:Q,joinStatus:w}}},h=s=>(de("data-v-49a78cfc"),s=s(),pe(),s),ve={class:"box"},ge=h(()=>a("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/3e94b3e0-46ae-11ed-b66b-937b834e3ef9_kap.png",alt:""},null,-1)),ye={class:"last"},_e={class:"top"},he=h(()=>a("img",{class:"img",src:"https://static.medsci.cn/public-image/ms-image/75ec18c0-46b7-11ed-b66b-937b834e3ef9_djs.png",alt:""},null,-1)),we={class:"block"},be=h(()=>a("span",{class:"colon a"},"\u5929",-1)),Ie={class:"block"},ke=h(()=>a("span",{class:"colon a"},"\u65F6",-1)),Se={class:"block"},Ae=h(()=>a("span",{class:"colon a"},"\u5206",-1)),Ue={class:"block"},xe=h(()=>a("span",{class:"colon a"},"\u79D2",-1)),Ce={class:"block"},Te=h(()=>a("span",{class:"colon"},[a("div",{class:"mill"},"\u6BEB"),a("div",{class:"mill"},"\u79D2")],-1)),qe={key:1,class:"image",src:"https://static.medsci.cn/public-image/ms-image/d6634130-4778-11ed-b66b-937b834e3ef9_join2.png",alt:""},Le={class:"wrapper"},Oe=h(()=>a("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),je=["src"];function Me(s,v,W,$,_,k){const x=P("van-count-down"),C=P("van-action-sheet"),S=P("van-overlay");return l(),d(I,null,[a("div",ve,[ge,a("div",ye,[a("div",_e,[he,B(x,{millisecond:"",time:s.time,onFinish:s.onFinish},{default:V(c=>[a("div",we,[(l(!0),d(I,null,A(s.formatTime(c.days),(p,r)=>(l(),d("div",{class:"block-item",key:r},U(p),1))),128))]),be,a("div",Ie,[(l(!0),d(I,null,A(s.formatTime(c.hours),(p,r)=>(l(),d("div",{class:"block-item",key:r},U(p),1))),128))]),ke,a("div",Se,[(l(!0),d(I,null,A(s.formatTime(c.minutes),(p,r)=>(l(),d("div",{class:"block-item",key:r},U(p),1))),128))]),Ae,a("div",Ue,[(l(!0),d(I,null,A(s.formatTime(c.seconds),(p,r)=>(l(),d("div",{class:"block-item",key:r},U(p),1))),128))]),xe,a("div",Ce,[(l(!0),d(I,null,A(s.formatTime(c.milliseconds),(p,r)=>(l(),d("div",{class:"block-item",key:r},U(p),1))),128))]),Te]),_:1},8,["time","onFinish"])]),s.joinStatus?(l(),d("img",qe)):(l(),d("img",{key:0,class:"image",onClick:v[0]||(v[0]=(...c)=>s.joinActivity&&s.joinActivity(...c)),src:"https://static.medsci.cn/public-image/ms-image/a02d2380-46ae-11ed-b66b-937b834e3ef9_join.png",alt:""}))])]),B(C,{show:s.show,"onUpdate:show":v[1]||(v[1]=c=>s.show=c),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),B(S,{show:s.showImg,onClick:v[3]||(v[3]=c=>s.showImg=!1)},{default:V(()=>[a("div",Le,[a("div",{onClick:v[2]||(v[2]=le(()=>{},["stop"]))},[Oe,a("img",{src:s.qrCodeUrlImg,alt:""},null,8,je)])])]),_:1},8,["show"])],64)}var $e=ne(fe,[["render",Me],["__scopeId","data-v-49a78cfc"]]);export{$e as default};
