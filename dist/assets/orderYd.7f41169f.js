import{_ as H,a as X,u as j,r,l as F,b as q,o as Q,t as Y,c as O,d as C,e as A,f as a,F as B,E as G,B as h,g as P,w as K,T as v,h as Z,p as $,i as ee}from"./index.8f4d7f86.js";import{C as se,s as g}from"./js.cookie.ad72bcd1.js";import{b as S}from"./config.3aca39f6.js";const te={setup(){const d=X();j();const n=r(0),L=r(!1),t=r(""),_=r(""),b=r(""),y=r(""),f=r(!1),o=r(!1),U=r(""),x=r("college"),E=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],N=e=>{f.value=!1,k(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},M=F(()=>u.list.filter(e=>e.active).map(e=>e.id)),p=q({info:null}),u=q({list:[],diacountList:[]});Q(()=>{R(),z()});const R=async()=>{const e=sessionStorage.getItem("ms_class_list");n.value=sessionStorage.getItem("ms_class_price");const s=JSON.parse(sessionStorage.getItem("ms_class_discount"));u.diacountList=s,u.list=JSON.parse(e),u.list.forEach((i,c)=>{u.diacountList.forEach((l,w)=>{c==w&&(i.activityPrice=l.activityPrice)})})},W=e=>{const i=window.location.search.substring(1).split("&");for(let c=0;c<i.length;c++){const l=i[c].split("=");if(l[0]==e)return l[1]}return!1},T=()=>{window.addLoginDom()},z=()=>{const e=navigator.userAgent,s=d.query.openId||W("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){t.value="WX";const i="wx9096048917ec59ab";if(s)y.value=s;else{const c=encodeURIComponent(window.location.href),l=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${i}?returnUrl=${c}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${i}&redirect_uri=${l}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?t.value="ALI":t.value="ALL"},J=async()=>{const e=se.get("userInfo");if(e)p.info=JSON.parse(e);else return T(),!1;v.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const s=await g.post("/activity/course/createCourseOrder",{ids:M.value,mobile:p.info?p.info.mobile:"",username:p.info?p.info.userName:"",userId:p.info?p.info.plaintextUserId:"",category:"new-year-day",department:sessionStorage.getItem("ms_class_type")});s.status==200?(_.value=s.result.orderNo,v.clear(),D()):v(s.message)},D=async()=>{if(window.innerWidth<750)t.value=="ALL"?f.value=!0:k(t.value);else{const e=await g.post(S+"/payment/pay/merge_qrcode",{accessAppId:x.value,appOrderId:_.value}),{qrCodeUrl:s}=e.data;s&&(o.value=!0,U.value=s);const i=setInterval(()=>{V(),b.value=="PAID"&&clearInterval(i)},3e3)}L.value=!1},V=async()=>{const e=await g.get(S+"/payment/pay/query",{params:{appOrderId:_.value}}),{paymentStatus:s}=e.data;b.value=s,s=="PAID"&&(v("\u652F\u4ED8\u6210\u529F"),sessionStorage.getItem("ms_class_type")=="breathing"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyBreath":sessionStorage.getItem("ms_class_type")=="scientific_research"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyScience":sessionStorage.getItem("ms_class_type")=="cardiovascular"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxg":sessionStorage.getItem("ms_class_type")=="nerve"&&(window.location.href="https://static.medsci.cn/active/index.html#/sjNew"))},k=async e=>{const s=await g.post(S+"/payment/pay/build",{accessAppId:x.value,appOrderId:_.value,payChannel:e,paySource:"MEDSCI_WEB",payType:t.value==="ALL"?"MWEB":t.value==="WX"?"JSAPI":"NATIVE"});if(s.code!=="SUCCESS"){v(s.msg);return}const i=await g.post(S+"/payment/pay/order",{accessAppId:x.value,payOrderId:s.data.payOrderId,openId:y.value}),{aliH5:c,aliQR:l,wechatH5:w,wechatJsapi:m}=i.data;if(c){const I=document.createElement("div");I.innerHTML=c.html,document.body.appendChild(I),document.forms[0].submit()}l&&(window.location.href=l.payUrl),w&&(window.location.href=w.h5Url),m&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:m.appId,timeStamp:m.timeStamp,nonceStr:m.nonceStr,package:m.packageStr,signType:m.signType,paySign:m.paySign},function(I){I.err_msg=="get_brand_wcpay_request:ok"&&(v.success("\u652F\u4ED8\u6210\u529F\uFF01"),sessionStorage.getItem("ms_class_type")=="breathing"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyBreath":sessionStorage.getItem("ms_class_type")=="scientific_research"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyScience":sessionStorage.getItem("ms_class_type")=="cardiovascular"?window.location.href="https://static.medsci.cn/product/medsci/active/zen0214/index.html#/nyxxg":sessionStorage.getItem("ms_class_type")=="nerve"&&(window.location.href="https://static.medsci.cn/active/index.html#/sjNew"))})};return{...Y(u),activityPrice:n,actions:E,show:f,showImg:o,qrCodeUrlImg:U,submit:J,onSelect:N}}},ae=d=>($("data-v-bdbd703c"),d=d(),ee(),d),oe={class:"box"},ne={class:"item-box"},ie={class:"info"},ce=["src"],re={class:"cls"},le={class:"t"},de={class:"v"},pe={class:"original"},me={class:"bottom"},ue={class:"bottom-left"},ve={class:"num"},_e=ae(()=>a("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),fe=["src"];function he(d,n,L,t,_,b){const y=O("van-action-sheet"),f=O("van-overlay");return C(),A(B,null,[a("div",oe,[a("div",ne,[(C(!0),A(B,null,G(d.list,o=>(C(),A("div",{class:"item",key:o},[a("div",ie,[a("img",{class:"img",src:o.cover,alt:""},null,8,ce),a("div",re,[a("div",le,h(o.title),1),a("div",de,[a("div",pe,"\u539F\u4EF7\uFF1A"+h(o.price)+"\u5143",1),a("div",null,"\u6298\u540E\uFF1A"+h(o.activityPrice)+"\u5143",1)])])])]))),128))]),a("div",me,[a("div",ue,[a("span",ve,"\u5171"+h(d.list.length)+"\u4EF6\uFF0C",1),a("span",null,"\u5408\u8BA1\uFF1A"+h(t.activityPrice)+"\u5143",1)]),a("div",{class:"bottom-right",onClick:n[0]||(n[0]=(...o)=>t.submit&&t.submit(...o))},"\u7ED3\u7B97")])]),P(y,{show:t.show,"onUpdate:show":n[1]||(n[1]=o=>t.show=o),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),P(f,{show:t.showImg,onClick:n[3]||(n[3]=o=>t.showImg=!1)},{default:K(()=>[a("div",{class:"wrapper",onClick:n[2]||(n[2]=Z(()=>{},["stop"]))},[a("div",null,[_e,a("img",{src:t.qrCodeUrlImg,alt:""},null,8,fe)])])]),_:1},8,["show"])],64)}var Ie=H(te,[["render",he],["__scopeId","data-v-bdbd703c"]]);export{Ie as default};
