import{_ as ae,r as a,u as oe,a as ie,b as A,o as ne,t as le,c as ce,d as m,e as f,f as s,C as g,n as y,g as re,w as de,F as pe,T as c,h as me,p as fe,i as ue}from"./index.8f4d7f86.js";import{C as ge,s as v}from"./js.cookie.ad72bcd1.js";import{f as ye}from"./falseData.c0306b2a.js";import{m as _e}from"./msSwiper.7d61aaec.js";import{b as U}from"./config.3aca39f6.js";import{b as V}from"./configFreeVip.d32e6c36.js";import{m as ve}from"./sanofiConfig.a80970f2.js";import{_ as he,a as be,b as we}from"./close.3afbe22b.js";const Se={name:"openCourse",components:{falseData:ye,msSwiper:_e},setup:()=>{const e=a(""),o=a(""),h=a(<PERSON><PERSON><PERSON>);oe();const d=ie(),N=a(!1),k=a("1"),S=a(""),r=a(!1),D=a(!1),P=a(!1),z=a(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime());a(!1);const F=a(""),E=a("");a("");const L=a(""),x=a(!0),M=a(!1),W=a(""),B=a("college"),T=a(!1),C=a(!1),O=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],j=t=>{x.value=!1,se(t.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},l=A({info:{}}),R=A({msg:{}}),$=t=>{for(var n=window.location.search.substring(1),b=n.split("&"),u=0;u<b.length;u++){var _=b[u].split("=");if(_[0]==t)return _[1]}return!1};ne(async()=>{window.innerWidth<768?h.value=!0:h.value=!1,d.query.type?d.query.type=="fund"?(o.value="fund",document.title="\u57FA\u91D1\u4F1A\u5458",e.value="https://static.medsci.cn/public-image/ms-image/34edcdc0-9664-11ee-926a-11c8565c20b2_fund.png"):d.query.type=="open"?(o.value="open",document.title="\u516C\u5F00\u8BFE\u4F1A\u5458",e.value="https://static.medsci.cn/public-image/ms-image/17eb6470-9665-11ee-926a-11c8565c20b2_open.png"):d.query.type=="guide"?(o.value="guide",document.title="\u6307\u5357\u4F1A\u5458",e.value="https://static.medsci.cn/public-image/ms-image/5dea3f00-9665-11ee-926a-11c8565c20b2_article.png"):d.query.type=="class1"?(o.value="class1",document.title="\u79D1\u7814\u7CBE\u54C1\u8BFE\u4F1A\u5458",e.value="https://static.medsci.cn/public-image/ms-image/a45e2280-9665-11ee-926a-11c8565c20b2_class1.png"):d.query.type=="class2"&&(o.value="class2",document.title="\u5FC3\u8840\u7BA1\u7CBE\u54C1\u8BFE",e.value="https://static.medsci.cn/public-image/ms-image/e9d24940-9665-11ee-926a-11c8565c20b2_class2.png"):(o.value="journal",document.title="\u671F\u520A\u4F1A\u5458",e.value="https://static.medsci.cn/public-image/ms-image/d7461690-9632-11ee-926a-11c8565c20b2_jounal.png"),ge.get("userInfo"),v.get(V+"/medsci-activity/visit",{params:{user_id:l.info.plaintextUserId,ciphertext_user_id:l.info.userId,event_type:"view",type:"new_user_register"}})});const H=async()=>{(await v.get(ve+"/perfectInfo/userInfoStatus?encryptionUserId="+l.info.userId)).data.isCompleteInfo?I():addPerfectInfoDom()},I=async()=>{c.clear(),C.value=!0},J=async()=>{C.value=!1},Q=async()=>{I()},X=()=>{addLoginDom()},G=t=>{t.link&&(window.location.href=t.link)},K=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const t=await v.post(V+"/medsci-activity/give/member-card",{user_id:l.info.plaintextUserId,ciphertext_user_id:l.info.userId,mobile:l.info.mobile,user_name:l.info.userName,real_name:l.info.realName,type:"breathing_give"});t.code!==200&&t.code!==205&&(c.clear(),c(t.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),t.code==200&&(c.clear(),c("\u606D\u559C\u60A8\u5DF2\u6210\u529F\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0"),t.code==205&&(c.clear(),c("\u60A8\u5DF2\u7ECF\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458\uFF0C\u76F4\u63A5\u5B66\u4E60\u5373\u53EF"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0")},Y=()=>{var t=navigator.userAgent;const n=t.indexOf("Android")>-1||t.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},Z=t=>{if(t>=10){const n=String(t);return[n[0],n[1]]}else return[0,t]},ee=()=>{P.value=!0},te=()=>{T.value=!0},se=async t=>{const n=await v.post(U+"/payment/pay/build",{accessAppId:B.value,appOrderId:E.value,payChannel:t,paySource:"MEDSCI_WEB",payType:F.value=="ALL"?"MWEB":F.value=="WX"?"JSAPI":"NATIVE"});if(n.code!="SUCCESS"){c(n.msg);return}const b=await v.post(U+"/payment/pay/order",{accessAppId:B.value,payOrderId:n.data.payOrderId,openId:L.value}),{aliH5:u,aliQR:_,wechatH5:q,wechatJsapi:p}=b.data;if(u){const w=document.createElement("div");w.innerHTML=u.html,document.body.appendChild(w),document.forms[0].submit()}_&&(window.location.href=_.payUrl),q&&(window.location.href=q.h5Url),p&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:p.appId,timeStamp:p.timeStamp,nonceStr:p.nonceStr,package:p.packageStr,signType:p.signType,paySign:p.paySign},function(w){w.err_msg=="get_brand_wcpay_request:ok"&&(c.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://open.medsci.cn/")})};return{curVal:k,...le(R),loading:r,userInfo:l,active:D,guize:P,time:z,vipType:S,actions:O,show:x,showImg:M,qrCodeUrlImg:W,isEnd:T,timing:N,dialogVisible:C,isMobile:h,url:e,type:o,getBtn:Q,Login:X,Pay:K,testPlay:G,getQueryVariable:$,link:Y,showGuize:ee,formatTime:Z,onFinish:te,onSelect:j,isLimitComplete:H,getBtn1:I,close:J}}},i=e=>(fe("data-v-dbdc77c4"),e=e(),ue(),e),Ce={class:"box"},Ie=["src"],Ne={class:"bottom"},ke={key:0,class:"left"},Pe=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"199",-1)),Fe=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),xe={key:1,class:"left"},Be=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"299",-1)),Te=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),qe={key:2,class:"left"},Ae=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"299",-1)),Ue=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),Ve={key:3,class:"left"},De=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"299",-1)),ze=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),Ee={key:4,class:"left"},Le=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"1499",-1)),Me=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),We={key:5,class:"left"},Oe=i(()=>s("span",{style:{color:"#fa3a53","font-size":"0.66rem","font-family":"PingFang SC SNaNpxibold"}},"799",-1)),je=i(()=>s("span",{style:{"font-family":"PingFang SC SNaNpxibold",color:"#fa3a53"}},"\u5143/\u5E74",-1)),Re={class:"block",style:{width:"8.8rem",position:"relative"}},$e=i(()=>s("img",{style:{width:"100%",height:"100%"},src:be,alt:""},null,-1)),He=i(()=>s("img",{style:{width:"50%",position:"absolute",bottom:"31%",left:"24.8%"},src:we,alt:""},null,-1));function Je(e,o,h,d,N,k){const S=ce("van-overlay");return m(),f(pe,null,[s("div",Ce,[s("img",{class:"img",src:e.url,alt:""},null,8,Ie),s("div",Ne,[e.type=="journal"?(m(),f("div",ke,[g(" \u671F\u520A\u4F1A\u5458 "),Pe,Fe])):y("",!0),e.type=="fund"?(m(),f("div",xe,[g(" \u57FA\u91D1\u4F1A\u5458 "),Be,Te])):y("",!0),e.type=="open"?(m(),f("div",qe,[g(" \u516C\u5F00\u8BFE\u4F1A\u5458 "),Ae,Ue])):y("",!0),e.type=="guide"?(m(),f("div",Ve,[g(" \u6307\u5357\u4F1A\u5458 "),De,ze])):y("",!0),e.type=="class1"?(m(),f("div",Ee,[g(" \u79D1\u7814\u4F1A\u5458 "),Le,Me])):y("",!0),e.type=="class2"?(m(),f("div",We,[g(" \u5FC3\u8840\u7BA1\u4F1A\u5458 "),Oe,je])):y("",!0),s("div",{class:"right right-2",onClick:o[1]||(o[1]=(...r)=>e.getBtn&&e.getBtn(...r))},[s("img",{style:{width:"100%",height:"100%"},onClick:o[0]||(o[0]=(...r)=>e.getBtn&&e.getBtn(...r)),src:"https://static.medsci.cn/public-image/ms-image/2ca10ed0-9640-11ee-926a-11c8565c20b2_buy.png",alt:""})])])]),re(S,{show:e.dialogVisible,onClick:o[4]||(o[4]=r=>e.show=!1)},{default:de(()=>[s("div",{class:"wrapper",onClick:o[3]||(o[3]=me(()=>{},["stop"])),style:{display:"flex","flex-direction":"column"}},[s("div",Re,[$e,He,s("img",{onClick:o[2]||(o[2]=(...r)=>e.close&&e.close(...r)),style:{width:"8%",position:"absolute",bottom:"-9%",left:"46%"},src:he,alt:""})])])]),_:1},8,["show"])],64)}var st=ae(Se,[["render",Je],["__scopeId","data-v-dbdc77c4"]]);export{st as default};
