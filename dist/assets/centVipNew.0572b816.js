import{_ as re,u as de,a as pe,r as t,b as J,o as me,t as ue,c as q,d as L,e as B,f as c,g as O,F as H,E as fe,w as ge,T as u,h as ve,p as we,i as he}from"./index.8f4d7f86.js";import{C as X,s as l}from"./js.cookie.ad72bcd1.js";import{f as _e}from"./falseData.c0306b2a.js";import{m as ye}from"./msSwiper.7d61aaec.js";import{b as y}from"./config.3aca39f6.js";import{b}from"./configFreeVip.d32e6c36.js";import{m as be}from"./sanofiConfig.a80970f2.js";var Ie="./assets/centVip.277fe3a5.png";const Se={name:"centVip",components:{falseData:_e,msSwiper:ye},setup:()=>{de();const a=pe(),r=t(!1),w=t([{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_lessons.png",id:"1",isSelect:!0},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_guide.png",id:"2",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_journal.png",id:"3",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_fund.png",id:"4",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_open.png",id:"10",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_breath.png",id:"5",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_neurology.png",id:"6",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_cardio.png",id:"7",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/a36ee5e0-3741-11ee-aed8-05e366306843_pifuke.png",id:"8",isSelect:!1},{url:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_eye.png",id:"9",isSelect:!1}]),o=t(localStorage.getItem("selectedId")||"1"),V=t(""),M=t(!1),I=t(!1),h=t(!1),S=t(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime()),d=t(!1),m=t(""),f=t(""),E=t(""),D=t(""),k=t(!1),x=t(!1),N=t(""),C=t("college"),$=t(!1),Q=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],G=e=>{k.value=!1,z(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},P=t(localStorage.getItem("selectedId")||"1"),i=J({info:{}}),K=J({msg:{}}),R=async()=>{(await l.get(be+"/perfectInfo/userInfoStatus?encryptionUserId="+i.info.userId)).data.isCompleteInfo?A():addPerfectInfoDom()},U=e=>{for(var s=window.location.search.substring(1),n=s.split("&"),p=0;p<n.length;p++){var g=n[p].split("=");if(g[0]==e)return g[1]}return!1};me(async()=>{Y(),w.value.forEach(e=>{e.id===P.value?(console.log(o.value,"curVal"),e.isSelect=!0):e.isSelect=!1}),document.title="\u6885\u65AF\u7279\u522B\u5206\u4EAB0.01\u5143\u4EFB\u900910\u5927\u4F1A\u5458\uFF01",W({title:"\u6885\u65AF\u7279\u522B\u5206\u4EAB0.01\u5143\u4EFB\u900910\u5927\u4F1A\u5458\uFF01",summary:"\u70B9\u51FB\u83B7\u53D6\uFF01\u7CBE\u54C1\u4F1A\u545810\u90091\uFF0C\u4EC5\u9650\u4ECA\u5929\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/78b03b20-4d4d-11ee-abff-45aa74f03fdd_300x300.png"}),X.get("userInfo"),l.get(b+"/medsci-activity/visit",{params:{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,event_type:"view",type:"new_year"}})});const W=async e=>{let s=window.location.href.split("#")[0];const n=await l.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(n.data),wx.error(function(p){console.log(p)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVipNew",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/centVipNew",imgUrl:e.thumb,success:function(){}})})},Y=()=>{const e=navigator.userAgent,s=a.query.openId||U("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){m.value="WX";const n="wx9096048917ec59ab";if(s)D.value=s;else{const p=encodeURIComponent(window.location.href),g=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${p}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${g}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?m.value="ALI":m.value="ALL"},Z=async e=>{o.value=e,localStorage.setItem("selectedId",o.value),w.value.forEach(s=>{o.value==s.id?s.isSelect=!0:s.isSelect=!1})},ee=async()=>{u.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=X.get("userInfo");if(e)i.info=JSON.parse(e),i.info.mobile?R():addLoginDom();else{const s=a.query.sso_sessionid||U("sso_sessionid");if(s){const n=await l.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});i.info=n.data,A()}else j()}},j=()=>{addLoginDom()},se=e=>{e.link&&(window.location.href=e.link)},A=async()=>{const e=await l.post(b+"/medsci-activity/attend-status",{order_id:f.value,type:"new_year",mobile:i.info.mobile});if(e.data.status==0){let s={1:"scientific_research",2:"guider",3:"journal",4:"nsfc",5:"breathe",6:"nerve",7:"cardiovascular",8:"skin",9:"ophthalmology",10:"open"};const n=await l.post(b+"/medsci-activity/pay/member-card/"+s[o.value],{user_id:i.info.plaintextUserId,ciphertext_user_id:i.info.userId,mobile:i.info.mobile,user_name:i.info.userName,real_name:i.info.realName,type:"new_year"});n.code!==200&&n.code!==205&&(u.clear(),u(n.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),f.value=n.data.data,u.clear(),oe()}else(e.data.status==1||res.code==205)&&u("\u60A8\u5DF2\u7ECF\u9886\u8FC7\u4E86\u54E6\uFF01")},te=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},ae=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},ne=()=>{h.value=!0},ie=()=>{$.value=!0},oe=async()=>{if(window.innerWidth<750)m.value=="ALL"?k.value=!0:z(m.value);else{const e=await l.post(y+"/payment/pay/merge_qrcode",{accessAppId:C.value,appOrderId:f.value}),{qrCodeUrl:s}=e.data;s&&(x.value=!0,N.value=s);const n=setInterval(()=>{le(),E.value=="PAID"&&(x.value=!1,ce(),clearInterval(n))},3e3)}d.value=!1},ce=async()=>{const e=await l.post(b+"/medsci-activity/pay/member-card-status",{order_id:f.value,type:"new_year"});console.log(e,"\u4E0B\u5355\u6210\u529F")},le=async()=>{const e=await l.get(y+"/payment/pay/query",{params:{appOrderId:f.value}}),{paymentStatus:s}=e.data;E.value=s,s=="PAID"&&(u("\u652F\u4ED8\u6210\u529F"),console.log(o),o.value=="3"?window.location.href="https://www.medsci.cn/sci/index.do":o.value=="2"?window.location.href="https://www.medsci.cn/guideline/index.do":o.value=="4"?window.location.href="https://www.medsci.cn/sci/nsfc.do?utm_campaign":["1","5","6","7","8","9"].includes(o.value)&&(window.location.href="https://class.medsci.cn/"))},z=async e=>{const s=await l.post(y+"/payment/pay/build",{accessAppId:C.value,appOrderId:f.value,payChannel:e,paySource:"MEDSCI_WEB",payType:m.value=="ALL"?"MWEB":m.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){u(s.msg);return}const n=await l.post(y+"/payment/pay/order",{accessAppId:C.value,payOrderId:s.data.payOrderId,openId:D.value}),{aliH5:p,aliQR:g,wechatH5:F,wechatJsapi:v}=n.data;if(p){const _=document.createElement("div");_.innerHTML=p.html,document.body.appendChild(_),document.forms[0].submit()}g&&(window.location.href=g.payUrl),F&&(window.location.href=F.h5Url),v&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:v.appId,timeStamp:v.timeStamp,nonceStr:v.nonceStr,package:v.packageStr,signType:v.signType,paySign:v.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&u.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{curVal:o,urls:w,...ue(K),loading:M,userInfo:i,active:I,guize:h,time:S,vipType:V,actions:Q,show:k,showImg:x,qrCodeUrlImg:N,isEnd:$,timing:r,selectedId:P,getBtn:ee,Login:j,Pay:A,testPlay:se,getQueryVariable:U,wxShare:W,link:te,showGuize:ne,formatTime:ae,onFinish:ie,onSelect:G,selectOne:Z,isLimitComplete:R}}},T=a=>(we("data-v-2a8690cb"),a=a(),he(),a),ke={class:"box"},xe=T(()=>c("img",{class:"image",src:Ie,alt:""},null,-1)),Ce={class:"content"},Ue=["onClick"],Ae=["src"],qe=["src"],Le={class:"last"},Be=T(()=>c("img",{src:"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_btn.png",alt:""},null,-1)),Oe=[Be],Te=T(()=>c("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Ve=["src"];function Me(a,r,w,o,V,M){const I=q("false-data"),h=q("van-action-sheet"),S=q("van-overlay");return L(),B(H,null,[c("div",ke,[O(I),xe,c("div",Ce,[(L(!0),B(H,null,fe(a.urls,(d,m)=>(L(),B("div",{class:"content_box",key:m,onClick:f=>a.selectOne(d.id)},[c("img",{class:"content1",src:d.url,alt:""},null,8,Ae),c("img",{class:"btn",src:d.isSelect?"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_selected.png":"https://static.medsci.cn/public-image/ms-image/342939e0-34eb-11ee-aed8-05e366306843_no-btn.png"},null,8,qe)],8,Ue))),128))]),c("div",Le,[c("div",{class:"bottom",onClick:r[0]||(r[0]=(...d)=>a.getBtn&&a.getBtn(...d))},Oe)])]),O(h,{show:a.show,"onUpdate:show":r[1]||(r[1]=d=>a.show=d),actions:a.actions,onSelect:a.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),O(S,{show:a.showImg,onClick:r[3]||(r[3]=d=>a.showImg=!1)},{default:ge(()=>[c("div",{class:"wrapper",onClick:r[2]||(r[2]=ve(()=>{},["stop"]))},[c("div",null,[Te,c("img",{src:a.qrCodeUrlImg,alt:""},null,8,Ve)])])]),_:1},8,["show"])],64)}var je=re(Se,[["render",Me],["__scopeId","data-v-2a8690cb"]]);export{je as default};
