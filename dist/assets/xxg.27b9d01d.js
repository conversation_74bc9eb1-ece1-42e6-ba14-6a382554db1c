import{_ as Y,u as Z,a as ee,r as i,b as R,o as te,T as y,t as se,c as q,d as ae,e as ne,f as m,g as P,w as ie,F as oe,h as ce,p as re,i as le}from"./index.8f4d7f86.js";import{s as c,C as de}from"./js.cookie.ad72bcd1.js";import{f as me}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b}from"./config.3aca39f6.js";import{m as ue}from"./sanofiConfig.a80970f2.js";const ge={name:"Home",components:{falseData:me,msSwiper:pe},setup:()=>{Z();const s=ee(),r=i(!1),L=i(!1),x=i(!1),w=i(new Date("2032/03/16 14:00:00").getTime()-new Date().getTime()),v=i(""),S=i(!1),d=i(""),g=i(""),u=i(""),D=i(""),A=i(!1),M=i(!1),E=i(""),C=i("college"),U=i(!1),z=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],j=e=>{A.value=!1,N(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},f=R({info:{}}),a=R({msg:{}}),O=async()=>{const e=await c.get(ue+"/perfectInfo/userInfoStatus?encryptionUserId="+f.info.userId);console.log(e,"rreeesss"),e.data.isCompleteInfo?k():addPerfectInfoDom()},h=e=>{for(var t=window.location.search.substring(1),n=t.split("&"),o=0;o<n.length;o++){var p=n[o].split("=");if(p[0]==e)return p[1]}return!1};te(async()=>{if(s.query.day==30){document.title=" \u5FC3\u8840\u7BA1\u4F1A\u5458\u6708\u5EA6\u4F1A\u5458\u5361",v.value="https://static.medsci.cn/public-image/ms-image/bead3990-f08a-11ed-9b52-b908d10125b2_\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_16838687107624.png";const t=await c.post("/activity/memberCardDetail",{id:"****************"});a.msg=t.data,I({title:"\u5FC3\u8840\u7BA1\u4F1A\u5458\u6708\u5EA6\u4F1A\u5458\u5361",summary:"\u6708\u5EA6\u4F1A\u5458\u4EC5\u9700"+a.msg.activityPrice+"\u5143",thumb:"https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"})}else if(s.query.day==90){document.title=" \u5FC3\u5185\u79D1\u8BFE\u7A0B\u4F1A\u5458",v.value="https://static.medsci.cn/public-image/ms-image/60aef8f0-4ae5-11ee-97bd-cf3d3c77cb57_GW&\u6885\u65AF\u5FC3\u5185.png";const t=await c.post("/activity/memberCardDetail",{id:"****************"});a.msg=t.data,I({title:"\u5FC3\u5185\u79D1\u8BFE\u7A0B\u4F1A\u5458",summary:"\u5FC3\u5185\u79D1\u8BFE\u7A0B\u4F1A\u5458\u4EC5\u9700"+a.msg.activityPrice+"\u5143",thumb:"https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"})}else{document.title=" \u5FC3\u8840\u7BA1\u4F1A\u5458\u5E74\u5EA6\u4F1A\u5458\u5361",v.value="https://static.medsci.cn/public-image/ms-image/e2ebbc20-f542-11ed-80f5-c75471cc8620_\u5E74\u5361.png";const t=await c.post("/activity/memberCardDetail",{id:"****************"});a.msg=t.data,I({title:" \u5FC3\u8840\u7BA1\u4F1A\u5458\u5E74\u5EA6\u4F1A\u5458\u5361",summary:"\u5E74\u5EA6\u4F1A\u5458\u4EC5\u9700"+a.msg.activityPrice+"\u5143",thumb:"https://static.medsci.cn/public-image/ms-image/aa67dfa0-a417-11ec-a1b8-6123b3ff61ea_xxg.png"})}a.msg&&a.msg.activityEndTime&&(a.msg.activityEndTime=a.msg.activityEndTime.replace(/-/g,"/"),w.value=new Date(a.msg.activityEndTime).getTime()-new Date().getTime()),w.value<0&&(U.value=!0);let e=navigator.userAgent;e!=null&&e.indexOf("MicroMessenger")>-1&&(s.query.openId||h("openId"))&&B()});const I=async e=>{let t=window.location.href.split("#")[0];const n=await c.get("https://ypxcx.medsci.cn/ean/share",{params:{url:t}});wx.config(n.data),wx.error(function(o){console.log(o)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/xxg",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/xxg",imgUrl:e.thumb,success:function(){}})})},H=()=>{const e=navigator.userAgent,t=s.query.openId||h("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){d.value="WX";const n="wx9096048917ec59ab";if(t)D.value=t;else{const o=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${o}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?d.value="ALI":d.value="ALL"},B=async()=>{H();const e=de.get("userInfo");if(y.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)f.info=JSON.parse(e),f.info.mobile||addLoginDom(),await O();else{const t=s.query.sso_sessionid||h("sso_sessionid");if(t){const n=await c.post("/medsciUser/getLoginUserInfoBySid",{sessionid:t});f.info=n.data,k()}else W()}},W=()=>{addLoginDom()},J=e=>{e.link&&(window.location.href=e.link)},k=async()=>{const{userId:e,userName:t,realName:n,mobile:o,email:p,plaintextUserId:T}=f.info,l=await c.post("/activity/createOrder",{itemId:a.msg.id,itemNum:1,itemPicPath:a.msg.cardImage,itemTitle:a.msg.cardName,itemPrice:a.msg.activityPrice,projectId:a.msg.projectId,orderType:1,mobile:o,payment:a.msg.activityPrice,userId:e,nikeName:t,orderExplain:"\u4E34\u5E8A\u4F1A\u5458\u6D3B\u52A8"});console.log(l.data,"app_order_id"),g.value=l.data,y.clear(),Q()},V=()=>{var e=navigator.userAgent;const t=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},F=e=>{if(e>=10){const t=String(e);return[t[0],t[1]]}else return[0,e]},X=()=>{x.value=!0},G=()=>{U.value=!0},Q=async()=>{if(window.innerWidth<750)d.value=="ALL"?A.value=!0:N(d.value);else{const e=await c.post(b+"/payment/pay/merge_qrcode",{accessAppId:C.value,appOrderId:g.value}),{qrCodeUrl:t}=e.data;t&&(M.value=!0,E.value=t);const n=setInterval(()=>{K(),u.value=="PAID"&&clearInterval(n)},3e3)}S.value=!1},K=async()=>{const e=await c.get(b+"/payment/pay/query",{params:{appOrderId:g.value}}),{paymentStatus:t}=e.data;u.value=t,t=="PAID"&&y("\u652F\u4ED8\u6210\u529F")},N=async e=>{const t=await c.post(b+"/payment/pay/build",{accessAppId:C.value,appOrderId:g.value,payChannel:e,paySource:"MEDSCI_WEB",payType:d.value=="ALL"?"MWEB":d.value=="WX"?"JSAPI":"NATIVE"});if(console.log(t.data,"build"),t.code!="SUCCESS"){y(t.msg);return}const n=await c.post(b+"/payment/pay/order",{accessAppId:C.value,payOrderId:t.data.payOrderId,openId:D.value});console.log(n.data,"order");const{aliH5:o,aliQR:p,wechatH5:T,wechatJsapi:l}=n.data;if(o){const _=document.createElement("div");_.innerHTML=o.html,document.body.appendChild(_),document.forms[0].submit()}p&&(window.location.href=p.payUrl),T&&(window.location.href=T.h5Url),l&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:l.appId,timeStamp:l.timeStamp,nonceStr:l.nonceStr,package:l.packageStr,signType:l.signType,paySign:l.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&y.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...se(a),loading:r,userInfo:f,active:L,guize:x,time:w,url:v,actions:z,show:A,showImg:M,qrCodeUrlImg:E,isEnd:U,Login:W,buy:B,Pay:k,testPlay:J,getQueryVariable:h,wxShare:I,link:V,showGuize:X,formatTime:F,onFinish:G,onSelect:j,isLimitComplete:O}}},$=s=>(re("data-v-0e3b9623"),s=s(),le(),s),fe={class:"box"},ve=["src"],ye={class:"last"},we=$(()=>m("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/9df67870-f539-11ed-80f5-c75471cc8620_\u6309\u94AE(1)(1).png",alt:""},null,-1)),he=[we],Ie={class:"wrapper"},_e=$(()=>m("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),be=["src"];function xe(s,r,L,x,w,v){const S=q("false-data"),d=q("van-action-sheet"),g=q("van-overlay");return ae(),ne(oe,null,[m("div",fe,[P(S),m("img",{class:"image",src:s.url,alt:""},null,8,ve),m("div",ye,[m("div",{class:"bottom",onClick:r[0]||(r[0]=(...u)=>s.buy&&s.buy(...u))},he)])]),P(d,{show:s.show,"onUpdate:show":r[1]||(r[1]=u=>s.show=u),actions:s.actions,onSelect:s.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),P(g,{show:s.showImg,onClick:r[3]||(r[3]=u=>s.showImg=!1)},{default:ie(()=>[m("div",Ie,[m("div",{onClick:r[2]||(r[2]=ce(()=>{},["stop"]))},[_e,m("img",{src:s.qrCodeUrlImg,alt:""},null,8,be)])])]),_:1},8,["show"])],64)}var qe=Y(ge,[["render",xe],["__scopeId","data-v-0e3b9623"]]);export{qe as default};
