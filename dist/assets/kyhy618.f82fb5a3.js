import{_ as se,u as te,a as ae,r as a,b as P,o as ne,T as g,t as oe,c as U,d as ie,e as ce,f as i,g as _,w as N,F as re,h as z,p as le,i as de}from"./index.8f4d7f86.js";import{s as p,C as pe}from"./js.cookie.ad72bcd1.js";import{f as me}from"./falseData.c0306b2a.js";import{m as ue}from"./msSwiper.7d61aaec.js";import{b as I}from"./config.3aca39f6.js";import{b as Q}from"./config618.d924d98a.js";const fe={name:"kyhy618",components:{falseData:me,msSwiper:ue},setup:()=>{te();const t=ae(),n=a(!1),q=a(!1),b=a(!1),L=a(new Date("2022/03/16 14:00:00").getTime()-new Date().getTime()),M=a(!1),l=a(""),m=a(""),y=a(""),d=a(""),S=a(!1),k=a(!1),A=a(!1),O=a(""),C=a("college"),T=a(!1),J=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],V=e=>{S.value=!1,D(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},c=P({info:{}}),F=P({msg:{}}),v=e=>{for(var s=window.location.search.substring(1),o=s.split("&"),r=0;r<o.length;r++){var u=o[r].split("=");if(u[0]==e)return u[1]}return!1};ne(async()=>{document.title="\u7231\u5B66618\uFF0C\u79D1\u7814\u4F1A\u5458\u798F\u5229\u8D2D\uFF01",$({title:"\u7231\u5B66618\uFF0C\u79D1\u7814\u4F1A\u5458\u798F\u5229\u8D2D\uFF01",summary:"\u73B0\u5728\u52A0\u5165\u4F1A\u5458\u8D60\u90013\u4E2A\u6708\u4F1A\u5458\u65F6\u957F\uFF01",thumb:"https://static.medsci.cn/public-image/ms-image/23dc0240-e661-11ec-a1b8-6123b3ff61ea_kyhy618_icon.png"});let e=navigator.userAgent;e!=null&&e.indexOf("MicroMessenger")>-1&&(t.query.openId||v("openId"))&&B()});const $=async e=>{let s=window.location.href.split("#")[0];const o=await p.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(o.data),wx.error(function(r){console.log(r)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kyhy618",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/kyhy618",imgUrl:e.thumb,success:function(){}})})},H=()=>{const e=navigator.userAgent,s=t.query.openId||v("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){l.value="WX";const o="wx9096048917ec59ab";if(s)d.value=s;else{const r=encodeURIComponent(window.location.href),u=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${o}?returnUrl=${r}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${o}&redirect_uri=${u}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?l.value="ALI":l.value="ALL"},B=async()=>{H();const e=pe.get("userInfo");if(g.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)c.info=JSON.parse(e),c.info.mobile||addLoginDom(),x();else{const s=t.query.sso_sessionid||v("sso_sessionid");if(s){const o=await p.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});c.info=o.data,x()}else R()}},R=()=>{addLoginDom()},X=e=>{e.link&&(window.location.href=e.link)},x=async()=>{const e=await p.post(Q+"/medsci-activity/pay/member-card",{user_id:c.info.plaintextUserId,ciphertext_user_id:c.info.userId,mobile:c.info.mobile,user_name:c.info.userName,real_name:c.info.realName,email:c.info.email,type:"scientific_card"});e.code!==200&&(g.clear(),g(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),m.value=e.data.data,g.clear(),Z()},j=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},G=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},K=()=>{b.value=!0},Y=()=>{T.value=!0},Z=async()=>{if(window.innerWidth<750)l.value=="ALL"?S.value=!0:D(l.value);else{const e=await p.post(I+"/payment/pay/merge_qrcode",{accessAppId:C.value,appOrderId:m.value}),{qrCodeUrl:s}=e.data;s&&(k.value=!0,O.value=s);const o=setInterval(()=>{ee(),y.value=="PAID"&&(k.value=!1,W(),clearInterval(o))},3e3)}M.value=!1},W=async()=>{const e=await p.post(Q+"/medsci-activity/pay/member-card-status",{order_id:m.value,type:"scientific_card"});console.log(e,"\u4E0B\u5355\u6210\u529F")},ee=async()=>{const e=await p.get(I+"/payment/pay/query",{params:{appOrderId:m.value}}),{paymentStatus:s}=e.data;y.value=s,s=="PAID"&&(A.value=!0,g("\u652F\u4ED8\u6210\u529F"))},D=async e=>{const s=await p.post(I+"/payment/pay/build",{accessAppId:C.value,appOrderId:m.value,payChannel:e,paySource:"MEDSCI_WEB",payType:l.value=="ALL"?"MWEB":l.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){g(s.msg);return}const o=await p.post(I+"/payment/pay/order",{accessAppId:C.value,payOrderId:s.data.payOrderId,openId:d.value}),{aliH5:r,aliQR:u,wechatH5:E,wechatJsapi:f}=o.data;if(r){const w=document.createElement("div");w.innerHTML=r.html,document.body.appendChild(w),document.forms[0].submit()}u&&(window.location.href=u.payUrl),E&&(window.location.href=E.h5Url),f&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:f.appId,timeStamp:f.timeStamp,nonceStr:f.nonceStr,package:f.packageStr,signType:f.signType,paySign:f.paySign},function(w){w.err_msg=="get_brand_wcpay_request:ok"&&(g.success("\u652F\u4ED8\u6210\u529F\uFF01"),A.value=!0)})};return{...oe(F),loading:n,userInfo:c,active:q,guize:b,time:L,actions:J,show:S,showImg:k,showQrcode:A,qrCodeUrlImg:O,isEnd:T,Login:R,buy:B,Pay:x,testPlay:X,getQueryVariable:v,wxShare:$,link:j,showGuize:K,formatTime:G,onFinish:Y,onSelect:V,memberCardStatus:W}}},h=t=>(le("data-v-2bf79ec0"),t=t(),de(),t),ge={class:"box"},ye=h(()=>i("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/39c88340-f5c4-11ec-a1b8-6123b3ff61ea_kyhy618.png",alt:""},null,-1)),he={class:"bottom"},ve=h(()=>i("div",{class:"left"},[i("img",{src:"https://static.medsci.cn/public-image/ms-image/162c6330-e6f0-11ec-a1b8-6123b3ff61ea_ky_left.png",alt:""})],-1)),we=h(()=>i("img",{src:"https://static.medsci.cn/public-image/ms-image/162c6330-e6f0-11ec-a1b8-6123b3ff61ea_ky_righ.png",alt:""},null,-1)),_e=[we],Ie={class:"wrapper"},be=h(()=>i("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),Se=["src"],ke=h(()=>i("img",{class:"img1",src:"https://static.medsci.cn/public-image/ms-image/cb18e310-e6f3-11ec-a1b8-6123b3ff61ea_kyhy618_code.png",alt:""},null,-1)),Ae=[ke];function Ce(t,n,q,b,L,M){const l=U("false-data"),m=U("van-action-sheet"),y=U("van-overlay");return ie(),ce(re,null,[i("div",ge,[_(l),ye,i("div",he,[ve,i("div",{class:"right",onClick:n[0]||(n[0]=(...d)=>t.buy&&t.buy(...d))},_e)])]),_(m,{show:t.show,"onUpdate:show":n[1]||(n[1]=d=>t.show=d),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),_(y,{show:t.showImg,onClick:n[3]||(n[3]=d=>t.showImg=!1)},{default:N(()=>[i("div",Ie,[i("div",{onClick:n[2]||(n[2]=z(()=>{},["stop"]))},[be,i("img",{src:t.qrCodeUrlImg,alt:""},null,8,Se)])])]),_:1},8,["show"]),_(y,{show:t.showQrcode,onClick:n[5]||(n[5]=d=>t.showQrcode=!1),center:""},{default:N(()=>[i("div",{class:"dialog-wrapper",onClick:n[4]||(n[4]=z(()=>{},["stop"]))},Ae)]),_:1},8,["show"])],64)}var Te=se(fe,[["render",Ce],["__scopeId","data-v-2bf79ec0"]]);export{Te as default};
