import{_ as Y,u as Z,a as ee,r as a,b as E,o as se,T as g,t as te,c as A,d as ae,e as ne,f as o,g as U,w as oe,F as ie,h as ce,p as re,i as le}from"./index.8f4d7f86.js";import{s as d,C as de}from"./js.cookie.ad72bcd1.js";import{f as pe}from"./falseData.c0306b2a.js";import{m as me}from"./msSwiper.7d61aaec.js";import{b as w}from"./config.3aca39f6.js";import{b as P}from"./config618.d924d98a.js";const ue={name:"hxk618",components:{falseData:pe,msSwiper:me},setup:()=>{Z();const t=ee(),i=a(!1),C=a(!1),I=a(!1),q=a(new Date("2022/03/16 14:00:00").getTime()-new Date().getTime()),L=a(!1),l=a(""),p=a(""),h=a(""),m=a(""),b=a(!1),S=a(!1),M=a(""),x=a("college"),O=a(!1),N=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],z=e=>{b.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},c=E({info:{}}),j=E({msg:{}}),v=e=>{for(var s=window.location.search.substring(1),n=s.split("&"),r=0;r<n.length;r++){var u=n[r].split("=");if(u[0]==e)return u[1]}return!1};se(async()=>{document.title="618\u8FD4\u573A\u8D2D\u547C\u5438\u79D1\u4F1A\u5458\uFF0C999\u5143\u518D\u8D602\u4E2A\u6708\u65F6\u957F\uFF01",T({title:"618\u8FD4\u573A\u8D2D\u547C\u5438\u79D1\u4F1A\u5458\uFF0C999\u5143\u518D\u8D602\u4E2A\u6708\u65F6\u957F\uFF01",summary:"\u4F1A\u5458\u8BFE\u8868\u5168\u9762\u5347\u7EA7\uFF0C\u52A0\u8BFE\u964D\u4EF7\uFF0C\u70B9\u51FB\u52A0\u5165~",thumb:"https://static.medsci.cn/public-image/ms-image/6fb5f180-f5c5-11ec-a1b8-6123b3ff61ea_hx_icon.jpg"});let e=navigator.userAgent;e!=null&&e.indexOf("MicroMessenger")>-1&&(t.query.openId||v("openId"))&&B()});const T=async e=>{let s=window.location.href.split("#")[0];const n=await d.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(n.data),wx.error(function(r){console.log(r)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/hxk618",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/hxk618",imgUrl:e.thumb,success:function(){}})})},J=()=>{const e=navigator.userAgent,s=t.query.openId||v("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){l.value="WX";const n="wx9096048917ec59ab";if(s)m.value=s;else{const r=encodeURIComponent(window.location.href),u=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${n}?returnUrl=${r}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${n}&redirect_uri=${u}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?l.value="ALI":l.value="ALL"},B=async()=>{J();const e=de.get("userInfo");if(g.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)c.info=JSON.parse(e),c.info.mobile||addLoginDom(),k();else{const s=t.query.sso_sessionid||v("sso_sessionid");if(s){const n=await d.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});c.info=n.data,k()}else R()}},R=()=>{addLoginDom()},V=e=>{e.link&&(window.location.href=e.link)},k=async()=>{const e=await d.post(P+"/medsci-activity/pay/member-card",{user_id:c.info.plaintextUserId,ciphertext_user_id:c.info.userId,mobile:c.info.mobile,user_name:c.info.userName,real_name:c.info.realName,email:c.info.email,type:"breathing_card"});e.code!==200&&(g.clear(),g(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),p.value=e.data.data,g.clear(),G()},F=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},H=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},X=()=>{I.value=!0},Q=()=>{O.value=!0},G=async()=>{if(window.innerWidth<750)l.value=="ALL"?b.value=!0:$(l.value);else{const e=await d.post(w+"/payment/pay/merge_qrcode",{accessAppId:x.value,appOrderId:p.value}),{qrCodeUrl:s}=e.data;s&&(S.value=!0,M.value=s);const n=setInterval(()=>{K(),h.value=="PAID"&&(S.value=!1,W(),clearInterval(n))},3e3)}L.value=!1},W=async()=>{const e=await d.post(P+"/medsci-activity/pay/member-card-status",{order_id:p.value,type:"breathing_card"});console.log(e,"\u4E0B\u5355\u6210\u529F")},K=async()=>{const e=await d.get(w+"/payment/pay/query",{params:{appOrderId:p.value}}),{paymentStatus:s}=e.data;h.value=s,s=="PAID"&&g("\u652F\u4ED8\u6210\u529F")},$=async e=>{const s=await d.post(w+"/payment/pay/build",{accessAppId:x.value,appOrderId:p.value,payChannel:e,paySource:"MEDSCI_WEB",payType:l.value=="ALL"?"MWEB":l.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){g(s.msg);return}const n=await d.post(w+"/payment/pay/order",{accessAppId:x.value,payOrderId:s.data.payOrderId,openId:m.value}),{aliH5:r,aliQR:u,wechatH5:D,wechatJsapi:f}=n.data;if(r){const y=document.createElement("div");y.innerHTML=r.html,document.body.appendChild(y),document.forms[0].submit()}u&&(window.location.href=u.payUrl),D&&(window.location.href=D.h5Url),f&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:f.appId,timeStamp:f.timeStamp,nonceStr:f.nonceStr,package:f.packageStr,signType:f.signType,paySign:f.paySign},function(y){y.err_msg=="get_brand_wcpay_request:ok"&&g.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...te(j),loading:i,userInfo:c,active:C,guize:I,time:q,actions:N,show:b,showImg:S,qrCodeUrlImg:M,isEnd:O,Login:R,buy:B,Pay:k,testPlay:V,getQueryVariable:v,wxShare:T,link:F,showGuize:X,formatTime:H,onFinish:Q,onSelect:z,memberCardStatus:W}}},_=t=>(re("data-v-c5737dce"),t=t(),le(),t),fe={class:"box"},ge=_(()=>o("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/39c88340-f5c4-11ec-a1b8-6123b3ff61ea_hxk618.jpg",alt:""},null,-1)),he={class:"bottom"},ve=_(()=>o("div",{class:"left"},[o("img",{src:"https://static.medsci.cn/public-image/ms-image/d63df9e0-f5be-11ec-a1b8-6123b3ff61ea_9992.png",alt:""})],-1)),ye=_(()=>o("img",{src:"https://static.medsci.cn/public-image/ms-image/162c6330-e6f0-11ec-a1b8-6123b3ff61ea_ky_righ.png",alt:""},null,-1)),we=[ye],_e={class:"wrapper"},Ie=_(()=>o("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),be=["src"];function Se(t,i,C,I,q,L){const l=A("false-data"),p=A("van-action-sheet"),h=A("van-overlay");return ae(),ne(ie,null,[o("div",fe,[U(l),ge,o("div",he,[ve,o("div",{class:"right",onClick:i[0]||(i[0]=(...m)=>t.buy&&t.buy(...m))},we)])]),U(p,{show:t.show,"onUpdate:show":i[1]||(i[1]=m=>t.show=m),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),U(h,{show:t.showImg,onClick:i[3]||(i[3]=m=>t.showImg=!1)},{default:oe(()=>[o("div",_e,[o("div",{onClick:i[2]||(i[2]=ce(()=>{},["stop"]))},[Ie,o("img",{src:t.qrCodeUrlImg,alt:""},null,8,be)])])]),_:1},8,["show"])],64)}var Le=Y(ue,[["render",Se],["__scopeId","data-v-c5737dce"]]);export{Le as default};
