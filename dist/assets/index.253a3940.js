import{_ as o,d as c,e,f as a,F as v,E as n,T as _,p as l,i as r,U as p}from"./index.8f4d7f86.js";const b={setup(){return{submit:()=>{_("\u5DF2\u5F00\u901A")}}}},m=s=>(l("data-v-6c48ab91"),s=s(),r(),s),u={class:"box"},f={class:"item-box"},h=p('<div class="title" data-v-6c48ab91>\u5546\u54C1\u4E00</div><div class="info" data-v-6c48ab91><img class="img" src="https://static.medsci.cn/public-image/ms-image/2a8fb480-3566-11ec-8e2f-1389d01aad85_head.jpg" alt="" data-v-6c48ab91><div class="cls" data-v-6c48ab91><div class="t" data-v-6c48ab91>\u5510\u4F1F\u7CBE\u54C1\u8BFE\uFF1A\u8111\u8840\u7BA1\u75C5\u5F71\u50CF\u4E0E\u4E34\u5E8A</div><div class="v" data-v-6c48ab91><div class="original" data-v-6c48ab91>\u539F\u4EF7\uFF1A499\u5143</div><div data-v-6c48ab91>\u6298\u540E\uFF1A119.4\u5143</div></div></div></div>',2),g=[h],x={class:"bottom"},k=m(()=>a("div",{class:"bottom-left"},[a("span",{class:"num"},"\u51714\u4EF6\uFF0C"),a("span",null,"\u5408\u8BA1\uFF1A477.6\u5143")],-1));function I(s,d,S,i,y,B){return c(),e("div",u,[a("div",f,[(c(),e(v,null,n(4,t=>a("div",{class:"item",key:t},g)),64))]),a("div",x,[k,a("div",{class:"bottom-right",onClick:d[0]||(d[0]=(...t)=>i.submit&&i.submit(...t))},"\u63D0\u4EA4\u8BA2\u5355")])])}var F=o(b,[["render",I],["__scopeId","data-v-6c48ab91"]]);export{F as default};
