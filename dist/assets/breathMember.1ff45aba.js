import{_ as Y,u as Z,a as ee,r as t,b as O,o as se,t as te,c as y,d as ne,e as oe,f as l,g as I,w as ae,F as ie,T as r,h as ce,p as re,i as le}from"./index.8f4d7f86.js";import{C as R,s as u}from"./js.cookie.ad72bcd1.js";import{f as de}from"./falseData.c0306b2a.js";import{m as pe}from"./msSwiper.7d61aaec.js";import{b as $}from"./config.3aca39f6.js";import{b as E}from"./configFreeVip.d32e6c36.js";import{m as me}from"./sanofiConfig.a80970f2.js";const ue={name:"openCourse",components:{falseData:de,msSwiper:pe},setup:()=>{Z();const n=ee(),i=t(!1),S=t("1"),x=t(""),U=t(!1),C=t(!1),g=t(!1),w=t(new Date("2032/02/20 23:59:59").getTime()-new Date().getTime());t(!1);const d=t(""),f=t("");t("");const k=t(""),A=t(!1),W=t(!1),N=t(""),T=t("college"),M=t(!1),P=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],V=e=>{A.value=!1,K(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},o=O({info:{}}),z=O({msg:{}}),v=e=>{for(var s=window.location.search.substring(1),a=s.split("&"),c=0;c<a.length;c++){var p=a[c].split("=");if(p[0]==e)return p[1]}return!1};se(async()=>{document.title="\u547C\u5438\u5B88\u62A4\u8005-\u5171\u6297\u6DF7\u5408\u611F\u67D3",q({title:"\u547C\u5438\u5B88\u62A4\u8005-\u5171\u6297\u6DF7\u5408\u611F\u67D3",summary:"\u9A70\u63F4\u4E34\u5E8A\u4E00\u7EBF\u516C\u76CA\u6D3B\u52A8\uFF0C\u6885\u65AF\u533B\u5B66\u547C\u5438\u79D1\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458\u514D\u8D39\u9001",thumb:"https://static.medsci.cn/public-image/ms-image/0c7ae180-94e7-11ee-926a-11c8565c20b2_share.png"}),R.get("userInfo"),u.get(E+"/medsci-activity/visit",{params:{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,event_type:"view",type:"new_user_register"}})});const q=async e=>{let s=window.location.href.split("#")[0];const a=await u.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(a.data),wx.error(function(c){console.log(c)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/breathMember",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/breathMember",imgUrl:e.thumb,success:function(){}})})},L=async()=>{(await u.get(me+"/perfectInfo/userInfoStatus?encryptionUserId="+o.info.userId)).data.isCompleteInfo?_():addPerfectInfoDom()},F=()=>{const e=navigator.userAgent,s=n.query.openId||v("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){d.value="WX";const a="wx9096048917ec59ab";if(s)k.value=s;else{const c=encodeURIComponent(window.location.href),p=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${c}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${p}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?d.value="ALI":d.value="ALL"},J=async()=>{F(),r.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"});const e=R.get("userInfo");if(e)o.info=JSON.parse(e),o.info.mobile?L():addLoginDom();else{const s=n.query.sso_sessionid||v("sso_sessionid");if(s){const a=await u.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});o.info=a.data,_()}else B()}},B=()=>{addLoginDom()},H=e=>{e.link&&(window.location.href=e.link)},_=async()=>{new Date().getTime(),new Date("2023/11/07 00:00:00").getTime(),new Date("2023/11/11 23:59:59").getTime();const e=await u.post(E+"/medsci-activity/give/member-card",{user_id:o.info.plaintextUserId,ciphertext_user_id:o.info.userId,mobile:o.info.mobile,user_name:o.info.userName,real_name:o.info.realName,type:"breathing_give"});e.code!==200&&e.code!==205&&(r.clear(),r(e.msg||"\u8BF7\u7A0D\u540E\u518D\u8BD5")),e.code==200&&(r.clear(),r("\u606D\u559C\u60A8\u5DF2\u6210\u529F\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0"),e.code==205&&(r.clear(),r("\u60A8\u5DF2\u7ECF\u9886\u53D6\u547C\u5438\u7CBE\u54C1\u8BFE\u5B63\u5EA6\u4F1A\u5458\uFF0C\u76F4\u63A5\u5B66\u4E60\u5373\u53EF"),window.location.href="https://class.medsci.cn/list/0-12-1-20408-0-0-0")},X=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},j=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},Q=()=>{g.value=!0},G=()=>{M.value=!0},K=async e=>{const s=await u.post($+"/payment/pay/build",{accessAppId:T.value,appOrderId:f.value,payChannel:e,paySource:"MEDSCI_WEB",payType:d.value=="ALL"?"MWEB":d.value=="WX"?"JSAPI":"NATIVE"});if(s.code!="SUCCESS"){r(s.msg);return}const a=await u.post($+"/payment/pay/order",{accessAppId:T.value,payOrderId:s.data.payOrderId,openId:k.value}),{aliH5:c,aliQR:p,wechatH5:D,wechatJsapi:m}=a.data;if(c){const h=document.createElement("div");h.innerHTML=c.html,document.body.appendChild(h),document.forms[0].submit()}p&&(window.location.href=p.payUrl),D&&(window.location.href=D.h5Url),m&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:m.appId,timeStamp:m.timeStamp,nonceStr:m.nonceStr,package:m.packageStr,signType:m.signType,paySign:m.paySign},function(h){h.err_msg=="get_brand_wcpay_request:ok"&&(r.success("\u652F\u4ED8\u6210\u529F\uFF01"),window.location.href="https://open.medsci.cn/")})};return{curVal:S,...te(z),loading:U,userInfo:o,active:C,guize:g,time:w,vipType:x,actions:P,show:A,showImg:W,qrCodeUrlImg:N,isEnd:M,timing:i,getBtn:J,Login:B,Pay:_,testPlay:H,getQueryVariable:v,wxShare:q,link:X,showGuize:Q,formatTime:j,onFinish:G,onSelect:V,isLimitComplete:L}}},b=n=>(re("data-v-7c3460a0"),n=n(),le(),n),fe={class:"box"},ge=b(()=>l("img",{class:"image",src:"https://static.medsci.cn/public-image/ms-image/3a5625d0-94e5-11ee-926a-11c8565c20b2_bcg.png",alt:""},null,-1)),he={class:"last"},we=b(()=>l("img",{src:"https://static.medsci.cn/public-image/ms-image/40ce8230-9572-11ee-926a-11c8565c20b2_btn.png",alt:""},null,-1)),ve=[we],_e=b(()=>l("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),ye=["src"];function Ie(n,i,S,x,U,C){const g=y("false-data"),w=y("van-action-sheet"),d=y("van-overlay");return ne(),oe(ie,null,[l("div",fe,[I(g),ge,l("div",he,[l("div",{class:"bottom",onClick:i[0]||(i[0]=(...f)=>n.getBtn&&n.getBtn(...f))},ve)])]),I(w,{show:n.show,"onUpdate:show":i[1]||(i[1]=f=>n.show=f),actions:n.actions,onSelect:n.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),I(d,{show:n.showImg,onClick:i[3]||(i[3]=f=>n.showImg=!1)},{default:ae(()=>[l("div",{class:"wrapper",onClick:i[2]||(i[2]=ce(()=>{},["stop"]))},[l("div",null,[_e,l("img",{src:n.qrCodeUrlImg,alt:""},null,8,ye)])])]),_:1},8,["show"])],64)}var Te=Y(ue,[["render",Ie],["__scopeId","data-v-7c3460a0"]]);export{Te as default};
