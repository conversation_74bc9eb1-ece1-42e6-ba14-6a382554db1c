import{_ as Y,u as Z,a as ee,r as n,b as R,o as se,T as y,t as te,c as U,d as M,e as E,f as m,g as L,w as ae,F as ne,h as oe,p as ie,i as ce}from"./index.8f4d7f86.js";import{s as p,C as re}from"./js.cookie.ad72bcd1.js";import{f as le}from"./falseData.c0306b2a.js";import{m as de}from"./msSwiper.7d61aaec.js";import{b}from"./config.3aca39f6.js";const pe={name:"Home",components:{falseData:le,msSwiper:de},setup:()=>{Z();const t=ee(),r=n(!1),O=n(!1),S=n(!1),v=n(new Date("2022/02/20 23:59:59").getTime()-new Date().getTime()),w=n(""),A=n(!1),c=n(""),u=n(""),g=n(""),P=n(""),x=n(!1),D=n(!1),W=n(""),h=n("college"),k=n(!1),j=[{name:"\u652F\u4ED8\u5B9D\u652F\u4ED8"},{name:"\u5FAE\u4FE1\u652F\u4ED8"}],z=e=>{x.value=!1,$(e.name=="\u652F\u4ED8\u5B9D\u652F\u4ED8"?"ALI":"WX")},f=R({info:{}}),o=R({msg:{}}),I=e=>{for(var s=window.location.search.substring(1),a=s.split("&"),i=0;i<a.length;i++){var d=a[i].split("=");if(d[0]==e)return d[1]}return!1};se(async()=>{let e;t.query.day==30?(document.title="\u79D1\u7814\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",T({title:"\u79D1\u7814\u7CBE\u54C1\u8BFE\u6708\u5EA6\u4F1A\u5458\u5361",summary:"52\u4E2A\u4E13\u8F91\u8BFE\uFF0C\u8F7B\u677E\u53D1\u8868SCI\u62FF\u4E0B\u56FD\u81EA\u7136",thumb:"https://static.medsci.cn/public-image/ms-image/c6996440-8d34-11ec-bca5-7f892b5df5d6_study_enjoy.png"}),w.value="https://static.medsci.cn/public-image/ms-image/bf4063c0-f088-11ed-9b52-b908d10125b2_\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_16838704104457.png",e="****************"):(document.title="\u6885\u65AF\u79D1\u7814\u7CBE\u54C1\u8BFE\u4F1A\u5458\u5E74\u5361",T({title:"\u6885\u65AF\u79D1\u7814\u7CBE\u54C1\u8BFE\u4F1A\u5458\u5E74\u5361",summary:"\u70B9\u51FB\u7ACB\u5373\u5F00\u901A",thumb:"https://static.medsci.cn/public-image/ms-image/c6996440-8d34-11ec-bca5-7f892b5df5d6_study_enjoy.png"}),w.value="https://static.medsci.cn/public-image/ms-image/\u4F01\u4E1A\u5FAE\u4FE1\u622A\u56FE_17530896452190(1).jpg",e="****************");const s=await p.post("/activity/memberCardDetail",{id:e});o.msg=s.data,o.msg&&o.msg.activityEndTime&&(o.msg.activityEndTime=o.msg.activityEndTime.replace(/-/g,"/"),v.value=new Date(o.msg.activityEndTime).getTime()-new Date().getTime()),v.value<0&&(k.value=!0);let a=navigator.userAgent;a!=null&&a.indexOf("MicroMessenger")>-1&&(t.query.openId||I("openId"))&&B()});const T=async e=>{let s=window.location.href.split("#")[0];const a=await p.get("https://ypxcx.medsci.cn/ean/share",{params:{url:s}});wx.config(a.data),wx.error(function(i){console.log(i)}),wx.ready(function(){wx.onMenuShareAppMessage({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/",imgUrl:e.thumb,success:function(){}}),wx.onMenuShareTimeline({title:e.title,desc:e.summary,link:"https://static.medsci.cn/product/medsci/active/zen0214/index.html#/",imgUrl:e.thumb,success:function(){}})})},J=()=>{const e=navigator.userAgent,s=t.query.openId||I("openId");if(e!=null&&e.indexOf("MicroMessenger")>-1){c.value="WX";const a="wx9096048917ec59ab";if(s)P.value=s;else{const i=encodeURIComponent(window.location.href),d=encodeURIComponent(`http://api.center.medsci.cn/api/wechat/access-token/${a}?returnUrl=${i}`);window.location.href=`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${a}&redirect_uri=${d}&response_type=code&scope=snsapi_base&state=1#wechat_redirect`}}else e!=null&&e.indexOf("AlipayClient")>-1?c.value="ALI":c.value="ALL"},B=async()=>{J();const e=re.get("userInfo");if(y.loading({duration:0,message:"\u63D0\u4EA4\u4E2D...",loadingType:"spinner"}),e)f.info=JSON.parse(e),f.info.mobile||addLoginDom(),C();else{const s=t.query.sso_sessionid||I("sso_sessionid");if(s){const a=await p.post("/medsciUser/getLoginUserInfoBySid",{sessionid:s});f.info=a.data,C()}else N()}},N=()=>{addLoginDom()},V=e=>{e.link&&(window.location.href=e.link)},C=async()=>{const{userId:e,userName:s,realName:a,mobile:i,email:d,plaintextUserId:q}=f.info,l=await p.post("/activity/createOrder",{itemId:o.msg.id,itemNum:1,itemPicPath:o.msg.cardImage,itemTitle:o.msg.cardName,itemPrice:o.msg.activityPrice,projectId:o.msg.projectId,orderType:1,mobile:i,payment:0,userId:e,nikeName:s,buyerMessage:"\u79D1\u7814\u5E74\u5EA6\u4F1A\u5458-\u56FD\u81EA\u7136\u57FA\u91D1\u6D3B\u52A8\u9875"});u.value=l.data,y.clear(),G()},H=()=>{var e=navigator.userAgent;const s=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;window.location.href="https://a.app.qq.com/o/simple.jsp?pkgname=cn.medsci.app.news"},X=e=>{if(e>=10){const s=String(e);return[s[0],s[1]]}else return[0,e]},F=()=>{S.value=!0},Q=()=>{k.value=!0},G=async()=>{if(window.innerWidth<750)c.value=="ALL"?x.value=!0:$(c.value);else{const e=await p.post(b+"/payment/pay/merge_qrcode",{accessAppId:h.value,appOrderId:u.value}),{qrCodeUrl:s}=e.data;s&&(D.value=!0,W.value=s);const a=setInterval(()=>{K(),g.value=="PAID"&&clearInterval(a)},3e3)}A.value=!1},K=async()=>{const e=await p.get(b+"/payment/pay/query",{params:{appOrderId:u.value}}),{paymentStatus:s}=e.data;g.value=s,s=="PAID"&&y("\u652F\u4ED8\u6210\u529F")},$=async e=>{const s=await p.post(b+"/payment/pay/build",{accessAppId:h.value,appOrderId:u.value,payChannel:e,paySource:"MEDSCI_WEB",payType:c.value=="ALL"?"MWEB":c.value=="WX"?"JSAPI":"NATIVE"});if(console.log({accessAppId:h.value,appOrderId:u.value,payChannel:e,paySource:"MEDSCI_WEB",payType:c.value=="ALL"?"MWEB":c.value=="WX"?"JSAPI":"NATIVE"},"xxx"),s.code!="SUCCESS"){y(s.msg);return}const a=await p.post(b+"/payment/pay/order",{accessAppId:h.value,payOrderId:s.data.payOrderId,openId:P.value}),{aliH5:i,aliQR:d,wechatH5:q,wechatJsapi:l}=a.data;if(i){const _=document.createElement("div");_.innerHTML=i.html,document.body.appendChild(_),document.forms[0].submit()}d&&(window.location.href=d.payUrl),q&&(window.location.href=q.h5Url),l&&WeixinJSBridge.invoke("getBrandWCPayRequest",{appId:l.appId,timeStamp:l.timeStamp,nonceStr:l.nonceStr,package:l.packageStr,signType:l.signType,paySign:l.paySign},function(_){_.err_msg=="get_brand_wcpay_request:ok"&&y.success("\u652F\u4ED8\u6210\u529F\uFF01")})};return{...te(o),loading:r,userInfo:f,active:O,guize:S,time:v,url:w,actions:j,show:x,showImg:D,qrCodeUrlImg:W,isEnd:k,Login:N,buy:B,Pay:C,testPlay:V,getQueryVariable:I,wxShare:T,link:H,showGuize:F,formatTime:X,onFinish:Q,onSelect:z}}},me=t=>(ie("data-v-1a1300db"),t=t(),ce(),t),ue={class:"box"},ge=["src"],fe={class:"last"},ye={key:0,class:"image",src:"https://static.medsci.cn/public-image/ms-image/869e7b40-ca24-11ed-8dcb-15ac2b21d063_\u6309\u94AE.png",alt:""},ve={key:1,class:"image",src:"https://static.medsci.cn/public-image/ms-image/202404161117_\u6885\u65AF\u79D1\u7814\u7CBE\u54C1\u8BFE\u4F1A\u5458\u5E74\u5361\u6309\u94AEnew2.png",alt:""},we=me(()=>m("div",{class:"text"},"\u626B\u7801\u5B8C\u6210\u652F\u4ED8",-1)),he=["src"];function Ie(t,r,O,S,v,w){const A=U("false-data"),c=U("van-action-sheet"),u=U("van-overlay");return M(),E(ne,null,[m("div",ue,[L(A),m("img",{class:"image",src:t.url,alt:""},null,8,ge),m("div",fe,[m("div",{class:"bottom",onClick:r[0]||(r[0]=(...g)=>t.buy&&t.buy(...g))},[t.$route.query.day==30?(M(),E("img",ye)):(M(),E("img",ve))])])]),L(c,{show:t.show,"onUpdate:show":r[1]||(r[1]=g=>t.show=g),actions:t.actions,onSelect:t.onSelect,"cancel-text":"\u53D6\u6D88","close-on-click-action":""},null,8,["show","actions","onSelect"]),L(u,{show:t.showImg,onClick:r[3]||(r[3]=g=>t.showImg=!1)},{default:ae(()=>[m("div",{class:"wrapper",onClick:r[2]||(r[2]=oe(()=>{},["stop"]))},[m("div",null,[we,m("img",{src:t.qrCodeUrlImg,alt:""},null,8,he)])])]),_:1},8,["show"])],64)}var ke=Y(pe,[["render",Ie],["__scopeId","data-v-1a1300db"]]);export{ke as default};
