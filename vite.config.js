import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  base:"./",//打包路径
  resolve: {
    alias:{
      '@': path.resolve(__dirname, './src') //设置别名
    }
  },
  server: {
    port: 3000,//启动端口
    // open: true,
    hmr:true,
    host: '0.0.0.0',
    proxy: {
      // 选项写法
      "/medsciActivity": {
        target: "https://api.medon.com.cn/api/pm/open",
        changeOrigin: true,
        pathRewrite: {
          "^/medsciActivity": ""
        }
      },
      "/openOrder": {
        target: "http://***************:9999/api/pm/open",
        changeOrigin: true,
        pathRewrite: {
          "^/openOrder": ""
        }
      },
      "/activityNew": {
        target: "http://***************:9999/api/mg/user",
        changeOrigin: true,
        pathRewrite: {
          "^/activityNew": ""
        }
      },
      "/medsci-activity": {
        ws: false,
        target: "http://www.medsci-activity.com", //本地
        // target: "https://activity.medon.com.cn", //测试
        changeOrigin: true,
        pathRewrite: {
          "^/medsci-activity": ""
        }
      },
      '/py': 'http://out.s.medsci.cn/',//代理网址
      "/medsciUser": {
        ws: false,
        target: "http://***************:9999/api/auth/",
        changeOrigin: true,
        pathRewrite: {
          "^/medsciUser": ""
        }
      },
      "/payment": {
        ws: false,
        target: "https://mid.medon.com.cn/",
        changeOrigin: true,
        pathRewrite: {
          "^/payment": ""
        }
      },
      "/activity": {
        ws: false,
        target: "http://customer.medsci.cn/",
        changeOrigin: true,
        pathRewrite: {
          "^/activity": ""
        }
      },
      "/user": {
        ws: true,
        // target: "http://***************:9999/api/edu",
        target: "https://micro-app.medon.com.cn/vip",
        changeOrigin: true,
        pathRewrite: {
          "^/user": "",
        },
      }
    },
    cors:true
  }
})
